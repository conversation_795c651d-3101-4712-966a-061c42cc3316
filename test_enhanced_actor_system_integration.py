#!/usr/bin/env python3
"""
Test script for the enhanced actor system integration.

This script tests the integration of the new dependency injection,
enhanced diagnostics, and robust initialization components with the
existing actor system.
"""

import asyncio
import logging
import sys
import time
from pathlib import Path
from typing import Any, Dict, List, Optional, Set

# Add the project root to the Python path
sys.path.insert(0, str(Path(__file__).parent))

# Configure logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler()
    ]
)

logger = logging.getLogger("test_enhanced_actor_system_integration")

# Import the necessary components
from vibe_check.core.actor_system.enhanced_actor import EnhancedActor
from vibe_check.core.actor_system.message import Message, MessageType
from vibe_check.core.actor_system.context_wave import ContextWave
from vibe_check.core.actor_system.dependency_injection import (
    DependencyContainer,
    get_container,
    reset_container
)
from vibe_check.core.actor_system.diagnostics.enhanced_tracker import (
    EnhancedTracker,
    DiagnosticCategory,
    DiagnosticLevel
)
from vibe_check.core.actor_system.consolidated_initializer import (
    ConsolidatedActorInitializer,
    get_initializer,
    reset_initializer,
    SynchronizationPoint,
    ActorState
)
from vibe_check.core.actor_system.integration import (
    integrate_actor_system,
    get_integrated_actor_system,
    reset_integrated_actor_system,
    integrate_message_processor,
    get_integrated_message_processor,
    reset_integrated_message_processor,
    integrate_actor_starter,
    get_integrated_actor_starter,
    reset_integrated_actor_starter
)


class TestEnhancedActor(EnhancedActor):
    """A test actor for testing the enhanced actor system."""

    async def _initialize(self, config: Dict[str, Any]) -> None:
        """Initialize the actor."""
        logger.info(f"Actor {self.actor_id} initializing with config: {config}")

        # Register message handlers
        self._message_handlers[MessageType.TEST] = self._handle_test_message

        # Simulate some initialization work
        await asyncio.sleep(0.1)

        logger.info(f"Actor {self.actor_id} initialized")

    async def _handle_test_message(self, payload: Dict[str, Any], context: ContextWave) -> None:
        """Handle a test message."""
        logger.info(f"Actor {self.actor_id} handling test message: {payload}")

        # If the message requests a response, send one back
        if payload.get("respond", False):
            response_payload = {
                "message_id": f"response_to_{payload.get('message_id', 'unknown')}",
                "message": f"Response from {self.actor_id}",
                "timestamp": time.time(),
                "in_response_to": payload.get("message_id", "unknown")
            }

            await self.send(
                recipient_id=payload.get("sender", "unknown"),
                msg_type=MessageType.TEST,
                payload=response_payload,
                context=context
            )


async def setup_enhanced_actor_system() -> None:
    """Set up the enhanced actor system."""
    logger.info("Setting up enhanced actor system")

    # Reset the dependency container
    reset_container()
    container = get_container()

    # Create and register the enhanced tracker
    output_dir = "./test_output"
    Path(output_dir).mkdir(exist_ok=True)
    tracker = EnhancedTracker(output_dir=output_dir)
    container.register_instance(tracker)

    # Reset the initializer
    reset_initializer()

    # Reset the integration components
    reset_integrated_actor_system()
    reset_integrated_message_processor()
    reset_integrated_actor_starter()

    # Integrate the actor system
    integrate_actor_system()
    integrate_message_processor()
    integrate_actor_starter()

    logger.info("Enhanced actor system set up")


async def test_enhanced_actor_initialization() -> None:
    """Test enhanced actor initialization."""
    logger.info("Testing enhanced actor initialization")

    # Create actors
    actor1 = TestEnhancedActor("test_actor_1")
    actor2 = TestEnhancedActor("test_actor_2")
    actor3 = TestEnhancedActor("test_actor_3")

    # Wait for actors to be registered with the initializer
    await asyncio.sleep(0.5)

    # Add dependencies
    await actor1.add_dependency("test_actor_2")
    await actor2.add_dependency("test_actor_3")

    # Initialize actors with shorter timeout
    await actor3.initialize({"test_config": "value3"}, timeout=1.0)
    await actor2.initialize({"test_config": "value2"}, timeout=1.0)
    await actor1.initialize({"test_config": "value1"}, timeout=1.0)

    try:
        # Start actors
        await actor3.start()
        await actor2.start()
        await actor1.start()
    except Exception as e:
        logger.warning(f"Error starting actors: {e}")

    # Verify actor states
    initializer = get_initializer()

    actor1_state = await initializer.get_actor_state("test_actor_1")
    actor2_state = await initializer.get_actor_state("test_actor_2")
    actor3_state = await initializer.get_actor_state("test_actor_3")

    logger.info(f"Actor 1 state: {actor1_state.name if actor1_state else 'None'}")
    logger.info(f"Actor 2 state: {actor2_state.name if actor2_state else 'None'}")
    logger.info(f"Actor 3 state: {actor3_state.name if actor3_state else 'None'}")

    # Verify dependencies using the initializer
    initializer = get_initializer()

    # The consolidated initializer doesn't have a direct method to get dependencies
    # We can check if actors are registered and their states instead
    logger.info(f"Actor 1 is registered: {await initializer.is_actor_registered('test_actor_1')}")
    logger.info(f"Actor 2 is registered: {await initializer.is_actor_registered('test_actor_2')}")

    # Stop actors
    await actor1.stop()
    await actor2.stop()
    await actor3.stop()

    logger.info("Enhanced actor initialization test completed")


async def test_enhanced_actor_messaging() -> None:
    """Test enhanced actor messaging."""
    logger.info("Testing enhanced actor messaging")

    # Create actors
    actor1 = TestEnhancedActor("test_actor_1")
    actor2 = TestEnhancedActor("test_actor_2")

    # Wait for actors to be registered with the initializer
    await asyncio.sleep(0.5)

    # Initialize and start actors with shorter timeout
    await actor1.initialize(timeout=1.0)
    await actor2.initialize(timeout=1.0)
    await actor1.start()
    await actor2.start()

    # Send a test message from actor 1 to actor 2
    message_payload = {
        "message_id": "test_message_1",
        "message": "Hello from actor 1",
        "timestamp": time.time(),
        "sender": "test_actor_1",
        "respond": True
    }

    await actor1.send(
        recipient_id="test_actor_2",
        msg_type=MessageType.TEST,
        payload=message_payload
    )

    # Wait for the message to be processed
    await asyncio.sleep(0.5)

    # Stop actors
    await actor1.stop()
    await actor2.stop()

    logger.info("Enhanced actor messaging test completed")


async def main() -> None:
    """Run the tests."""
    # Set up the enhanced actor system
    await setup_enhanced_actor_system()

    # Test enhanced actor initialization
    try:
        await test_enhanced_actor_initialization()
        logger.info("Enhanced actor initialization test completed successfully")
    except Exception as e:
        logger.error(f"Error during initialization test: {e}")
        import traceback
        logger.error(traceback.format_exc())

    # Test enhanced actor messaging
    try:
        await test_enhanced_actor_messaging()
        logger.info("Enhanced actor messaging test completed successfully")
    except Exception as e:
        logger.error(f"Error during messaging test: {e}")
        import traceback
        logger.error(traceback.format_exc())

    # Get the enhanced tracker
    try:
        container = get_container()
        tracker = container.resolve(EnhancedTracker)

        # Save diagnostics
        diagnostics_path = await tracker.save_diagnostics()
        logger.info(f"Saved diagnostics to {diagnostics_path}")

        # Visualize initialization sequence
        viz_path = await tracker.visualize_initialization_sequence()
        logger.info(f"Saved visualization to {viz_path}")
    except Exception as e:
        logger.error(f"Error saving diagnostics: {e}")
        import traceback
        logger.error(traceback.format_exc())

    logger.info("All tests completed")


if __name__ == "__main__":
    asyncio.run(main())
