#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to run the Adaptive Execution Mode component tests.

This script runs the test suite for the Adaptive Execution Mode component,
including unit tests for individual components and integration tests for
the complete system.
"""

import asyncio
import logging
import os
import sys
import pytest
import argparse
from pathlib import Path
from typing import List, Optional, Union, Any, Dict, Set, Tuple, Callable

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('adaptive_execution_tests.log')
    ]
)

logger = logging.getLogger("adaptive_execution_tests")


def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description="Run Adaptive Execution Mode component tests")
    
    parser.add_argument(
        "--unit-tests-only",
        action="store_true",
        help="Run only unit tests"
    )
    
    parser.add_argument(
        "--integration-tests-only",
        action="store_true",
        help="Run only integration tests"
    )
    
    parser.add_argument(
        "--verbose",
        "-v",
        action="store_true",
        help="Enable verbose output"
    )
    
    parser.add_argument(
        "--test-files",
        nargs="+",
        help="Specific test files to run"
    )
    
    return parser.parse_args()


def run_tests(unit_tests_only: bool = False, integration_tests_only: bool = False,
              verbose: bool = False, test_files: Optional[List[str]] = None) -> bool:
    """
    Run the Adaptive Execution Mode component tests.
    
    Args:
        unit_tests_only: Run only unit tests
        integration_tests_only: Run only integration tests
        verbose: Enable verbose output
        test_files: Specific test files to run
        
    Returns:
        True if all tests passed, False otherwise
    """
    logger.info("Running Adaptive Execution Mode component tests")
    
    # Prepare pytest arguments
    pytest_args = []
    
    # Add verbosity
    if verbose:
        pytest_args.append("-v")
    
    # Add test files
    if test_files:
        pytest_args.extend(test_files)
    else:
        # Determine which tests to run
        if unit_tests_only and not integration_tests_only:
            # Run only unit tests
            pytest_args.extend([
                "tests/core/actor_system/execution/test_execution_mode_manager.py",
                "tests/core/actor_system/execution/test_workload_monitor.py",
                "tests/core/actor_system/execution/test_resource_monitor.py",
                "tests/core/actor_system/execution/test_policy_manager.py",
                "tests/core/actor_system/execution/test_integration.py"
            ])
        elif integration_tests_only and not unit_tests_only:
            # Run only integration tests
            pytest_args.extend([
                "tests/core/actor_system/execution/test_adaptive_execution.py",
                "tests/core/actor_system/execution/test_actor_system_integration.py"
            ])
        else:
            # Run all tests
            pytest_args.extend([
                "tests/core/actor_system/execution/test_execution_mode_manager.py",
                "tests/core/actor_system/execution/test_workload_monitor.py",
                "tests/core/actor_system/execution/test_resource_monitor.py",
                "tests/core/actor_system/execution/test_policy_manager.py",
                "tests/core/actor_system/execution/test_integration.py",
                "tests/core/actor_system/execution/test_adaptive_execution.py",
                "tests/core/actor_system/execution/test_actor_system_integration.py"
            ])
    
    # Run the tests
    result = pytest.main(pytest_args)
    
    # Check the result
    if result == 0:
        logger.info("All tests passed!")
        return True
    else:
        logger.error(f"Tests failed with code {result}")
        return False


def main():
    """Main function."""
    args = parse_args()
    
    success = run_tests(
        unit_tests_only=args.unit_tests_only,
        integration_tests_only=args.integration_tests_only,
        verbose=args.verbose,
        test_files=args.test_files
    )
    
    return 0 if success else 1


if __name__ == "__main__":
    sys.exit(main())
