#!/usr/bin/env python3
"""
Enhanced Simple Analyzer

This script analyzes a Python project using the simple analyzer with enhanced output.
It provides a comprehensive report of the analysis results and handles errors gracefully.
"""

import asyncio
import logging
import os
import sys
import time
from datetime import datetime
from pathlib import Path
import json

# Add the project root to the Python path
sys.path.append(str(Path(__file__).parent))

# Configure logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler("simple_analysis.log")
    ]
)
logger = logging.getLogger("enhanced_simple_analyzer")

# Import analyzer components
from vibe_check.core.simple_analyzer import simple_analyze_project
from vibe_check.core.models.project_metrics import ProjectMetrics


def analyze_project(project_path: str, output_dir: str) -> ProjectMetrics:
    """
    Analyze a project using the simple analyzer.

    Args:
        project_path: Path to the project to analyze
        output_dir: Directory to write output files to

    Returns:
        ProjectMetrics object with analysis results
    """
    logger.info(f"Starting analysis of project: {project_path}")
    logger.info(f"Output directory: {output_dir}")

    try:
        # Configure the analysis
        config = {
            "file_extensions": [".py"],
            "exclude_patterns": ["**/venv/**", "**/.git/**", "**/__pycache__/**"],
            "tools": {
                "ruff": {
                    "enabled": True,
                    "args": ["--select=E,F,W,I"]
                },
                "mypy": {
                    "enabled": True,
                    "args": ["--ignore-missing-imports"]
                },
                "complexity": {
                    "enabled": True,
                    "threshold": 10
                }
            },
            "report": {
                "formats": ["html", "json", "markdown"],
                "include_visualizations": True
            }
        }

        # Run the analysis
        logger.info("Running analysis")
        start_time = time.time()
        result = simple_analyze_project(
            project_path=project_path,
            output_dir=output_dir,
            config=config
        )
        end_time = time.time()
        logger.info(f"Analysis completed in {end_time - start_time:.2f} seconds")

        # Print summary
        logger.info(f"Analysis summary:")
        logger.info(f"  Total files: {len(result.files)}")
        logger.info(f"  Total directories: {len(result.directories)}")
        logger.info(f"  Total issues: {result._issue_count}")

        # Generate detailed report
        generate_detailed_report(result, output_dir)

        return result

    except Exception as e:
        logger.error(f"Error analyzing project: {e}")
        import traceback
        logger.error(traceback.format_exc())
        raise


def generate_detailed_report(metrics: ProjectMetrics, output_dir: str) -> None:
    """
    Generate a detailed report of the analysis results.

    Args:
        metrics: ProjectMetrics object with analysis results
        output_dir: Directory to write output files to
    """
    logger.info("Generating detailed report")

    # Create output directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)

    # Generate JSON report
    json_path = os.path.join(output_dir, "report.json")
    try:
        with open(json_path, 'w') as f:
            # Convert metrics to a dictionary
            metrics_dict = {
                "project_path": metrics.project_path,
                "files": {k: vars(v) for k, v in metrics.files.items()},
                "directories": {k: vars(v) for k, v in metrics.directories.items()},
                "issue_count": metrics._issue_count,
                "max_complexity": metrics._max_complexity
            }
            json.dump(metrics_dict, f, indent=2, default=str)
        logger.info(f"JSON report saved to {json_path}")
    except Exception as e:
        logger.error(f"Error generating JSON report: {e}")

    # Generate Markdown report
    md_path = os.path.join(output_dir, "report.md")
    try:
        with open(md_path, 'w') as f:
            f.write(f"# Project Analysis Report\n\n")
            f.write(f"**Project:** {metrics.project_path}\n\n")
            f.write(f"**Date:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            
            f.write(f"## Summary\n\n")
            f.write(f"- **Total Files:** {len(metrics.files)}\n")
            f.write(f"- **Total Directories:** {len(metrics.directories)}\n")
            f.write(f"- **Total Issues:** {metrics._issue_count}\n")
            f.write(f"- **Max Complexity:** {metrics._max_complexity}\n\n")
            
            f.write(f"## Files\n\n")
            f.write(f"| File | Lines | Issues | Complexity |\n")
            f.write(f"|------|-------|--------|------------|\n")
            for file_path, file_metrics in metrics.files.items():
                f.write(f"| {file_path} | {file_metrics.lines} | {len(file_metrics.issues)} | {file_metrics.complexity} |\n")
            
            f.write(f"\n## Issues\n\n")
            for file_path, file_metrics in metrics.files.items():
                if file_metrics.issues:
                    f.write(f"### {file_path}\n\n")
                    f.write(f"| Line | Column | Type | Message |\n")
                    f.write(f"|------|--------|------|--------|\n")
                    for issue in file_metrics.issues:
                        f.write(f"| {issue.line} | {issue.column} | {issue.type} | {issue.message} |\n")
                    f.write(f"\n")
        logger.info(f"Markdown report saved to {md_path}")
    except Exception as e:
        logger.error(f"Error generating Markdown report: {e}")

    logger.info("Detailed report generation completed")


def main() -> None:
    """Main function."""
    try:
        # Get the project to analyze
        if len(sys.argv) > 1:
            project_path = sys.argv[1]
        else:
            # Default to analyzing a small test project
            project_path = os.path.join(os.path.dirname(__file__), "test_project_new")

        # Create an output directory with timestamp
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_dir = Path(f'./analysis_results/report_{timestamp}')
        output_dir.mkdir(parents=True, exist_ok=True)

        logger.info(f"Starting analysis of {project_path}")
        logger.info(f"Results will be saved to {output_dir}")

        # Run the analysis
        metrics = analyze_project(project_path, str(output_dir))

        logger.info("Analysis completed successfully")
        logger.info(f"Results saved to {output_dir}")

    except Exception as e:
        logger.error(f"Analysis failed: {e}")
        import traceback
        logger.error(traceback.format_exc())
        sys.exit(1)


if __name__ == "__main__":
    main()
