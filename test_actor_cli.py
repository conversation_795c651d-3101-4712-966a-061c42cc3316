"""
Test script for the CLI with the actor system.
"""

import sys
import os
import logging
from pathlib import Path

# Configure logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler(sys.stdout)]
)
logger = logging.getLogger("test_actor_cli")

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath("."))

# Import the CLI
from vibe_check.cli.commands import analyze_command
from vibe_check.cli.error_handler import handle_analysis_error

# Run the CLI with the actor system
logger.info("Running analyze_command with actor system")
try:
    results = analyze_command(
        project_path="./test_project",
        config_path=None,
        output_dir="./test_results_actor",
        verbose=True,
        quiet=False,
        config_override={"preset": "minimal"},
        analyze_trends=False,
        report_progress=False,
        use_simple_analyzer=False,
        debug=True,
        debug_actor_system=True
    )
    logger.info("analyze_command completed")
except Exception as e:
    logger.error(f"Error in analyze_command: {e}")
    import traceback
    logger.error(traceback.format_exc())
    sys.exit(1)

# Print the results
print(f"Results: {results}")

# Check if there was an error
if isinstance(results, dict) and "error" in results:
    print(f"Error detected: {results['error']}")
    handle_analysis_error(results)
else:
    # Format and display the results
    print("=== Analysis Summary ===")
    print(f"Total Files: {results.get('total_file_count', 'Not available')}")
    print(f"Total Lines: {results.get('total_line_count', 'Not available')}")
    print(f"Average Complexity: {results.get('avg_complexity', 'Not available')}")
    print(f"Total Issues: {results.get('issue_count', 'Not available')}")
