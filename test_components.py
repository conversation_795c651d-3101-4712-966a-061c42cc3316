"""
Test script for the actor system components.

This script creates an actor system with our enhanced components and verifies
that they work correctly.
"""

import asyncio
import logging
import sys
from pathlib import Path

from vibe_check.core.actor_system.actor_system import ActorSystem
from vibe_check.core.actor_system.actor import Actor
from vibe_check.core.actor_system.message import Message, MessageType
from vibe_check.core.actor_system.components import (
    SupervisorComponent, StreamManager, StateManager, MetricsCollector
)


class TestActor(Actor):
    """Test actor for demonstrating the actor system components."""

    def __init__(self, actor_id: str) -> None:
        super().__init__(actor_id=actor_id)
        self.received_messages = []

    async def receive(self, message: Message) -> None:
        """Handle received messages."""
        print(f"Actor {self.actor_id} received message: {message.type}")
        self.received_messages.append(message)

        # Echo the message back to the sender
        if message.type == MessageType.CUSTOM:
            response = Message(
                type=MessageType.CUSTOM,
                payload={"response": f"Echo from {self.actor_id}"},
                sender_id=self.actor_id,
                recipient_id=message.sender_id
            )
            # We'll let the system handle this in the test function


async def test_components() -> None:
    """Test the actor system components."""
    # Configure logging
    logging.basicConfig(
        level=logging.DEBUG,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[logging.StreamHandler(sys.stdout)]
    )

    # Create an actor system
    system = ActorSystem()

    try:
        # Start the actor system
        await system.start(wait_for_ready=False)
        print("Actor system started")

        # Get the components
        supervisor = system.get_supervisor()
        stream_manager = system.get_stream_manager()
        state_manager = system.get_state_manager()
        metrics_collector = system.get_metrics_collector()

        print(f"Supervisor: {supervisor}")
        print(f"Stream Manager: {stream_manager}")
        print(f"State Manager: {state_manager}")
        print(f"Metrics Collector: {metrics_collector}")

        # Create and register actors
        actor1 = TestActor("actor1")
        actor2 = TestActor("actor2")
        system.register_actor(actor1)
        system.register_actor(actor2)
        print("Actors registered")

        # Test the stream manager
        if stream_manager:
            # Create a stream
            stream = await stream_manager.create_stream(name="test-stream")
            await stream_manager.register_stream(stream, "actor1")

            # Write to the stream
            await stream.write("test-data")

            # Read from the stream
            data = await stream.read()
            print(f"Stream data: {data}")

        # Test the state manager
        if state_manager:
            # Save state for an actor
            await state_manager.save_state("actor1", {"key": "value"})

            # Check that the state exists
            has_state = await state_manager.has_state("actor1")
            print(f"Actor1 has state: {has_state}")

            # Load the state
            state = await state_manager.load_state("actor1")
            print(f"Actor1 state: {state}")

        # Test the metrics collector
        if metrics_collector:
            # Record some metrics
            metrics_collector.counter("test_counter", labels={"service": "test"})
            metrics_collector.gauge("test_gauge", 42, labels={"service": "test"})

            # Export metrics
            await metrics_collector._export_metrics()

        # Test message passing
        message = Message(
            type=MessageType.CUSTOM,
            payload={"test": "data"},
            sender_id="actor1",
            recipient_id="actor2"
        )
        # Send the message through the system
        await system.send_message(message)

        # Wait for the message to be processed
        await asyncio.sleep(1)

        # Check that the message was received
        print(f"Actor2 received {len(actor2.received_messages)} messages")

        # Wait a bit to see all the logs
        await asyncio.sleep(2)

    finally:
        # Stop the actor system
        await system.stop()
        print("Actor system stopped")


if __name__ == "__main__":
    asyncio.run(test_components())
