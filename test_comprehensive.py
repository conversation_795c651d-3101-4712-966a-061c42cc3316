"""
Comprehensive test for the enhanced actor system.

This script demonstrates the full capabilities of the enhanced actor system,
including the StreamManager, StateManager, and MetricsCollector components.
"""

import asyncio
import logging
import sys
import time
from pathlib import Path
from typing import Any, Dict, List, Optional, Set, Tuple, Union

from vibe_check.core.actor_system.actor_system import ActorSystem
from vibe_check.core.actor_system.actor import Actor
from vibe_check.core.actor_system.message import Message, MessageType
from vibe_check.core.actor_system.components import (
    SupervisorComponent, StreamManager, StateManager, MetricsCollector,
    Stream, AsyncStream, StreamProcessor, StreamTransformer
)


class DataProducerActor(Actor):
    """Actor that produces data and sends it to a stream."""

    def __init__(self, actor_id: str) -> None:
        super().__init__(actor_id=actor_id)
        self.data_count = 0
        self.stream: Optional[Stream] = None
        self.system = None  # Will be set by the actor system

    async def receive(self, message: Message) -> None:
        """Handle received messages."""
        if message.type == MessageType.CUSTOM:
            payload = message.payload or {}
            command = payload.get("command")

            if command == "set_stream":
                self.stream = payload.get("stream")
                print(f"Actor {self.actor_id}: Stream set")
            elif command == "produce_data":
                count = payload.get("count", 1)
                await self._produce_data(count)
                print(f"Actor {self.actor_id}: Produced {count} data items")

    async def _produce_data(self, count: int) -> None:
        """Produce data and send it to the stream."""
        if not self.stream:
            print(f"Actor {self.actor_id}: No stream set")
            return

        for i in range(count):
            self.data_count += 1
            data = f"Data {self.data_count} from {self.actor_id}"
            print(f"Actor {self.actor_id}: Producing {data}")
            await self.stream.write(data)


class DataProcessorActor(Actor):
    """Actor that processes data from a stream."""

    def __init__(self, actor_id: str) -> None:
        super().__init__(actor_id=actor_id)
        self.input_stream: Optional[Stream] = None
        self.output_stream: Optional[Stream] = None
        self.processed_count = 0
        self.processing_task: Optional[asyncio.Task] = None
        self.system = None  # Will be set by the actor system

    async def receive(self, message: Message) -> None:
        """Handle received messages."""
        if message.type == MessageType.CUSTOM:
            payload = message.payload or {}
            command = payload.get("command")

            if command == "set_streams":
                self.input_stream = payload.get("input_stream")
                self.output_stream = payload.get("output_stream")
                print(f"Actor {self.actor_id}: Streams set")
            elif command == "start_processing":
                await self._start_processing()
                print(f"Actor {self.actor_id}: Processing started")
            elif command == "stop_processing":
                await self._stop_processing()
                print(f"Actor {self.actor_id}: Processing stopped")

    async def _start_processing(self) -> None:
        """Start processing data from the input stream."""
        if not self.input_stream or not self.output_stream:
            print(f"Actor {self.actor_id}: Streams not set")
            return

        if self.processing_task and not self.processing_task.done():
            print(f"Actor {self.actor_id}: Already processing")
            return

        self.processing_task = asyncio.create_task(self._process_data())
        print(f"Actor {self.actor_id}: Processing started")

    async def _stop_processing(self) -> None:
        """Stop processing data."""
        if not self.processing_task or self.processing_task.done():
            print(f"Actor {self.actor_id}: Not processing")
            return

        self.processing_task.cancel()
        try:
            await self.processing_task
        except asyncio.CancelledError:
            pass
        self.processing_task = None
        print(f"Actor {self.actor_id}: Processing stopped")

    async def _process_data(self) -> None:
        """Process data from the input stream and write to the output stream."""
        if not self.input_stream or not self.output_stream:
            return

        try:
            while True:
                try:
                    # Read data from the input stream
                    data = await self.input_stream.read()

                    # If the stream is closed, exit the loop
                    if data is None:
                        print(f"Actor {self.actor_id}: Input stream closed")
                        break

                    # Process the data
                    self.processed_count += 1
                    processed_data = f"Processed: {data} (by {self.actor_id})"
                    print(f"Actor {self.actor_id}: {processed_data}")

                    # Write to the output stream
                    await self.output_stream.write(processed_data)
                except Exception as e:
                    print(f"Actor {self.actor_id}: Error processing data: {e}")
                    # If the stream is closed, exit the loop
                    if "closed" in str(e).lower():
                        print(f"Actor {self.actor_id}: Stream closed, stopping processing")
                        break
        except asyncio.CancelledError:
            print(f"Actor {self.actor_id}: Processing cancelled")
            raise


class DataConsumerActor(Actor):
    """Actor that consumes data from a stream."""

    def __init__(self, actor_id: str) -> None:
        super().__init__(actor_id=actor_id)
        self.stream: Optional[Stream] = None
        self.consumed_data: List[str] = []
        self.consuming_task: Optional[asyncio.Task] = None
        self.system = None  # Will be set by the actor system

    async def receive(self, message: Message) -> None:
        """Handle received messages."""
        if message.type == MessageType.CUSTOM:
            payload = message.payload or {}
            command = payload.get("command")

            if command == "set_stream":
                self.stream = payload.get("stream")
                print(f"Actor {self.actor_id}: Stream set")
            elif command == "start_consuming":
                await self._start_consuming()
                print(f"Actor {self.actor_id}: Consuming started")
            elif command == "stop_consuming":
                await self._stop_consuming()
                print(f"Actor {self.actor_id}: Consuming stopped")
            elif command == "get_consumed_data":
                print(f"Actor {self.actor_id}: Returning consumed data: {len(self.consumed_data)} items")

    async def _start_consuming(self) -> None:
        """Start consuming data from the stream."""
        if not self.stream:
            print(f"Actor {self.actor_id}: No stream set")
            return

        if self.consuming_task and not self.consuming_task.done():
            print(f"Actor {self.actor_id}: Already consuming")
            return

        self.consuming_task = asyncio.create_task(self._consume_data())
        print(f"Actor {self.actor_id}: Consuming started")

    async def _stop_consuming(self) -> None:
        """Stop consuming data."""
        if not self.consuming_task or self.consuming_task.done():
            print(f"Actor {self.actor_id}: Not consuming")
            return

        self.consuming_task.cancel()
        try:
            await self.consuming_task
        except asyncio.CancelledError:
            pass
        self.consuming_task = None
        print(f"Actor {self.actor_id}: Consuming stopped")

    async def _consume_data(self) -> None:
        """Consume data from the stream."""
        if not self.stream:
            return

        try:
            while True:
                try:
                    # Read data from the stream
                    data = await self.stream.read()

                    # If the stream is closed, exit the loop
                    if data is None:
                        print(f"Actor {self.actor_id}: Stream closed")
                        break

                    # Store the data
                    self.consumed_data.append(data)
                    print(f"Actor {self.actor_id}: Consumed {data}")
                except Exception as e:
                    print(f"Actor {self.actor_id}: Error consuming data: {e}")
                    # If the stream is closed, exit the loop
                    if "closed" in str(e).lower():
                        print(f"Actor {self.actor_id}: Stream closed, stopping consumption")
                        break
        except asyncio.CancelledError:
            print(f"Actor {self.actor_id}: Consuming cancelled")
            raise


class StatefulActor(Actor):
    """Actor that maintains state that can be persisted."""

    def __init__(self, actor_id: str) -> None:
        super().__init__(actor_id=actor_id)
        self.state: Dict[str, Any] = {"counter": 0, "data": {}}
        self.system = None  # Will be set by the actor system

    async def receive(self, message: Message) -> None:
        """Handle received messages."""
        if message.type == MessageType.CUSTOM:
            payload = message.payload or {}
            command = payload.get("command")

            if command == "increment_counter":
                amount = payload.get("amount", 1)
                self.state["counter"] += amount
                print(f"Actor {self.actor_id}: Counter incremented by {amount} to {self.state['counter']}")
            elif command == "set_data":
                key = payload.get("key")
                value = payload.get("value")
                if key:
                    self.state["data"][key] = value
                    print(f"Actor {self.actor_id}: Data set for key {key}")
            elif command == "get_data":
                key = payload.get("key")
                if key:
                    value = self.state["data"].get(key)
                    print(f"Actor {self.actor_id}: Data for key {key}: {value}")
            elif command == "get_state":
                print(f"Actor {self.actor_id}: Current state: {self.state}")
            elif command == "save_state":
                # In our test environment, we don't have a state manager
                print(f"Actor {self.actor_id}: No state manager available")
            elif command == "load_state":
                # In our test environment, we don't have a state manager
                print(f"Actor {self.actor_id}: No state manager available")


async def run_comprehensive_test() -> None:
    """Run a comprehensive test of the actor system components."""
    # Configure logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[logging.StreamHandler(sys.stdout)]
    )

    # Create an actor system
    system = ActorSystem()

    try:
        # Start the actor system
        await system.start(wait_for_ready=False)
        print("Actor system started")

        # Get the components
        stream_manager = system.get_stream_manager()
        state_manager = system.get_state_manager()
        metrics_collector = system.get_metrics_collector()

        if not stream_manager or not state_manager or not metrics_collector:
            print("Error: Required components not available")
            return

        # Create and register actors
        producer = DataProducerActor("producer")
        processor = DataProcessorActor("processor")
        consumer = DataConsumerActor("consumer")
        stateful = StatefulActor("stateful")

        system.register_actor(producer)
        system.register_actor(processor)
        system.register_actor(consumer)
        system.register_actor(stateful)

        print("Actors registered")

        # Create streams
        raw_data_stream = await stream_manager.create_stream(name="raw-data", max_size=10)
        processed_data_stream = await stream_manager.create_stream(name="processed-data", max_size=10)

        # Register streams with the stream manager
        await stream_manager.register_stream(raw_data_stream, "producer")
        await stream_manager.register_stream(processed_data_stream, "processor")

        print("Streams created and registered")

        # Set up the stream pipeline
        await system.send_message(Message(
            type=MessageType.CUSTOM,
            payload={"command": "set_stream", "stream": raw_data_stream},
            sender_id="system",
            recipient_id="producer"
        ))

        await system.send_message(Message(
            type=MessageType.CUSTOM,
            payload={"command": "set_streams", "input_stream": raw_data_stream, "output_stream": processed_data_stream},
            sender_id="system",
            recipient_id="processor"
        ))

        await system.send_message(Message(
            type=MessageType.CUSTOM,
            payload={"command": "set_stream", "stream": processed_data_stream},
            sender_id="system",
            recipient_id="consumer"
        ))

        print("Stream pipeline set up")

        # Start processing and consuming
        await system.send_message(Message(
            type=MessageType.CUSTOM,
            payload={"command": "start_processing"},
            sender_id="system",
            recipient_id="processor"
        ))

        await system.send_message(Message(
            type=MessageType.CUSTOM,
            payload={"command": "start_consuming"},
            sender_id="system",
            recipient_id="consumer"
        ))

        print("Processing and consuming started")

        # Produce some data
        await system.send_message(Message(
            type=MessageType.CUSTOM,
            payload={"command": "produce_data", "count": 5},
            sender_id="system",
            recipient_id="producer"
        ))

        # Wait for processing to complete
        await asyncio.sleep(1)

        # Test the stateful actor
        await system.send_message(Message(
            type=MessageType.CUSTOM,
            payload={"command": "increment_counter", "amount": 5},
            sender_id="system",
            recipient_id="stateful"
        ))

        await system.send_message(Message(
            type=MessageType.CUSTOM,
            payload={"command": "set_data", "key": "test", "value": "data"},
            sender_id="system",
            recipient_id="stateful"
        ))

        await system.send_message(Message(
            type=MessageType.CUSTOM,
            payload={"command": "get_state"},
            sender_id="system",
            recipient_id="stateful"
        ))

        # Save the state
        await system.send_message(Message(
            type=MessageType.CUSTOM,
            payload={"command": "save_state"},
            sender_id="system",
            recipient_id="stateful"
        ))

        print("State saved")

        # Modify the state
        await system.send_message(Message(
            type=MessageType.CUSTOM,
            payload={"command": "increment_counter", "amount": 10},
            sender_id="system",
            recipient_id="stateful"
        ))

        await system.send_message(Message(
            type=MessageType.CUSTOM,
            payload={"command": "get_state"},
            sender_id="system",
            recipient_id="stateful"
        ))

        # Load the saved state
        await system.send_message(Message(
            type=MessageType.CUSTOM,
            payload={"command": "load_state"},
            sender_id="system",
            recipient_id="stateful"
        ))

        print("State loaded")

        # Check the consumed data
        await system.send_message(Message(
            type=MessageType.CUSTOM,
            payload={"command": "get_consumed_data"},
            sender_id="system",
            recipient_id="consumer"
        ))

        # Export metrics
        await metrics_collector._export_metrics()

        # Wait a bit to see all the logs
        await asyncio.sleep(2)

    finally:
        # Stop the actor system
        await system.stop()
        print("Actor system stopped")


if __name__ == "__main__":
    asyncio.run(run_comprehensive_test())
