#!/usr/bin/env python3
"""
Simple test script for the actor system.
"""

import asyncio
import logging
import sys
from pathlib import Path

# Add the project root to the Python path
sys.path.append(str(Path(__file__).parent))

# Configure logging
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("simple_actor_test")

# Import actor system components
from vibe_check.core.actor_system.logging.enhanced_logger import setup_actor_system_logging
from vibe_check.core.actor_system.actor_system import ActorSystem


async def main() -> None:
    """Main function."""
    logger.info("Starting test")
    
    # Configure actor system logging
    setup_actor_system_logging(debug_mode=True)
    
    # Create actor system
    logger.info("Creating actor system")
    system = ActorSystem()
    logger.info("Actor system created")
    
    try:
        # Start the system
        logger.info("Starting the system")
        await system.start()
        logger.info("System started")
        
        # Wait a bit
        logger.info("Waiting...")
        await asyncio.sleep(1)
        
        # Stop the system
        logger.info("Stopping the system")
        await system.stop()
        logger.info("System stopped")
        
        logger.info("Test completed successfully")
    except Exception as e:
        logger.error(f"Test failed: {e}")
        import traceback
        logger.error(traceback.format_exc())
        raise


if __name__ == "__main__":
    asyncio.run(main())
