"""
Orchestrator Module
================

This module defines the Orchestrator class, which initializes and coordinates
the actor system for project analysis. It implements the CAW principle of
choreographed interactions by setting up communication paths between actors.

The orchestrator doesn't centrally control the flow; instead, it establishes
the initial conditions for emergent behavior among actors.

This module has been refactored to use the components in the orchestration package:
- ActorSystemBuilder: Responsible for creating and configuring the actor system
- ActorConnector: Responsible for connecting actors to each other
- ActorLifecycleManager: Responsible for starting, stopping, and monitoring actors
- ExecutionModeManager: Responsible for managing the execution mode (parallel/sequential)
"""

import asyncio
import logging
import os
import time
from pathlib import Path
from typing import Any, Dict, Optional, Set, Tuple, Union

from .actor_system import (
    Actor,
    ActorPool,
    ContextWave,
    Message,
    MessageType,
    get_registry,
    reset_registry,
)
from .actor_system.actors.project_actor import ProjectActor
from .actor_system.actors.report_actor import ReportActor
from .actor_system.actors.visualization_actor import VisualizationActor
from .orchestration import (
    ActorSystemBuilder,
    ActorConnector,
    ActorLifecycleManager,
    ExecutionModeManager,
)
from .orchestration.actor_connection import ActorConnectionManager
from .orchestration.actor_initialization import ActorInitializationManager
from .orchestration.analysis_execution import AnalysisExecutionManager
from .orchestration.stability_manager import StabilityManager
from .utils.config_utils import load_config
from .utils.error_utils import log_error_with_context, create_error_context

logger = logging.getLogger("vibe_check_orchestrator")


class Orchestrator:
    """
    Orchestrator for the Vibe Check analysis system.

    This class initializes and coordinates the actor system, setting up
    the communication paths between actors but not controlling their interactions.
    It implements the CAW principle of choreographed interactions.

    This class has been refactored to use the components in the orchestration package:
    - ActorSystemBuilder: Responsible for creating and configuring the actor system
    - ActorConnector: Responsible for connecting actors to each other
    - ActorLifecycleManager: Responsible for starting, stopping, and monitoring actors
    - ExecutionModeManager: Responsible for managing the execution mode (parallel/sequential)
    """

    def __init__(self, config_path: Optional[Union[str, Path]] = None):
        """
        Initialize the orchestrator.

        Args:
            config_path: Optional path to a configuration file
        """
        # Load configuration
        self.config = load_config(config_path)

        # Initialize the actors
        self.actors: Dict[str, Actor] = {}
        self.project_actor: Optional[ProjectActor] = None
        self.report_actor: Optional[ReportActor] = None
        self.visualization_actor: Optional[VisualizationActor] = None
        self.project_path: Union[str, Path, None] = None  # Will be set when analyze_project is called
        self.file_actor_pool: Optional[ActorPool] = None

        # Set default output directory
        self.output_dir = Path(self.config.get("output_dir", "vibe_check_output"))
        os.makedirs(self.output_dir, exist_ok=True)

        # Tracking for analysis progress
        self.analysis_task: Optional[asyncio.Task] = None
        self.analysis_complete: Optional[asyncio.Future] = None
        self.is_running = False

        # Create component managers
        self.actor_system_builder = ActorSystemBuilder(self.config, self.output_dir, self.project_path)
        self.actor_connector: Optional[ActorConnector] = None  # Will be initialized after actors are created
        self.actor_lifecycle_manager: Optional[ActorLifecycleManager] = None  # Will be initialized after actors are created
        self.execution_mode_manager = ExecutionModeManager(self.config)

        # New modular managers
        self.actor_connection_manager: Optional[ActorConnectionManager] = None
        self.actor_initialization_manager: Optional[ActorInitializationManager] = None
        self.analysis_execution_manager: Optional[AnalysisExecutionManager] = None
        self.stability_manager: Optional[StabilityManager] = None

        # Set default execution mode
        self.execution_mode = self.config.get("execution_mode", "parallel")

        # Context storage
        self.current_context: Dict[str, Any] = {}

    def create_actor_system(self) -> Dict[str, Actor]:
        """
        Create the actor system with all necessary actors.

        Enhanced to use the ActorSystemBuilder component for better modularity
        and maintainability. This method implements the CAW principle of choreographed
        interactions by setting up the initial conditions for emergent behavior.

        Returns:
            Dictionary of actor IDs to actor instances
        """
        try:
            # Update the project path in the builder if it has been set
            if self.project_path:
                # Convert to Path if it's a string
                project_path = Path(self.project_path) if isinstance(self.project_path, str) else self.project_path
                self.actor_system_builder = ActorSystemBuilder(
                    self.config,
                    self.output_dir,
                    project_path
                )

            # Build the actor system
            logger.info("Building actor system using ActorSystemBuilder")
            self.actors = self.actor_system_builder.build()

            # Get references to important actors
            self.project_actor = self.actor_system_builder.project_actor
            self.report_actor = self.actor_system_builder.report_actor
            self.visualization_actor = self.actor_system_builder.visualization_actor
            self.file_actor_pool = self.actor_system_builder.file_actor_pool

            # Initialize the actor connector
            self.actor_connector = ActorConnector(self.actors)

            # Initialize the actor lifecycle manager
            self.actor_lifecycle_manager = ActorLifecycleManager(self.actors, self.config)

            # Initialize new modular managers
            self.actor_connection_manager = ActorConnectionManager(self.actors)
            self.actor_initialization_manager = ActorInitializationManager(self.actors, self.file_actor_pool)
            self.analysis_execution_manager = AnalysisExecutionManager(self.project_actor, self.config)
            self.stability_manager = StabilityManager(self.actors, self.config)

            # Connect all actors to each other
            logger.info("Connecting actors using ActorConnectionManager")
            self.actor_connection_manager.connect_all_actors()

            logger.info(f"Actor system created with {len(self.actors)} actors")
            return self.actors

        except Exception as e:
            logger.error(f"Error creating actor system: {e}")
            import traceback
            logger.error(traceback.format_exc())
            raise



    async def start_actors(self) -> None:
        """
        Start all actors in the actor system.

        Enhanced to use the ActorLifecycleManager component for better modularity
        and maintainability. This method implements a two-phase initialization process
        with explicit synchronization points to ensure all actors are properly initialized
        before they start communicating.

        Enhanced with robust error handling, cleanup on failure, and configurable
        fail-fast option.
        """
        try:
            # Ensure the actor lifecycle manager is initialized
            if self.actor_lifecycle_manager is None:
                logger.info("Initializing ActorLifecycleManager")
                self.actor_lifecycle_manager = ActorLifecycleManager(self.actors, self.config)

            # Start all actors using the lifecycle manager
            logger.info("Starting actors using ActorLifecycleManager")
            await self.actor_lifecycle_manager.start_actors()

            logger.info("All actors started successfully")

        except Exception as e:
            logger.error(f"Error during actor system initialization: {e}")
            import traceback
            logger.error(traceback.format_exc())

            # Clean up any started actors
            if self.actor_lifecycle_manager is not None:
                # Stop all actors
                await self.stop_actors()

            # Re-raise the exception
            raise

    async def _setup_actor_initializer(self) -> Tuple[Any, bool]:
        """
        Set up the actor initializer.

        Returns:
            Tuple containing:
            - The initializer instance
            - The fail-fast flag
        """
        from .actor_system.actor_initializer import (
            get_initializer,
            reset_initializer
        )

        # Reset the initializer to ensure a clean state
        reset_initializer()

        # Create initializer with fail-fast option from config
        fail_fast = self.config.get("actor_system", {}).get("fail_fast", False)
        initializer = get_initializer()
        initializer.set_fail_fast(fail_fast)

        logger.info(f"Reset actor initializer (fail_fast={fail_fast})")

        return initializer, fail_fast

    async def _initialize_all_actors(self, initializer: Any, fail_fast: bool) -> Set[str]:
        """
        Initialize all actors in the system.

        Args:
            initializer: The actor initializer instance
            fail_fast: Whether to fail fast on errors

        Returns:
            Set of initialized actor IDs

        Raises:
            RuntimeError: If no actors were successfully initialized
        """
        initialized_actors: Set[str] = set()
        logger.info("Phase 1: Initializing all actors")

        # Initialize the supervisor actor first
        initialized_actors = await self._initialize_supervisor_actor(initialized_actors, fail_fast)

        # Initialize the file actor pool
        initialized_actors = await self._initialize_file_actor_pool(initialized_actors, fail_fast)

        # Initialize all other actors
        initialized_actors = await self._initialize_regular_actors(initialized_actors, fail_fast)

        # Check if we have enough actors initialized to proceed
        if not initialized_actors:
            raise RuntimeError("No actors were successfully initialized")

        return initialized_actors

    async def _initialize_supervisor_actor(self, initialized_actors: Set[str], fail_fast: bool) -> Set[str]:
        """
        Initialize the supervisor actor.

        Args:
            initialized_actors: Set of already initialized actor IDs
            fail_fast: Whether to fail fast on errors

        Returns:
            Updated set of initialized actor IDs
        """
        supervisor = self.actors.get("supervisor_actor")
        if supervisor:
            try:
                await supervisor.initialize()
                initialized_actors.add("supervisor_actor")
                logger.info("Supervisor actor initialized")
            except Exception as e:
                logger.error(f"Failed to initialize supervisor actor: {e}")
                import traceback
                logger.error(traceback.format_exc())

                # If fail-fast is enabled, this will re-raise the exception
                if fail_fast:
                    raise

        return initialized_actors

    async def _initialize_file_actor_pool(self, initialized_actors: Set[str], fail_fast: bool) -> Set[str]:
        """
        Initialize the file actor pool.

        Args:
            initialized_actors: Set of already initialized actor IDs
            fail_fast: Whether to fail fast on errors

        Returns:
            Updated set of initialized actor IDs
        """
        if hasattr(self, "file_actor_pool") and self.file_actor_pool is not None:
            try:
                # Initialize the pool
                await self.file_actor_pool.initialize()

                # Add all pool actors to the initialized set
                for actor_id in self.file_actor_pool.get_actors().keys():
                    initialized_actors.add(actor_id)

                logger.info("File actor pool initialized")
            except Exception as e:
                logger.error(f"Failed to initialize file actor pool: {e}")
                import traceback
                logger.error(traceback.format_exc())

                # If fail-fast is enabled, this will re-raise the exception
                if fail_fast:
                    raise

        return initialized_actors

    async def _initialize_regular_actors(self, initialized_actors: Set[str], fail_fast: bool) -> Set[str]:
        """
        Initialize all regular actors (not supervisor or file actor pool).

        Args:
            initialized_actors: Set of already initialized actor IDs
            fail_fast: Whether to fail fast on errors

        Returns:
            Updated set of initialized actor IDs
        """
        for actor_id, actor in self.actors.items():
            if actor_id != "supervisor_actor" and actor_id not in initialized_actors:  # Not already initialized
                try:
                    await actor.initialize()
                    initialized_actors.add(actor_id)
                    logger.info(f"Actor {actor_id} initialized")
                except Exception as e:
                    logger.error(f"Failed to initialize actor {actor_id}: {e}")
                    import traceback
                    logger.error(traceback.format_exc())

                    # If fail-fast is enabled, this will re-raise the exception
                    if fail_fast:
                        raise

        return initialized_actors

    async def _register_actor_dependencies(self, initializer: Any, initialized_actors: Set[str]) -> None:
        """
        Register dependencies between actors.

        Args:
            initializer: The actor initializer instance
            initialized_actors: Set of initialized actor IDs
        """
        logger.info("Registering actor dependencies")

        # Register dependencies by category
        await self._register_supervisor_dependencies(initializer, initialized_actors)
        await self._register_tool_actor_dependencies(initializer, initialized_actors)
        await self._register_project_actor_dependencies(initializer, initialized_actors)
        await self._register_report_actor_dependencies(initializer, initialized_actors)

        logger.info("Actor dependencies registered")

    async def _register_supervisor_dependencies(self, initializer: Any, initialized_actors: Set[str]) -> None:
        """
        Register dependencies related to the supervisor actor.

        Args:
            initializer: The actor initializer instance
            initialized_actors: Set of initialized actor IDs
        """
        # All actors depend on the supervisor if it was initialized
        if "supervisor_actor" in initialized_actors:
            for actor_id in initialized_actors:
                if actor_id != "supervisor_actor":
                    initializer.register_dependency(actor_id, "supervisor_actor")

    async def _register_tool_actor_dependencies(self, initializer: Any, initialized_actors: Set[str]) -> None:
        """
        Register dependencies related to the tool actor.

        Args:
            initializer: The actor initializer instance
            initialized_actors: Set of initialized actor IDs
        """
        # File actors depend on the tool actor if both were initialized
        if "tool_actor" in initialized_actors:
            for actor_id in initialized_actors:
                if actor_id.startswith("file_actor_"):
                    initializer.register_dependency(actor_id, "tool_actor")

    async def _register_project_actor_dependencies(self, initializer: Any, initialized_actors: Set[str]) -> None:
        """
        Register dependencies related to the project actor.

        Args:
            initializer: The actor initializer instance
            initialized_actors: Set of initialized actor IDs
        """
        # Project actor dependencies
        if "project_actor" in initialized_actors:
            if "report_actor" in initialized_actors:
                initializer.register_dependency("project_actor", "report_actor")

            for actor_id in initialized_actors:
                if actor_id.startswith("file_actor_"):
                    initializer.register_dependency("project_actor", actor_id)

    async def _register_report_actor_dependencies(self, initializer: Any, initialized_actors: Set[str]) -> None:
        """
        Register dependencies related to the report actor.

        Args:
            initializer: The actor initializer instance
            initialized_actors: Set of initialized actor IDs
        """
        # Report actor depends on visualization actor
        if "report_actor" in initialized_actors and "visualization_actor" in initialized_actors:
            initializer.register_dependency("report_actor", "visualization_actor")

    async def _start_all_actors(self, initializer: Any, initialized_actors: Set[str], fail_fast: bool) -> Set[str]:
        """
        Start all initialized actors.

        Args:
            initializer: The actor initializer instance
            initialized_actors: Set of initialized actor IDs
            fail_fast: Whether to fail fast on errors

        Returns:
            Set of started actor IDs

        Raises:
            RuntimeError: If no actors were successfully started
        """
        started_actors: Set[str] = set()
        logger.info("Phase 2: Starting all actors")

        # Start the supervisor actor first
        started_actors = await self._start_supervisor_actor(initialized_actors, started_actors, fail_fast)

        # Start the file actor pool
        started_actors = await self._start_file_actor_pool(initialized_actors, started_actors, fail_fast)

        # Start all other actors
        started_actors = await self._start_regular_actors(initialized_actors, started_actors, fail_fast)

        # Check if we have enough actors started to proceed
        if not started_actors:
            raise RuntimeError("No actors were successfully started")

        return started_actors

    async def _start_supervisor_actor(self, initialized_actors: Set[str], started_actors: Set[str], fail_fast: bool) -> Set[str]:
        """
        Start the supervisor actor.

        Args:
            initialized_actors: Set of initialized actor IDs
            started_actors: Set of already started actor IDs
            fail_fast: Whether to fail fast on errors

        Returns:
            Updated set of started actor IDs
        """
        supervisor = self.actors.get("supervisor_actor")
        if "supervisor_actor" in initialized_actors and supervisor:
            try:
                await supervisor.start()
                started_actors.add("supervisor_actor")
                logger.info("Supervisor actor started")
            except Exception as e:
                logger.error(f"Failed to start supervisor actor: {e}")
                import traceback
                logger.error(traceback.format_exc())

                # If fail-fast is enabled, this will re-raise the exception
                if fail_fast:
                    raise

        return started_actors

    async def _start_file_actor_pool(self, initialized_actors: Set[str], started_actors: Set[str], fail_fast: bool) -> Set[str]:
        """
        Start the file actor pool.

        Args:
            initialized_actors: Set of initialized actor IDs
            started_actors: Set of already started actor IDs
            fail_fast: Whether to fail fast on errors

        Returns:
            Updated set of started actor IDs
        """
        if hasattr(self, "file_actor_pool") and self.file_actor_pool is not None and any(actor_id.startswith("file_actor_") for actor_id in initialized_actors):
            try:
                await self.file_actor_pool.start()

                # Add all pool actors to the started set
                for actor_id in self.file_actor_pool.get_actors().keys():
                    if actor_id in initialized_actors:
                        started_actors.add(actor_id)

                logger.info("File actor pool started")
            except Exception as e:
                logger.error(f"Failed to start file actor pool: {e}")
                import traceback
                logger.error(traceback.format_exc())

                # If fail-fast is enabled, this will re-raise the exception
                if fail_fast:
                    raise

        return started_actors

    async def _start_regular_actors(self, initialized_actors: Set[str], started_actors: Set[str], fail_fast: bool) -> Set[str]:
        """
        Start all regular actors (not supervisor or file actor pool).

        Args:
            initialized_actors: Set of initialized actor IDs
            started_actors: Set of already started actor IDs
            fail_fast: Whether to fail fast on errors

        Returns:
            Updated set of started actor IDs
        """
        for actor_id, actor in self.actors.items():
            if actor_id in initialized_actors and actor_id != "supervisor_actor" and actor_id not in started_actors:
                try:
                    await actor.start()
                    started_actors.add(actor_id)
                    logger.info(f"Actor {actor_id} started")
                except Exception as e:
                    logger.error(f"Failed to start actor {actor_id}: {e}")
                    import traceback
                    logger.error(traceback.format_exc())

                    # If fail-fast is enabled, this will re-raise the exception
                    if fail_fast:
                        raise

        return started_actors

    async def _wait_for_actors_ready(self, initializer: Any) -> None:
        """
        Wait for all actors to be ready.

        Args:
            initializer: The actor initializer instance
        """
        logger.info("Waiting for all actors to be ready")
        all_ready = await initializer.wait_all_ready(timeout=30.0)
        if all_ready:
            logger.info("All actors are ready")
        else:
            # Get information about failed actors
            failed_actors = initializer.get_failed_actors()
            if failed_actors:
                logger.warning(f"{len(failed_actors)} actors failed during initialization/startup")
                for actor_id, error in failed_actors.items():
                    logger.warning(f"Actor {actor_id} failed during {error.phase} phase: {error.message}")
            else:
                logger.warning("Timeout waiting for all actors to be ready, continuing anyway")

    async def _fallback_initialization(self) -> Set[str]:
        """
        Fall back to the old initialization process if the initializer is not available.

        Returns:
            Set of started actor IDs
        """
        started_actors = set()

        # Start the supervisor actor first
        supervisor = self.actors.get("supervisor_actor")
        if supervisor:
            try:
                await supervisor.start()
                started_actors.add("supervisor_actor")
                logger.info("Supervisor actor started")
            except Exception as e:
                logger.error(f"Failed to start supervisor actor: {e}")
                raise

        # Start the file actor pool
        if hasattr(self, "file_actor_pool") and self.file_actor_pool is not None:
            try:
                await self.file_actor_pool.start()
                logger.info("File actor pool started")
            except Exception as e:
                logger.error(f"Failed to start file actor pool: {e}")
                raise

        # Start all other actors
        for actor_id, actor in self.actors.items():
            if actor_id != "supervisor_actor":  # Supervisor already started
                try:
                    await actor.start()
                    started_actors.add(actor_id)
                    logger.info(f"Actor {actor_id} started")
                except Exception as e:
                    logger.error(f"Failed to start actor {actor_id}: {e}")
                    raise

        return started_actors

    async def _cleanup_actors(self, started_actors: Set[str], initialized_actors: Set[str]) -> None:
        """
        Clean up actors after a failed initialization or start.

        Args:
            started_actors: Set of actor IDs that were successfully started
            initialized_actors: Set of actor IDs that were successfully initialized
        """
        logger.info(f"Cleaning up {len(started_actors)} started actors and {len(initialized_actors)} initialized actors")

        # Stop started actors first
        await self._stop_started_actors(started_actors)

        # Roll back initialized actors that weren't started
        await self._rollback_initialized_actors(initialized_actors - started_actors)

        logger.info("Actor cleanup completed")

    async def _stop_started_actors(self, started_actors: Set[str]) -> None:
        """
        Stop actors that were successfully started.

        Args:
            started_actors: Set of actor IDs that were successfully started
        """
        for actor_id in started_actors:
            try:
                actor = self._get_actor_by_id(actor_id)
                if actor:
                    await actor.stop()
                    logger.info(f"Stopped actor {actor_id} during cleanup")
            except Exception as e:
                logger.error(f"Error stopping actor {actor_id} during cleanup: {e}")

    async def _rollback_initialized_actors(self, initialized_actors: Set[str]) -> None:
        """
        Roll back actors that were initialized but not started.

        Args:
            initialized_actors: Set of actor IDs that were initialized but not started
        """
        for actor_id in initialized_actors:
            try:
                actor = self._get_actor_by_id(actor_id)
                if actor:
                    await self._rollback_actor(actor_id)
            except Exception as e:
                logger.error(f"Error rolling back actor {actor_id} during cleanup: {e}")

    def _get_actor_by_id(self, actor_id: str) -> Optional[Actor]:
        """
        Get an actor by its ID from the appropriate collection.

        Args:
            actor_id: The actor ID

        Returns:
            The actor instance, or None if not found
        """
        # Get the actor from the appropriate collection
        if actor_id.startswith("file_actor_") and hasattr(self, "file_actor_pool") and self.file_actor_pool is not None:
            return self.file_actor_pool.get_actors().get(actor_id)
        else:
            return self.actors.get(actor_id)

    async def _rollback_actor(self, actor_id: str) -> None:
        """
        Roll back an actor using the initializer.

        Args:
            actor_id: The actor ID to roll back
        """
        try:
            from .actor_system.actor_initializer import get_initializer
            initializer = get_initializer()
            if initializer:
                await initializer.rollback_actor(actor_id)
                logger.info(f"Rolled back actor {actor_id} during cleanup")
        except (ImportError, Exception) as e:
            logger.warning(f"Could not roll back actor {actor_id}: {e}")

    async def stop_actors(self) -> None:
        """
        Stop all actors in the actor system.

        Enhanced to stop the actor pool and handle the supervisor actor.
        """
        # Stop all actors except the supervisor
        stop_tasks = []
        for actor_id, actor in self.actors.items():
            if actor_id != "supervisor_actor":  # Stop supervisor last
                stop_tasks.append(actor.stop())

        if stop_tasks:
            await asyncio.gather(*stop_tasks)
            logger.info("All regular actors stopped")

        # Stop the file actor pool
        if hasattr(self, "file_actor_pool") and self.file_actor_pool is not None:
            await self.file_actor_pool.stop()
            logger.info("File actor pool stopped")

        # Stop the supervisor actor last
        supervisor = self.actors.get("supervisor_actor")
        if supervisor:
            await supervisor.stop()
            logger.info("Supervisor actor stopped")

        logger.info("All actors stopped")

    async def _validate_project_path(self, project_path: Union[str, Path]) -> Optional[Dict[str, str]]:
        """
        Validate the project path.

        This is a helper method for the analyze_project method to make it less complex.

        Args:
            project_path: Path to the project directory

        Returns:
            None if the path is valid, or a dictionary with an error message if invalid
        """
        try:
            # Convert project_path to Path object if it's a string
            if isinstance(project_path, str):
                project_path = Path(project_path)
                logger.info(f"Converted project_path to Path object: {project_path}")

            # Validate project path
            if not project_path.exists():
                error_msg = f"Project path does not exist: {project_path}"
                logger.error(error_msg)
                return {"error": error_msg}

            if not project_path.is_dir():
                error_msg = f"Project path is not a directory: {project_path}"
                logger.error(error_msg)
                return {"error": error_msg}

            # Store project path for later use
            self.project_path = project_path
            logger.info(f"Stored project_path: {self.project_path}")

            return None
        except Exception as e:
            import traceback
            error_details = traceback.format_exc()
            logger.error(f"Error validating project path: {e}\n{error_details}")
            return {"error": str(e), "error_details": error_details}

    async def _initialize_actor_system(self) -> Optional[Dict[str, str]]:
        """
        Initialize the actor system if not already initialized.

        This is a helper method for the analyze_project method to make it less complex.
        It has been enhanced to properly initialize the actor system with better error handling
        and diagnostics.

        Returns:
            None if successful, or a dictionary with an error message if failed
        """
        try:
            # Ensure output directory exists
            self.output_dir.mkdir(parents=True, exist_ok=True)
            logger.info(f"Ensured output directory exists: {self.output_dir}")

            # Initialize diagnostics tracker
            from .actor_system.diagnostics import initialize_tracker, InitializationStep
            tracker = initialize_tracker()
            logger.info("Initialized actor system diagnostics tracker")

            # Reset the actor registry to ensure a clean state
            from .actor_system.actor_registry import reset_registry
            reset_registry()
            logger.info("Reset actor registry")

            if not self.project_actor:
                logger.info("Creating actor system")
                self.create_actor_system()

                # Record actor creation in diagnostics
                for actor_id in self.actors:
                    await tracker.record_event(
                        actor_id=actor_id,
                        step=InitializationStep.REGISTRATION,
                        details={"message": f"Actor {actor_id} registered"}
                    )

                logger.info("Starting actors")

                # Use a timeout to prevent hanging
                try:
                    # Start the actors with a timeout
                    await asyncio.wait_for(self.start_actors(), timeout=60.0)
                    logger.info("Actors started successfully")

                    # Wait for all actors to be ready
                    from .actor_system.actor_initializer import get_initializer
                    initializer = get_initializer()

                    if initializer:
                        logger.info("Waiting for all actors to be ready")
                        ready = await initializer.wait_for_all_ready(timeout=30.0)

                        if ready:
                            logger.info("All actors are ready")

                            # Record success in diagnostics
                            await tracker.record_event(
                                actor_id="system",
                                step=InitializationStep.READY,
                                details={"message": "All actors are ready"}
                            )
                        else:
                            # Get information about failed actors
                            failed_actors = initializer.get_failed_actors()
                            if failed_actors:
                                logger.warning(f"{len(failed_actors)} actors failed during initialization/startup")
                                for actor_id, error in failed_actors.items():
                                    # Extract error information safely
                                    phase = getattr(error, "phase", "unknown")
                                    message = str(error)

                                    logger.warning(f"Actor {actor_id} failed during {phase} phase: {message}")

                                    # Record failure in diagnostics
                                    await tracker.record_event(
                                        actor_id=actor_id,
                                        step=InitializationStep.FAILED,
                                        details={"phase": phase, "message": message}
                                    )

                                # Return error if critical actors failed
                                critical_actors = {"project_actor", "report_actor", "supervisor_actor"}
                                failed_critical = critical_actors.intersection(failed_actors.keys())

                                if failed_critical:
                                    return {
                                        "error": f"Critical actors failed during initialization: {failed_critical}",
                                        "error_details": str(failed_actors)
                                    }
                            else:
                                logger.warning("Timeout waiting for all actors to be ready, continuing anyway")
                    else:
                        logger.warning("Actor initializer not available, cannot wait for actors to be ready")
                except asyncio.TimeoutError:
                    logger.error("Timeout starting actors")
                    return {"error": "Timeout starting actors", "error_details": "The actor system initialization timed out after 60 seconds"}

            return None
        except Exception as e:
            import traceback
            error_details = traceback.format_exc()
            logger.error(f"Error initializing actor system: {e}\n{error_details}")

            # Record error in diagnostics
            from .actor_system.diagnostics import get_tracker, InitializationStep
            tracker = get_tracker()
            if tracker:
                await tracker.record_event(
                    actor_id="system",
                    step=InitializationStep.FAILED,
                    details={"message": f"Error initializing actor system: {e}"},
                    error=e
                )

            return {"error": str(e), "error_details": error_details}

    async def _create_and_send_analysis_message(self, project_path: Path, context_wave: ContextWave) -> Optional[Dict[str, str]]:
        """
        Create and send the analysis message to the project actor.

        This is a helper method for the analyze_project method to make it less complex.

        Args:
            project_path: Path to the project directory
            context_wave: Context wave for the analysis

        Returns:
            None if successful, or a dictionary with an error message if failed
        """
        try:
            # Create analysis future to wait for completion
            self.analysis_complete = asyncio.Future[Dict[str, Any]]()
            self.is_running = True
            logger.info("Set is_running to True")

            # Ensure project actor is initialized
            if self.project_actor is None:
                return {"error": "Project actor is not initialized"}

            # Send the initial message to the project actor
            payload = {
                "project_path": str(project_path),
                "output_dir": str(self.output_dir),
                "completion_callback": self._analysis_completed
            }
            logger.info(f"Created payload: {payload}")

            # Create a message and send it to the project actor
            message = Message(
                type=MessageType.ANALYZE_PROJECT,
                payload=payload,
                context=context_wave,
                recipient_id="project_actor",
                sender_id="orchestrator"
            )
            logger.info(f"Created message with type {MessageType.ANALYZE_PROJECT}")

            # Send the message to the project actor
            logger.info("Creating task to send message to project actor")
            self.analysis_task = asyncio.create_task(
                self.project_actor.receive(message)
            )
            logger.info("Task created")

            return None
        except Exception as e:
            import traceback
            error_details = traceback.format_exc()
            logger.error(f"Error creating and sending analysis message: {e}\n{error_details}")
            return {"error": str(e), "error_details": error_details}

    async def _prepare_context_wave(self, context: Optional[Dict[str, Any]] = None) -> ContextWave:
        """
        Prepare the context wave for analysis.

        This is a helper method for the analyze_project method to make it less complex.

        Args:
            context: Optional context metadata

        Returns:
            Prepared context wave
        """
        # Store the context for later use
        if context is not None:
            self.current_context = context.copy()
            logger.info(f"Stored context for later use: {self.current_context}")

        # Create a context wave with provided metadata
        context_wave = ContextWave()
        if context:
            context_wave.metadata.update(context)
            logger.info(f"Updated context_wave with metadata: {context}")

        # Add project-level context
        context_wave.metadata["project_path"] = str(self.project_path)
        context_wave.metadata["analysis_start_time"] = time.time()
        context_wave.metadata["output_dir"] = str(self.output_dir)
        context_wave.metadata["sender_id"] = "orchestrator"
        context_wave.metadata["recipient_id"] = "project_actor"
        logger.info(f"Added project-level context: {context_wave.metadata}")

        # Adapt analysis based on configuration
        if "priorities" in self.config:
            context_wave.metadata["project_priorities"] = self.config["priorities"]
            logger.info(f"Added priorities to context: {self.config['priorities']}")

        return context_wave

    async def _wait_for_analysis_completion(self) -> Dict[str, Any]:
        """
        Wait for the analysis to complete.

        This is a helper method for the analyze_project method to make it less complex.

        Returns:
            Analysis results or error information
        """
        logger.info("Waiting for analysis to complete")
        try:
            if self.analysis_complete is None:
                logger.error("Analysis future is None")
                return {"error": "Analysis future is None"}

            results = await self.analysis_complete
            logger.info(f"Analysis completed with results: {results}")
            # Ensure we return a Dict[str, Any]
            if isinstance(results, dict):
                return results
            else:
                return {"result": results}
        except asyncio.CancelledError:
            logger.warning("Analysis was cancelled")
            return {"error": "Analysis was cancelled"}
        except Exception as e:
            import traceback
            error_details = traceback.format_exc()
            logger.error(f"Error waiting for analysis to complete: {e}\n{error_details}")
            return {"error": str(e), "error_details": error_details}

    def _record_failure(self, error_type: str) -> None:
        """
        Record a failure in the stability metrics.

        Args:
            error_type: Type of error that occurred
        """
        try:
            # Ensure stability_metrics is properly initialized
            assert isinstance(self.stability_metrics, dict), "stability_metrics should be a dictionary"

            # Increment failure counters
            actor_failures = int(self.stability_metrics.get("actor_failures", 0))
            consecutive_failures = int(self.stability_metrics.get("consecutive_failures", 0))

            self.stability_metrics["actor_failures"] = actor_failures + 1
            self.stability_metrics["consecutive_failures"] = consecutive_failures + 1

            # Update error categories
            error_categories = self.stability_metrics.get("error_categories", {})
            if not isinstance(error_categories, dict):
                error_categories = {}

            error_count = int(error_categories.get(error_type, 0))
            error_categories[error_type] = error_count + 1
            self.stability_metrics["error_categories"] = error_categories

            logger.debug(f"Recorded failure of type {error_type}. Total failures: {actor_failures + 1}")
        except Exception as e:
            logger.error(f"Error recording failure: {e}")

    def _record_recovery_attempt(self) -> None:
        """Record a recovery attempt in the stability metrics."""
        try:
            # Ensure stability_metrics is initialized
            if not isinstance(self.stability_metrics, dict):
                self.stability_metrics = {}  # type: ignore[unreachable]

            recovery_attempts = int(self.stability_metrics.get("recovery_attempts", 0))
            self.stability_metrics["recovery_attempts"] = recovery_attempts + 1
            logger.debug(f"Recorded recovery attempt. Total attempts: {recovery_attempts + 1}")
        except Exception as e:
            logger.error(f"Error recording recovery attempt: {e}")

    def _record_success(self) -> None:
        """Record a successful operation in the stability metrics."""
        try:
            # Ensure stability_metrics is initialized
            if not isinstance(self.stability_metrics, dict):
                self.stability_metrics = {}  # type: ignore[unreachable]

            self.stability_metrics["consecutive_failures"] = 0
            logger.debug("Reset consecutive failures counter")
        except Exception as e:
            logger.error(f"Error recording success: {e}")

    def _calculate_instability_score(self) -> float:
        """
        Calculate a numeric score representing system instability.

        Returns:
            Instability score (higher means more unstable)
        """
        try:
            # Ensure stability_metrics is initialized
            if not isinstance(self.stability_metrics, dict):
                self.stability_metrics = {}  # type: ignore[unreachable]

            # Base score from failures and timeouts
            actor_failures = int(self.stability_metrics.get("actor_failures", 0))
            message_timeouts = int(self.stability_metrics.get("message_timeouts", 0))
            base_score = float((actor_failures * 2) + (message_timeouts * 1.5))

            # Apply multipliers for consecutive failures
            consecutive_failures = int(self.stability_metrics.get("consecutive_failures", 0))
            if consecutive_failures > 3:
                base_score *= 1.5

            # Apply time decay (reduce score for older issues)
            last_mode_switch_time_value = self.stability_metrics.get("last_mode_switch_time")
            if last_mode_switch_time_value is None:
                last_mode_switch_time = time.time()
            else:
                try:
                    last_mode_switch_time = float(last_mode_switch_time_value)
                except (ValueError, TypeError):
                    last_mode_switch_time = time.time()

            time_since_switch = time.time() - last_mode_switch_time
            if time_since_switch > 300:  # 5 minutes
                base_score *= 0.8

            return float(base_score)
        except Exception as e:
            logger.error(f"Error calculating instability score: {e}")
            return 0.0

    async def _switch_execution_mode(self, new_mode: str) -> None:
        """
        Switch the execution mode of the actor system.

        Args:
            new_mode: New execution mode ("parallel" or "sequential")
        """
        if self.execution_mode == new_mode:
            return

        logger.info(f"Switching execution mode from {self.execution_mode} to {new_mode}")

        # Record the switch time
        self.stability_metrics["last_mode_switch_time"] = time.time()

        # Update the mode
        self.execution_mode = new_mode

        # Notify all actors about the mode change
        await self._notify_actors_of_mode_change(new_mode)

    async def _notify_actors_of_mode_change(self, new_mode: str) -> None:
        """
        Notify all actors about the execution mode change.

        Args:
            new_mode: New execution mode ("parallel" or "sequential")
        """
        # Get all actors
        registry = get_registry()
        all_actors = registry.get_all_actors()

        # Prepare the notification message
        mode_change_payload = {
            "new_execution_mode": new_mode,
            "timestamp": time.time(),
            "reason": "system_stability"
        }

        # Send to all actors
        for actor in all_actors:
            try:
                context = ContextWave()
                context.metadata["sender_id"] = "orchestrator"

                message = Message(
                    type=MessageType.SYSTEM_CONFIG,
                    payload=mode_change_payload,
                    context=context,
                    recipient_id=actor.actor_id,
                    sender_id="orchestrator"
                )

                # Create a task to send the message
                asyncio.create_task(actor.receive(message))

            except Exception as e:
                logger.error(f"Failed to notify actor {actor.actor_id} of mode change: {e}")

    async def _execute_analysis_sequential(self, project_path: Path, context_wave: ContextWave) -> Dict[str, Any]:
        """
        Execute analysis in sequential mode for increased reliability.

        Args:
            project_path: Path to the project directory
            context_wave: Context wave for the analysis

        Returns:
            Analysis results
        """
        logger.info("Executing analysis in SEQUENTIAL mode for increased reliability")

        try:
            # Ensure project_path is valid
            if project_path is None:
                if self.project_path is None:  # type: ignore[unreachable]
                    raise ValueError("Project path is not set")
                project_path = Path(self.project_path)

            # Create analysis future to wait for completion
            self.analysis_complete = asyncio.Future()

            # Initialize project analysis
            payload = {
                "project_path": str(project_path),
                "output_dir": str(self.output_dir),
                "completion_callback": self._analysis_completed,
                "execution_mode": "sequential"
            }

            # Create a message and send it to the project actor
            message = Message(
                type=MessageType.ANALYZE_PROJECT,
                payload=payload,
                context=context_wave,
                recipient_id="project_actor",
                sender_id="orchestrator"
            )

            # Ensure project actor is initialized
            if self.project_actor is None:
                raise ValueError("Project actor is not initialized")

            # Send the message to the project actor and wait for response
            await self.project_actor.receive(message)

            # Wait for the analysis to complete
            return await self._wait_for_analysis_completion()

        except Exception as e:
            logger.error(f"Error in sequential analysis: {e}")
            import traceback
            logger.error(traceback.format_exc())

            # Record failure
            self._record_failure(str(type(e).__name__))

            return {"error": str(e), "error_details": traceback.format_exc()}

    async def _execute_analysis_with_recovery(self, project_path: Optional[Path] = None, context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Execute analysis with automatic recovery from failures.

        Args:
            project_path: Path to the project directory
            context: Optional context metadata

        Returns:
            Analysis results
        """
        # Reset retry counter
        self.current_retry = 0

        # Ensure project_path is valid
        if project_path is None:
            if self.project_path is None:
                return {"error": "Project path is not set"}
            project_path = Path(self.project_path)
        else:
            # Update the instance variable
            self.project_path = str(project_path)

        logger.info(f"Executing analysis with recovery for project: {project_path}")

        while self.current_retry <= self.max_retries:
            try:
                # Prepare context wave
                context_wave = await self._prepare_context_wave(context)

                # Check instability score and decide on execution mode
                instability_score = self._calculate_instability_score()
                logger.info(f"Current system instability score: {instability_score}")

                if instability_score > self.instability_threshold and self.execution_mode == "parallel":
                    await self._switch_execution_mode("sequential")
                elif instability_score < self.stability_threshold and self.execution_mode == "sequential":
                    await self._switch_execution_mode("parallel")

                # Execute analysis based on current mode
                if self.execution_mode == "parallel":
                    # Create and send analysis message (parallel mode)
                    send_error = await self._create_and_send_analysis_message(project_path, context_wave)
                    if send_error:
                        raise RuntimeError(f"Error sending analysis message: {send_error}")

                    # Wait for analysis to complete
                    result = await self._wait_for_analysis_completion()
                else:
                    # Execute in sequential mode
                    result = await self._execute_analysis_sequential(project_path, context_wave)

                # Check result for error
                if isinstance(result, dict) and "error" in result:
                    error_msg = result.get("error", "Unknown error")
                    if error_msg:  # Only raise if there's an actual error message
                        raise RuntimeError(f"Analysis error: {error_msg}")

                # Success - reset consecutive failures
                self._record_success()

                return result

            except Exception as e:
                # Record failure
                self._record_failure(str(type(e).__name__))

                # Increment retry counter
                self.current_retry += 1

                if self.current_retry <= self.max_retries:
                    # Calculate retry delay with exponential backoff
                    retry_delay = self.retry_delay * (2 ** (self.current_retry - 1))

                    logger.warning(f"Analysis failed (attempt {self.current_retry}/{self.max_retries}): {e}")
                    logger.info(f"Retrying in {retry_delay} seconds...")

                    # Record recovery attempt
                    self._record_recovery_attempt()

                    # Switch to sequential mode if in parallel mode
                    if self.execution_mode == "parallel":
                        await self._switch_execution_mode("sequential")

                    # Wait before retrying
                    await asyncio.sleep(retry_delay)

                    # Reset the actor system
                    await self.stop_actors()
                    await self._initialize_actor_system()
                else:
                    # Max retries exceeded
                    logger.error(f"Max retries ({self.max_retries}) exceeded. Analysis failed: {e}")
                    import traceback
                    return {"error": str(e), "error_details": traceback.format_exc()}

        # The loop above will return in all cases, so this will never be reached
        # But we keep it to satisfy mypy
        logger.error("Unexpected condition: analysis failed after retries loop without returning")
        return {"error": "Analysis failed after retries"}

    async def analyze_project(self, project_path: Union[str, Path],
                             context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Start project analysis.

        This method coordinates the analysis of a project by:
        1. Validating the project path
        2. Initializing the actor system if needed
        3. Creating and sending the analysis message to the project actor
        4. Waiting for the analysis to complete

        Enhanced with adaptive execution mode and automatic recovery from failures.

        Args:
            project_path: Path to the project directory
            context: Optional context metadata to initialize the analysis

        Returns:
            Analysis results or error information
        """
        logger.info(f"Orchestrator.analyze_project called with project_path={project_path}")
        logger.info(f"context={context}")

        try:
            # Check if analysis is already running
            if self.is_running:
                logger.warning("Analysis is already running")
                return {"error": "Analysis is already running"}

            # Validate project path
            validation_error = await self._validate_project_path(project_path)
            if validation_error:
                return validation_error

            # Store the project path
            self.project_path = str(project_path)

            # Update output directory from context if provided
            if context and "output_dir" in context:
                self.output_dir = Path(context["output_dir"])
                logger.info(f"Updated output_dir from context to {self.output_dir}")

            # Initialize actor system
            init_error = await self._initialize_actor_system()
            if init_error:
                return init_error

            # Set running flag
            self.is_running = True

            # Execute analysis with recovery
            return await self._execute_analysis_with_recovery(Path(project_path), context)

        except Exception as e:
            import traceback
            error_details = traceback.format_exc()
            logger.error(f"Error during analysis: {e}\n{error_details}")
            return {"error": str(e), "error_details": error_details}
        finally:
            self.is_running = False
            logger.info("Set is_running to False")

    def _analysis_completed(self, results: Dict[str, Any]) -> None:
        """
        Callback when analysis is completed.

        Args:
            results: Analysis results
        """
        logger.info("Analysis completed callback received")

        try:
            # Record success
            self._record_success()

            # Set the result in the future if it exists and is not already done
            if self.analysis_complete is not None and not self.analysis_complete.done():
                self.analysis_complete.set_result(results)
                logger.info("Set analysis_complete future with results")
        except Exception as e:
            logger.error(f"Error in _analysis_completed: {e}")
            import traceback
            logger.error(traceback.format_exc())

    async def close(self) -> None:
        """Clean up the orchestrator and stop all actors."""
        try:
            logger.info("Closing orchestrator")

            # Cancel any running analysis task
            if self.is_running:
                if hasattr(self, 'analysis_task') and self.analysis_task is not None:
                    if not self.analysis_task.done():
                        logger.info("Cancelling running analysis task")
                        self.analysis_task.cancel()
                        try:
                            # Wait for the task to be cancelled with a timeout
                            await asyncio.wait_for(asyncio.shield(self.analysis_task), timeout=1.0)
                        except (asyncio.TimeoutError, asyncio.CancelledError):
                            # This is expected
                            pass
                        except Exception as e:
                            logger.error(f"Error when awaiting cancelled analysis task: {e}")
                            import traceback
                            logger.error(traceback.format_exc())
                        finally:
                            self.analysis_task = None
                            self.is_running = False

            # Stop all actors gracefully
            logger.info("Stopping all actors")
            await self.stop_actors()

            # Wait a moment for any pending tasks to complete
            await asyncio.sleep(0.1)

            logger.info("Orchestrator closed")

        except Exception as e:
            logger.error(f"Error during orchestrator cleanup: {e}")
            import traceback
            logger.error(traceback.format_exc())


# Singleton instance for easy access
_instance = None

def get_orchestrator(config_path: Optional[Union[str, Path]] = None, reset: bool = False) -> Orchestrator:
    """
    Get or create the singleton orchestrator instance.

    Args:
        config_path: Optional path to a configuration file
        reset: Whether to reset the singleton instance

    Returns:
        Orchestrator instance
    """
    global _instance
    # Create a new instance if none exists or if reset is requested
    if _instance is None or reset:  # type: ignore[unreachable]
        logger.info(f"Creating new Orchestrator instance (reset={reset})")
        _instance = Orchestrator(config_path)
    return _instance

def reset_orchestrator() -> None:
    """Reset the singleton orchestrator instance."""
    global _instance
    _instance = None

async def _analyze_project_async(project_path: Union[str, Path],
                            context: Optional[Dict[str, Any]] = None,
                            config_path: Optional[Union[str, Path]] = None) -> Any:
    """
    Async convenience function to analyze a project.

    Args:
        project_path: Path to the project directory
        context: Optional context metadata
        config_path: Optional configuration path

    Returns:
        ProjectMetrics object with analysis results
    """
    logger.info(f"_analyze_project_async called with project_path={project_path}")
    logger.info(f"context={context}, config_path={config_path}")

    try:
        # Reset the orchestrator to ensure a clean state
        logger.info("Resetting orchestrator")
        reset_orchestrator()

        # Get a new orchestrator instance
        logger.info("Getting new orchestrator instance")
        orchestrator = get_orchestrator(config_path)
        logger.info(f"Orchestrator created with config: {orchestrator.config}")

        # Run the analysis
        logger.info("Starting orchestrator.analyze_project")
        results = await orchestrator.analyze_project(project_path, context)
        logger.info(f"Analysis completed with results: {results}")

        # Clean up
        logger.info("Closing orchestrator")
        await orchestrator.close()

        # If results is a dictionary with a metrics key, process it
        if isinstance(results, dict) and "metrics" in results:
            metrics = results["metrics"]

            # Analyze trends if requested
            if context and context.get("analyze_trends", False):
                logger.info("Analyzing trends")
                from .trend_analysis import TrendAnalyzer, TrendStorage, TrendVisualizer

                # Create trend analyzer
                trend_storage = TrendStorage()
                trend_analyzer = TrendAnalyzer(trend_storage)

                # Get output directory
                output_dir = context.get("output_dir", "vibe_check_output")

                # Analyze trends
                trend_results = trend_analyzer.analyze_trends(metrics, output_dir=output_dir)

                # Generate trend visualizations if we have historical data
                if trend_results.get("has_historical_data", False):
                    logger.info("Generating trend visualizations")
                    trend_output_dir = os.path.join(output_dir, "trends")
                    trend_visualizer = TrendVisualizer(trend_output_dir)
                    visualization_paths = trend_visualizer.visualize_trends(trend_results, trend_output_dir)

                    # Add trend results and visualization paths to the metrics
                    metrics.trend_results = trend_results
                    metrics.trend_visualizations = visualization_paths

                    logger.info(f"Trend analysis completed with {len(visualization_paths)} visualizations")
                else:
                    logger.info("No historical data available for trend analysis")
                    metrics.trend_results = trend_results

            # Report progress if requested
            if context and context.get("report_progress", False):
                logger.info("Analyzing progress")
                from .trend_analysis import TrendAnalyzer, TrendStorage, TrendVisualizer

                # Create trend analyzer if not already created
                if 'trend_analyzer' not in locals():
                    trend_storage = TrendStorage()
                    trend_analyzer = TrendAnalyzer(trend_storage)

                # Get output directory
                output_dir = context.get("output_dir", "vibe_check_output")

                # Analyze progress for the output directory
                progress_results = trend_analyzer.analyze_output_dir_progress(metrics, output_dir)

                # Generate progress visualizations if we have progress data
                if progress_results.get("has_progress_data", False):
                    logger.info("Generating progress visualizations")
                    progress_output_dir = os.path.join(output_dir, "progress")

                    # Create trend visualizer if not already created
                    if 'trend_visualizer' not in locals():
                        trend_visualizer = TrendVisualizer(progress_output_dir)

                    visualization_paths = trend_visualizer.visualize_progress(progress_results, progress_output_dir)

                    # Add progress results and visualization paths to the metrics
                    metrics.progress_results = progress_results
                    metrics.progress_visualizations = visualization_paths

                    logger.info(f"Progress analysis completed with {len(visualization_paths)} visualizations")
                else:
                    logger.info("Not enough data to analyze progress")
                    metrics.progress_results = progress_results

            logger.info("Returning metrics from results")
            return metrics

        logger.info("Returning raw results")
        return results
    except Exception as e:
        import traceback
        logger.error(f"Error in _analyze_project_async: {e}")
        logger.error(traceback.format_exc())
        raise


def analyze_project(project_path: Union[str, Path],
                   config_path: Optional[Union[str, Path]] = None,
                   output_dir: Optional[Union[str, Path]] = None,
                   config_override: Optional[Dict[str, Any]] = None,
                   show_progress: bool = False,
                   analyze_trends: bool = False,
                   report_progress: bool = False,
                   **kwargs: Any) -> Any:
    """
    Synchronous convenience function to analyze a project.

    Args:
        project_path: Path to the project directory
        config_path: Optional path to a configuration file
        output_dir: Optional directory to write output files to
        config_override: Optional dictionary to override configuration values
        show_progress: Whether to show progress during analysis
        analyze_trends: Whether to analyze trends compared to previous runs
        report_progress: Whether to report progress between analyses with the same output directory
        **kwargs: Additional keyword arguments

    Returns:
        ProjectMetrics object with analysis results
    """
    logger.info(f"analyze_project called with project_path={project_path}")
    logger.info(f"config_path={config_path}, output_dir={output_dir}")
    logger.info(f"config_override={config_override}, show_progress={show_progress}")

    # Build context from kwargs
    context = kwargs.pop("context", {})

    # Add output_dir to context if provided
    if output_dir:
        context["output_dir"] = str(output_dir)

    # Add config_override to context if provided
    if config_override:
        context["config_override"] = config_override

    # Add show_progress to context
    context["show_progress"] = show_progress

    # Add analyze_trends to context
    context["analyze_trends"] = analyze_trends

    # Add report_progress to context
    context["report_progress"] = report_progress

    # Add any remaining kwargs to context
    context.update(kwargs)

    logger.info(f"Final context: {context}")

    # Run the async function in a new event loop
    import asyncio

    # Check if we're already in an event loop
    try:
        loop = asyncio.get_running_loop()
        logger.info("Using existing event loop")
        in_loop = True
    except RuntimeError:
        # No running event loop
        logger.info("Creating new event loop")
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        in_loop = False

    try:
        logger.info("Starting _analyze_project_async")
        result = None
        if in_loop:
            # We're already in an event loop, create a task
            task = asyncio.create_task(_analyze_project_async(project_path, context, config_path))
            result = asyncio.run_coroutine_threadsafe(task, loop).result()
        else:
            # We created a new loop, run until complete
            result = loop.run_until_complete(_analyze_project_async(project_path, context, config_path))

        logger.info(f"Analysis completed with result: {result}")
        return result
    except Exception as e:
        import traceback
        logger.error(f"Error in analyze_project: {e}")
        logger.error(traceback.format_exc())
        raise
    finally:
        # Only close the loop if we created it
        if not in_loop and loop.is_running():
            # Give pending tasks a chance to complete
            pending = asyncio.all_tasks(loop)
            if pending:
                logger.info(f"Waiting for {len(pending)} pending tasks to complete")
                loop.run_until_complete(asyncio.gather(*pending, return_exceptions=True))

            loop.close()
