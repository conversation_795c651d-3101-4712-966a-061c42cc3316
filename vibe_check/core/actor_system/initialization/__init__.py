"""
Initialization Package (DEPRECATED)
================================

This package provides compatibility layers for the legacy initialization components.
It is deprecated and will be removed in a future version.
Use ConsolidatedActorInitializer instead.
"""

import warnings

warnings.warn(
    "The initialization package is deprecated. Use ConsolidatedActorInitializer instead.",
    DeprecationWarning,
    stacklevel=2
)

from .manager import InitializationManager, InitializationPhase
from .manager_provider import get_manager, reset_manager

__all__ = [
    'InitializationManager',
    'InitializationPhase',
    'get_manager',
    'reset_manager'
]
