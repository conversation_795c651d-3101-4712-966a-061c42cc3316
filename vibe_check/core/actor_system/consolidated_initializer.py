"""
Consolidated Actor Initializer Module
================================

This module provides the ConsolidatedActorInitializer class, which manages the two-phase
initialization process for actors in the system. It ensures that all actors
are properly initialized before they start communicating with each other.

The initializer implements the CAW principle of choreographed interactions
by establishing explicit synchronization points during actor initialization.

This implementation consolidates the best features from the original ActorInitializer
and the refactored version, providing a unified approach to actor initialization.

This implementation addresses several issues with the previous actor system:
1. Lazy initialization to avoid event loop dependencies in constructors
2. Clear separation of registration and activation phases
3. Improved error handling and recovery mechanisms
4. More flexible synchronization through event-based communication
5. Circuit breaker pattern for resilience
6. State machine pattern for lifecycle management
7. Dependency injection integration for component resolution
8. Cycle detection during initialization to prevent deadlocks

Enhanced with:
- Robust error handling and propagation
- Comprehensive cleanup on initialization failure
- Thread-safe state transitions
- Rollback capability for partial initialization
- Configurable fail-fast option
- Simplified synchronization points
- Atomic registration operations
- Dependency injection integration
- Cycle detection during initialization
- Support for optional dependencies
"""

import asyncio
import logging
import threading
import time
import traceback
import uuid
from typing import Any, Callable, Dict, List, Optional, Set, Tuple, Type, TYPE_CHECKING, Union, cast

from .actor_state import ActorState, get_valid_transitions, VALID_STATE_TRANSITIONS
from .actor_state_machine import ActorStateMachine, StateTransitionContext
from .circuit_breaker import CircuitBreaker, CircuitBreakerOpenError
from .dependency_injection.container import DependencyContainer, CircularDependencyError, get_container
from .diagnostics import get_tracker, InitializationStep
from .event_bus import Event, EventBus, EventPriority, get_event_bus
from .exceptions import ActorDependencyError, ActorInitializationError, ActorTimeoutError, DependencyErrorType
from .synchronization import SynchronizationPoint, DummyEvent, DummyLock

if TYPE_CHECKING:
    from .actor import Actor

# Configure logging
logger = logging.getLogger("vibe_check_actor_initializer")


# We're now importing ActorInitializationError from .exceptions, so we don't need to define it here



class ConsolidatedActorInitializer:
    """
    Manages the two-phase initialization process for actors.

    This class ensures that all actors are properly initialized before they
    start communicating with each other, implementing explicit synchronization
    points during the actor initialization process.

    Enhanced with:
    - Robust error handling and propagation
    - Comprehensive cleanup on initialization failure
    - Thread-safe state transitions
    - Rollback capability for partial initialization
    - Configurable fail-fast option
    - Simplified synchronization points
    - Atomic registration operations
    - Dependency injection integration
    - Cycle detection during initialization
    - Support for optional dependencies
    """

    def __init__(self, fail_fast: bool = False, dependency_container: Optional[DependencyContainer] = None):
        """
        Initialize the actor initializer.

        This implementation uses lazy initialization for asyncio objects to avoid
        event loop dependencies in constructors.

        Args:
            fail_fast: If True, the initialization process will stop immediately
                      if any actor fails to initialize. If False, the process will
                      continue with other actors.
            dependency_container: Optional dependency container to use for resolving dependencies.
                                 If not provided, the default container will be used.
        """
        # Actor state tracking
        self._actor_states: Dict[str, ActorState] = {}
        self._actor_details: Dict[str, Dict[str, Any]] = {}
        self._initialization_errors: Dict[str, ActorInitializationError] = {}
        self._state_machines: Dict[str, ActorStateMachine] = {}

        # Synchronization - use lazy initialization
        self._initialization_events: Dict[str, Union[asyncio.Event, DummyEvent]] = {}
        self._readiness_events: Dict[str, Union[asyncio.Event, DummyEvent]] = {}
        self._global_ready_event: Optional[Union[asyncio.Event, DummyEvent]] = None
        self._sync_points_created: Optional[Union[asyncio.Event, DummyEvent]] = None

        # Dependencies
        self._dependencies: Dict[str, Set[str]] = {}
        self._optional_dependencies: Dict[str, Set[str]] = {}
        self._dependency_container = dependency_container

        # Resources and cleanup
        self._actor_resources: Dict[str, List[Tuple[Callable, List[Any], Dict[str, Any]]]] = {}

        # Locking - use lazy initialization
        self._initialization_lock: Optional[Union[asyncio.Lock, DummyLock]] = None
        self._state_lock = threading.RLock()  # Reentrant lock for thread safety (not asyncio)
        self._actor_locks: Dict[str, threading.RLock] = {}

        # Configuration
        self._fail_fast = fail_fast

        # Synchronization points
        self._sync_points: Dict[str, SynchronizationPoint] = {}
        self._sync_points_initialized = False

        # Event bus for coordination - will be lazily initialized
        self._event_bus: Optional[EventBus] = None

        # Circuit breakers for resilience
        self._circuit_breakers: Dict[str, CircuitBreaker] = {
            "initialize": CircuitBreaker(name="initialize_circuit_breaker", failure_threshold=3),
            "start": CircuitBreaker(name="start_circuit_breaker", failure_threshold=3),
            "dependency": CircuitBreaker(name="dependency_circuit_breaker", failure_threshold=5)
        }

        # Valid state transitions
        self._valid_state_transitions = VALID_STATE_TRANSITIONS

    @property
    def global_ready_event(self) -> Union[asyncio.Event, DummyEvent]:
        """
        Get the global ready event.

        Lazily initializes the event if it doesn't exist yet.

        Returns:
            The global ready event
        """
        if self._global_ready_event is None:
            try:
                self._global_ready_event = asyncio.Event()
            except RuntimeError:
                # No running event loop, create a dummy event
                logger.warning("No running event loop when creating global ready event")
                self._global_ready_event = DummyEvent()
        return cast(Union[asyncio.Event, DummyEvent], self._global_ready_event)

    @property
    def sync_points_created_event(self) -> Union[asyncio.Event, DummyEvent]:
        """
        Get the sync points created event.

        Lazily initializes the event if it doesn't exist yet.

        Returns:
            The sync points created event
        """
        if self._sync_points_created is None:
            try:
                self._sync_points_created = asyncio.Event()
            except RuntimeError:
                # No running event loop, create a dummy event
                logger.warning("No running event loop when creating sync points created event")
                self._sync_points_created = DummyEvent()
        return cast(Union[asyncio.Event, DummyEvent], self._sync_points_created)

    @property
    def initialization_lock(self) -> Union[asyncio.Lock, DummyLock]:
        """
        Get the initialization lock.

        Lazily initializes the lock if it doesn't exist yet.

        Returns:
            The initialization lock
        """
        if self._initialization_lock is None:
            try:
                self._initialization_lock = asyncio.Lock()
            except RuntimeError:
                # No running event loop, create a dummy lock
                logger.warning("No running event loop when creating initialization lock")
                self._initialization_lock = DummyLock()
        return cast(Union[asyncio.Lock, DummyLock], self._initialization_lock)

    @property
    def event_bus(self) -> EventBus:
        """
        Get the event bus.

        Lazily initializes the event bus if it doesn't exist yet.

        Returns:
            The event bus
        """
        if self._event_bus is None:
            self._event_bus = get_event_bus(keep_history=True)
        return self._event_bus

    @property
    def dependency_container(self) -> DependencyContainer:
        """
        Get the dependency container.

        Lazily initializes the dependency container if it doesn't exist yet.

        Returns:
            The dependency container
        """
        if self._dependency_container is None:
            self._dependency_container = get_container()
        return self._dependency_container

    async def _create_sync_points(self) -> None:
        """
        Create the synchronization points for the initialization process.

        This implementation uses a more comprehensive synchronization model:
        1. "registration_complete" - Actors have registered with the system
        2. "dependency_resolution_complete" - All dependencies have been resolved
        3. "initialization_complete" - Actors have completed initialization
        4. "start_complete" - Actors have completed the start phase
        5. "system_ready" - The entire system is ready

        The synchronization points are created with auto_complete=False for production use.
        """
        try:
            # Create comprehensive synchronization points
            self._sync_points["registration_complete"] = SynchronizationPoint(
                name="registration_complete",
                auto_complete=False
            )
            self._sync_points["dependency_resolution_complete"] = SynchronizationPoint(
                name="dependency_resolution_complete",
                auto_complete=False
            )
            self._sync_points["initialization_complete"] = SynchronizationPoint(
                name="initialization_complete",
                auto_complete=False
            )
            self._sync_points["start_complete"] = SynchronizationPoint(
                name="start_complete",
                auto_complete=False
            )
            self._sync_points["system_ready"] = SynchronizationPoint(
                name="system_ready",
                auto_complete=False
            )

            # Set the event to indicate that sync points are created
            try:
                self.sync_points_created_event.set()
            except RuntimeError:
                # No running event loop, just log the event
                logger.warning("No running event loop when setting sync_points_created event")

            logger.info("Comprehensive synchronization points created successfully")
        except Exception as e:
            logger.error(f"Error creating synchronization points: {e}")
            logger.error(traceback.format_exc())
            raise

    async def is_actor_registered(self, actor_id: str) -> bool:
        """
        Check if an actor is registered with the initializer.

        Args:
            actor_id: ID of the actor to check

        Returns:
            True if the actor is registered, False otherwise
        """
        with self._state_lock:
            return actor_id in self._actor_states

    async def register_actor(self, actor_id: str, actor_type: Optional[str] = None,
                           tags: Optional[Set[str]] = None) -> None:
        """
        Register an actor with the initializer.

        This method is asynchronous to ensure proper synchronization
        and to allow for immediate state setting. It implements atomic
        registration operations using proper locking.

        Args:
            actor_id: ID of the actor to register
            actor_type: Optional type of the actor
            tags: Optional set of tags for the actor

        Raises:
            RuntimeError: If synchronization points creation times out
        """
        # Lazily initialize synchronization points if not already done
        if not self._sync_points_initialized:
            await self._create_sync_points()
            self._sync_points_initialized = True

        # Use the initialization lock for the entire registration process
        async with self.initialization_lock:
            # Check if actor is already registered
            with self._state_lock:
                if actor_id in self._actor_states:
                    logger.warning(f"Actor {actor_id} already registered with initializer")
                    return

                # Create a lock for this actor
                self._actor_locks[actor_id] = threading.RLock()

                # Initialize actor state and related data
                self._actor_states[actor_id] = ActorState.CREATED
                self._initialization_events[actor_id] = asyncio.Event()
                self._readiness_events[actor_id] = asyncio.Event()
                self._dependencies[actor_id] = set()
                self._actor_resources[actor_id] = []

                # Create a state machine for this actor
                state_machine = ActorStateMachine(actor_id, initial_state=ActorState.CREATED)

                # Add hooks for state transitions
                state_machine.add_state_entry_hook(ActorState.INITIALIZED,
                                                  lambda ctx: self._initialization_events[actor_id].set())
                state_machine.add_state_entry_hook(ActorState.READY,
                                                  lambda ctx: self._readiness_events[actor_id].set())

                # Store the state machine
                self._state_machines[actor_id] = state_machine

                # Store actor details
                self._actor_details[actor_id] = {
                    "actor_id": actor_id,
                    "actor_type": actor_type,
                    "tags": tags or set(),
                    "registered_at": time.time(),
                    "last_state_change": time.time(),
                    "initialization_time": None,
                    "start_time": None,
                    "ready_time": None
                }

                logger.info(f"Registered actor {actor_id} with initializer")

                # Record registration event with the tracker
                tracker = get_tracker()
                if tracker:
                    asyncio.create_task(tracker.record_event(
                        actor_id=actor_id,
                        step=InitializationStep.REGISTRATION,
                        details={"state": ActorState.CREATED.value}
                    ))

                # Notify the registration synchronization point
                sync_point = self._sync_points.get("registration_complete")
                if sync_point:
                    await sync_point.reach(actor_id)

    def _detect_dependency_cycle(self, actor_id: str, dependency_id: str) -> Optional[list]:
        """
        Detect if adding a dependency would create a cycle.

        Args:
            actor_id: ID of the dependent actor
            dependency_id: ID of the dependency actor

        Returns:
            Optional[list]: The cycle path if a cycle is detected, None otherwise
        """
        # If the dependency already depends on the actor, we have a direct cycle
        if dependency_id in self._dependencies and actor_id in self._dependencies[dependency_id]:
            return [actor_id, dependency_id, actor_id]

        # Check for indirect cycles using depth-first search
        visited = set()
        path = []

        def dfs(current: str, target: str) -> bool:
            if current == target:
                path.append(current)
                return True

            if current in visited:
                return False

            visited.add(current)
            path.append(current)

            # Check all dependencies of the current actor
            if current in self._dependencies:
                for dep in self._dependencies[current]:
                    if dfs(dep, target):
                        return True

            path.pop()
            return False

        # Start DFS from the dependency to see if it can reach the actor
        if dfs(dependency_id, actor_id):
            # Add the actor to complete the cycle
            path.append(actor_id)
            return path

        return None

    async def register_dependency(self, actor_id: str, dependency_id: str, optional: bool = False) -> None:
        """
        Register a dependency between actors.

        Args:
            actor_id: ID of the dependent actor
            dependency_id: ID of the actor that must be ready first
            optional: If True, the dependency is considered optional and the actor
                     can initialize even if the dependency is not available

        Raises:
            ValueError: If either actor is not registered
            ActorDependencyError: If adding the dependency would create a cycle
        """
        with self._state_lock:
            if actor_id not in self._actor_states:
                raise ValueError(f"Cannot register dependency: Actor {actor_id} not registered")

            if dependency_id not in self._actor_states:
                raise ValueError(f"Cannot register dependency: Dependency {dependency_id} not registered")

            # Initialize dependency sets if needed
            if actor_id not in self._dependencies:
                self._dependencies[actor_id] = set()

            if actor_id not in self._optional_dependencies:
                self._optional_dependencies[actor_id] = set()

            # If this is an optional dependency, add it to the optional dependencies set
            if optional:
                self._optional_dependencies[actor_id].add(dependency_id)
                logger.info(f"Registered optional dependency: {actor_id} depends on {dependency_id} (optional)")

                # Record dependency with the tracker
                tracker = get_tracker()
                if tracker:
                    await tracker.record_dependency(actor_id, dependency_id)
                    await tracker.record_event(
                        actor_id=actor_id,
                        step=InitializationStep.DEPENDENCY_CHECK,
                        details={"dependency_id": dependency_id, "optional": True}
                    )
                return

            # For required dependencies, check for cycles
            # First, check using our internal cycle detection
            cycle_path = self._detect_dependency_cycle(actor_id, dependency_id)
            if cycle_path:
                error_msg = f"Dependency cycle detected: {' -> '.join(cycle_path)}"
                logger.error(error_msg)
                raise ActorDependencyError(
                    message=error_msg,
                    actor_id=actor_id,
                    dependencies={dependency_id: "Would create a dependency cycle"},
                    error_type=DependencyErrorType.CYCLE,
                    cycle_path=cycle_path
                )

            # Then, use the DI container's cycle detection for more comprehensive analysis
            try:
                # Add the dependency to the container's graph for cycle detection
                container = self.dependency_container

                # Create a temporary copy of the dependency graph
                temp_graph = {k: set(v) for k, v in self._dependencies.items()}

                # Add the new dependency
                if actor_id not in temp_graph:
                    temp_graph[actor_id] = set()
                temp_graph[actor_id].add(dependency_id)

                # Update the container's graph with our temporary graph
                container._dependency_graph = temp_graph

                # Detect cycles
                cycles = container.detect_cycles()

                # If cycles are detected, raise an error
                if cycles:
                    cycle_str = " -> ".join(cycles[0]) + " -> " + cycles[0][0]
                    error_msg = f"Dependency cycle detected: {cycle_str}"
                    logger.error(error_msg)
                    raise ActorDependencyError(
                        message=error_msg,
                        actor_id=actor_id,
                        dependencies={dependency_id: "Would create a dependency cycle"},
                        error_type=DependencyErrorType.CYCLE,
                        cycle_path=cycles[0]
                    )
            except Exception as e:
                if isinstance(e, ActorDependencyError):
                    raise
                logger.warning(f"Error during advanced cycle detection: {e}. Falling back to basic cycle detection.")

            # If no cycles were detected, add the dependency
            self._dependencies[actor_id].add(dependency_id)
            logger.info(f"Registered dependency: {actor_id} depends on {dependency_id}")

            # Record dependency with the tracker
            tracker = get_tracker()
            if tracker:
                await tracker.record_dependency(actor_id, dependency_id)
                await tracker.record_event(
                    actor_id=actor_id,
                    step=InitializationStep.DEPENDENCY_CHECK,
                    details={"dependency_id": dependency_id, "optional": False}
                )

    def register_resource(self, actor_id: str, cleanup_func: Callable,
                         args: Optional[List[Any]] = None, kwargs: Optional[Dict[str, Any]] = None) -> None:
        """
        Register a cleanup resource for an actor.

        Args:
            actor_id: ID of the actor
            cleanup_func: Function to call when cleaning up the actor
            args: Positional arguments for the cleanup function
            kwargs: Keyword arguments for the cleanup function

        Raises:
            ValueError: If the actor is not registered
        """
        with self._state_lock:
            if actor_id not in self._actor_states:
                raise ValueError(f"Cannot register resource: Actor {actor_id} not registered")

            self._actor_resources[actor_id].append((cleanup_func, args or [], kwargs or {}))
            logger.debug(f"Registered cleanup resource for actor {actor_id}")

    async def set_actor_state(self, actor_id: str, state: ActorState,
                             error: Optional[Exception] = None,
                             phase: Optional[str] = None,
                             metadata: Optional[Dict[str, Any]] = None,
                             force: bool = False) -> None:
        """
        Set the state of an actor with enhanced error handling and state validation.

        This method uses the ActorStateMachine to manage state transitions, providing
        more robust state management and validation.

        Args:
            actor_id: ID of the actor
            state: New state for the actor
            error: Optional exception that caused a state change to FAILED
            phase: Optional phase name for error context
            metadata: Optional additional metadata for the transition
            force: If True, force the transition even if it's invalid

        Raises:
            ValueError: If the actor is not registered
            ActorInitializationError: If fail_fast is True and the actor failed to initialize
        """
        # Check if actor is registered
        if not await self.is_actor_registered(actor_id):
            raise ValueError(f"Cannot set state: Actor {actor_id} not registered")

        # Get the actor lock
        actor_lock = self._actor_locks.get(actor_id)
        if not actor_lock:
            # Create a lock if it doesn't exist
            with self._state_lock:
                self._actor_locks[actor_id] = threading.RLock()
                actor_lock = self._actor_locks[actor_id]

        # Use the actor lock for state transition
        with actor_lock:
            # Get the current state and state machine
            with self._state_lock:
                old_state = self._actor_states[actor_id]
                state_machine = self._state_machines.get(actor_id)

                if not state_machine:
                    # Create a state machine if it doesn't exist (should not happen)
                    logger.warning(f"State machine for actor {actor_id} not found, creating one")
                    state_machine = ActorStateMachine(actor_id, initial_state=old_state)
                    self._state_machines[actor_id] = state_machine

                # Check if the actor is already in the target state
                if old_state == state and state not in [ActorState.CREATED, ActorState.STOPPED]:
                    logger.debug(f"Actor {actor_id} is already in state {state.value}, no change needed")
                    return

                # Prepare metadata for the transition
                transition_metadata = metadata or {}
                transition_metadata.update({
                    "timestamp": time.time(),
                    "phase": phase or "unknown"
                })

                # Use the state machine to perform the transition
                try:
                    # Force transitions to FAILED or STOPPED states
                    transition_force = force or state in [ActorState.FAILED, ActorState.STOPPED]

                    # Perform the transition
                    await state_machine.transition_to(
                        state,
                        phase=phase,
                        error=error,
                        metadata=transition_metadata,
                        force=transition_force
                    )

                    # Update our internal state tracking
                    self._actor_states[actor_id] = state
                    self._actor_details[actor_id]["last_state_change"] = time.time()

                    # Update timing information
                    if state == ActorState.INITIALIZED:
                        self._actor_details[actor_id]["initialization_time"] = time.time()
                    elif state == ActorState.STARTING:
                        self._actor_details[actor_id]["start_time"] = time.time()
                    elif state == ActorState.READY:
                        self._actor_details[actor_id]["ready_time"] = time.time()

                    logger.info(f"Actor {actor_id} state changed: {old_state.value} -> {state.value}")

                    # Publish state change event
                    await self.event_bus.publish_simple(
                        event_type=f"actor.state_change.{actor_id}",
                        data={
                            "actor_id": actor_id,
                            "old_state": old_state.value,
                            "new_state": state.value,
                            "phase": phase or "unknown",
                            "timestamp": time.time()
                        },
                        source="initializer"
                    )

                    # Record state change with the tracker
                    tracker = get_tracker()
                    if tracker:
                        # Map actor state to initialization step
                        step = None
                        if state == ActorState.INITIALIZING:
                            step = InitializationStep.INITIALIZATION_START
                        elif state == ActorState.INITIALIZED:
                            step = InitializationStep.INITIALIZATION_COMPLETE
                        elif state == ActorState.STARTING:
                            step = InitializationStep.START_BEGIN
                        elif state == ActorState.READY:
                            step = InitializationStep.READY
                        elif state == ActorState.FAILED:
                            step = InitializationStep.FAILED

                        if step:
                            details = {
                                "old_state": old_state.value,
                                "new_state": state.value,
                                "phase": phase or "unknown"
                            }
                            asyncio.create_task(tracker.record_event(
                                actor_id=actor_id,
                                step=step,
                                details=details,
                                error=error
                            ))

                    # Handle FAILED state
                    if state == ActorState.FAILED:
                        # Create initialization error
                        error_msg = "Unknown error" if error is None else str(error)
                        phase_name = phase or "unknown"

                        # Create initialization error
                        init_error = ActorInitializationError(
                            message=error_msg,
                            actor_id=actor_id,
                            phase=phase_name,
                            state=old_state,
                            original_error=error
                        )

                        # Store the error
                        self._initialization_errors[actor_id] = init_error
                        logger.error(f"Actor {actor_id} failed: {init_error}")

                        # Publish error event
                        await self.event_bus.publish_simple(
                            event_type=f"actor.error.{actor_id}",
                            data={
                                "actor_id": actor_id,
                                "phase": phase_name,
                                "state": old_state.value,
                                "message": error_msg,
                                "original_error": str(error) if error else None,
                                "timestamp": time.time(),
                                "error_id": str(uuid.uuid4())
                            },
                            source="initializer",
                            priority=EventPriority.HIGH
                        )

                        # If fail_fast is enabled, raise the error
                        if self._fail_fast:
                            raise init_error

                    # Notify the appropriate synchronization point based on state
                    if state == ActorState.INITIALIZED:
                        sync_point = self._sync_points.get("initialization_complete")
                        if sync_point:
                            await sync_point.reach(actor_id)
                            # Check if all actors have reached this point
                            await self._check_sync_point_completion("initialization_complete", ActorState.INITIALIZED)

                    elif state == ActorState.STARTING:
                        # For STARTING state, notify the dependency_resolution_complete point
                        # since dependencies are checked before starting
                        sync_point = self._sync_points.get("dependency_resolution_complete")
                        if sync_point:
                            await sync_point.reach(actor_id)
                            # Check if all actors have reached this point
                            await self._check_sync_point_completion("dependency_resolution_complete", ActorState.STARTING)

                    elif state == ActorState.READY:
                        # For READY state, notify the start_complete point
                        sync_point = self._sync_points.get("start_complete")
                        if sync_point:
                            await sync_point.reach(actor_id)
                            # Check if all actors have reached this point
                            await self._check_sync_point_completion("start_complete", ActorState.READY)

                        # Also check global readiness
                        await self._check_global_readiness()

                except Exception as e:
                    logger.error(f"Error during state transition for actor {actor_id}: {e}")
                    logger.error(traceback.format_exc())

                    # If this was already a transition to FAILED, don't try again
                    if state != ActorState.FAILED:
                        # Try to transition to FAILED state
                        await self.set_actor_state(
                            actor_id=actor_id,
                            state=ActorState.FAILED,
                            error=e,
                            phase=f"state_transition_{phase or 'unknown'}",
                            force=True
                        )

    async def _check_sync_point_completion(self, sync_point_name: str, required_state: ActorState) -> None:
        """
        Check if all actors have reached a specific synchronization point and complete it if they have.

        Args:
            sync_point_name: Name of the synchronization point to check
            required_state: The state that actors must be in (or beyond) to be considered as having reached the point
        """
        with self._state_lock:
            # Get the synchronization point
            sync_point = self._sync_points.get(sync_point_name)
            if not sync_point or sync_point.is_complete():
                return

            # Get all registered actors
            all_actor_ids = set(self._actor_states.keys())

            # Check if any actors have failed
            failed_actors = [actor_id for actor_id, state in self._actor_states.items()
                            if state == ActorState.FAILED]

            if failed_actors and self._fail_fast:
                logger.warning(f"Cannot complete sync point {sync_point_name}: {len(failed_actors)} actors have failed: {failed_actors}")
                return

            # Get actors that have reached the sync point
            reached_actors = sync_point.reached_actors

            # Get actors that have reached the required state
            state_reached_actors = set()
            for actor_id, state in self._actor_states.items():
                # Check if the actor has reached or passed the required state
                # We consider an actor to have passed a state if it's in a later state in the lifecycle
                # For example, if required_state is INITIALIZED, then STARTING and READY also qualify
                if state == ActorState.FAILED:
                    # Failed actors don't count unless we're in fail_fast mode
                    if self._fail_fast:
                        return
                    continue

                if state.value >= required_state.value:
                    state_reached_actors.add(actor_id)

            # Check if all non-failed actors have reached the sync point and the required state
            remaining_actors = all_actor_ids - reached_actors - set(failed_actors)
            remaining_state = all_actor_ids - state_reached_actors - set(failed_actors)

            if remaining_actors or remaining_state:
                # Some actors haven't reached the sync point or the required state yet
                logger.debug(f"Sync point {sync_point_name} not complete yet. "
                           f"Waiting for actors to reach point: {remaining_actors}, "
                           f"waiting for actors to reach state {required_state.value}: {remaining_state}")
                return

            # All actors have reached the sync point and the required state, complete it
            logger.info(f"All actors have reached sync point {sync_point_name} and state {required_state.value}")
            sync_point.complete()

            # Record this event with the tracker
            tracker = get_tracker()
            if tracker:
                asyncio.create_task(tracker.record_event(
                    actor_id="system",
                    step=InitializationStep.DEPENDENCY_CHECK,  # Using DEPENDENCY_CHECK as a substitute for SYNCHRONIZATION
                    details={"message": f"Sync point {sync_point_name} completed",
                             "state": required_state.value}
                ))

            # Publish event
            await self.event_bus.publish_simple(
                event_type=f"sync_point.complete.{sync_point_name}",
                data={
                    "sync_point": sync_point_name,
                    "required_state": required_state.value,
                    "timestamp": time.time()
                },
                source="initializer"
            )

    async def check_actor_readiness(self, actor_id: str) -> bool:
        """
        Check if an actor is ready by calling its check_readiness method.

        Args:
            actor_id: ID of the actor to check

        Returns:
            bool: True if the actor is ready, False otherwise
        """
        # Get the actor from the registry
        try:
            from .actor_registry import get_registry
            registry = get_registry()
            actor = registry.get_actor(actor_id)

            if not actor:
                logger.warning(f"Cannot check readiness: Actor {actor_id} not found in registry")
                return False

            # Check if the actor has a check_readiness method
            if hasattr(actor, 'check_readiness') and callable(getattr(actor, 'check_readiness')):
                try:
                    # Call the check_readiness method
                    is_ready = await actor.check_readiness()

                    if not is_ready:
                        logger.warning(f"Actor {actor_id} reports it is not ready")
                        return False

                    return True
                except Exception as e:
                    logger.error(f"Error checking readiness of actor {actor_id}: {e}")
                    return False
            else:
                # If the actor doesn't have a check_readiness method, check its state
                with self._state_lock:
                    state = self._actor_states.get(actor_id)
                    if state != ActorState.READY:
                        logger.warning(f"Actor {actor_id} is not in READY state: {state.value if state else 'None'}")
                        return False

                return True
        except Exception as e:
            logger.error(f"Error checking readiness of actor {actor_id}: {e}")
            return False

    async def _check_global_readiness(self) -> None:
        """Check if all actors are ready and set the global ready event if they are."""
        with self._state_lock:
            # Check if any actors have failed
            failed_actors = [actor_id for actor_id, state in self._actor_states.items()
                            if state == ActorState.FAILED]

            if failed_actors:
                logger.warning(f"Cannot set global ready event: {len(failed_actors)} actors have failed: {failed_actors}")

                # Log detailed information about failed actors
                for actor_id in failed_actors:
                    if actor_id in self._initialization_errors:
                        error = self._initialization_errors[actor_id]
                        logger.error(f"Actor {actor_id} failed during {error.phase} phase: {error}")
                return

            # Check if all actors are ready
            all_ready = all(
                state == ActorState.READY for state in self._actor_states.values()
            )

            # Log the current state of all actors
            if not all_ready:
                not_ready = [(actor_id, state.value) for actor_id, state in self._actor_states.items()
                            if state != ActorState.READY]
                logger.debug(f"Not all actors are ready yet. Waiting for: {not_ready}")

            if all_ready and not self.global_ready_event.is_set():
                # Perform additional readiness checks on all actors
                all_actors_ready = True
                readiness_failures = []

                for actor_id in self._actor_states.keys():
                    if not await self.check_actor_readiness(actor_id):
                        all_actors_ready = False
                        readiness_failures.append(actor_id)

                if not all_actors_ready:
                    logger.warning(f"Cannot set global ready event: {len(readiness_failures)} actors failed readiness check: {readiness_failures}")
                    return

                logger.info("All actors are ready")
                self.global_ready_event.set()

                # Complete the system_ready synchronization point
                sync_point = self._sync_points.get("system_ready")
                if sync_point:
                    sync_point.complete()

                # Record this event with the tracker
                tracker = get_tracker()
                if tracker:
                    asyncio.create_task(tracker.record_event(
                        actor_id="system",
                        step=InitializationStep.READY,
                        details={"message": "All actors are ready"}
                    ))

    async def wait_for_actor_initialized(self, actor_id: str, timeout: Optional[float] = None) -> bool:
        """
        Wait for an actor to be initialized.

        Args:
            actor_id: ID of the actor to wait for
            timeout: Optional timeout in seconds

        Returns:
            True if the actor was initialized, False if timed out

        Raises:
            ValueError: If the actor is not registered
        """
        # Check if actor is registered
        if not await self.is_actor_registered(actor_id):
            raise ValueError(f"Cannot wait for initialization: Actor {actor_id} not registered")

        # Get the initialization event
        event = self._initialization_events.get(actor_id)
        if not event:
            raise ValueError(f"No initialization event found for actor {actor_id}")

        # Wait for the event
        try:
            if timeout is not None:
                return await asyncio.wait_for(event.wait(), timeout=timeout)
            else:
                await event.wait()
                return True
        except asyncio.TimeoutError:
            logger.warning(f"Timeout waiting for actor {actor_id} to initialize")
            return False

    async def wait_for_actor_ready(self, actor_id: str, timeout: Optional[float] = None,
                                  verify_readiness: bool = True) -> bool:
        """
        Wait for an actor to be ready.

        This method waits for the actor to reach the READY state and optionally
        verifies its readiness by calling its check_readiness method.

        Args:
            actor_id: ID of the actor to wait for
            timeout: Optional timeout in seconds
            verify_readiness: If True, call the actor's check_readiness method after it's in READY state

        Returns:
            True if the actor is ready, False if timed out or readiness check failed

        Raises:
            ValueError: If the actor is not registered
            ActorInitializationError: If the actor is in FAILED state
        """
        # Check if actor is registered
        if not await self.is_actor_registered(actor_id):
            raise ValueError(f"Cannot wait for readiness: Actor {actor_id} not registered")

        # Check if the actor is in FAILED state
        with self._state_lock:
            state = self._actor_states.get(actor_id)
            if state == ActorState.FAILED:
                error = self._initialization_errors.get(actor_id)
                if error:
                    raise error
                raise ActorInitializationError(
                    message=f"Actor {actor_id} is in FAILED state",
                    actor_id=actor_id,
                    phase="unknown",
                    state=ActorState.FAILED,
                    original_error=None
                )

        # Get the readiness event
        event = self._readiness_events.get(actor_id)
        if not event:
            raise ValueError(f"No readiness event found for actor {actor_id}")

        # Check if the event is already set
        if event.is_set():
            # If the event is set, the actor is in READY state, but we still need to verify its readiness
            if verify_readiness:
                return await self.check_actor_readiness(actor_id)
            return True

        # Wait for the event
        try:
            if timeout is not None:
                await asyncio.wait_for(event.wait(), timeout=timeout)
            else:
                await event.wait()

            # Verify readiness if requested
            if verify_readiness:
                return await self.check_actor_readiness(actor_id)
            return True
        except asyncio.TimeoutError:
            logger.warning(f"Timeout waiting for actor {actor_id} to be ready")
            return False

    async def wait_for_all_ready(self, timeout: Optional[float] = None,
                               verify_readiness: bool = True) -> bool:
        """
        Wait for all actors to be ready.

        This method waits for all actors to reach the READY state and optionally
        verifies their readiness by calling their check_readiness methods.

        Args:
            timeout: Optional timeout in seconds
            verify_readiness: If True, verify readiness of all actors after global ready event is set

        Returns:
            True if all actors are ready, False if timed out or readiness check failed

        Raises:
            ActorInitializationError: If any actor is in FAILED state and fail_fast is True
        """
        # First check if any actors have failed
        with self._state_lock:
            failed_actors = [actor_id for actor_id, state in self._actor_states.items()
                           if state == ActorState.FAILED]

            if failed_actors and self._fail_fast:
                # Get the first failed actor's error
                actor_id = failed_actors[0]
                error = self._initialization_errors.get(actor_id)
                if error:
                    raise error
                raise ActorInitializationError(
                    message=f"Actor {actor_id} is in FAILED state",
                    actor_id=actor_id,
                    phase="unknown",
                    state=ActorState.FAILED,
                    original_error=None
                )

        # Wait for the global ready event
        try:
            if timeout is not None:
                await asyncio.wait_for(self.global_ready_event.wait(), timeout=timeout)
            else:
                await self.global_ready_event.wait()

            # Verify readiness of all actors if requested
            if verify_readiness:
                with self._state_lock:
                    actor_ids = list(self._actor_states.keys())

                # Check readiness of each actor
                for actor_id in actor_ids:
                    if not await self.check_actor_readiness(actor_id):
                        logger.warning(f"Actor {actor_id} reports it is not ready after global ready event was set")
                        return False

            return True
        except asyncio.TimeoutError:
            logger.warning("Timeout waiting for all actors to be ready")
            return False

    async def wait_for_dependencies(self, actor_id: str, timeout: Optional[float] = None) -> bool:
        """
        Wait for an actor's dependencies to be ready.

        Enhanced with better timeout handling, detailed error reporting, and
        support for dependency prioritization.

        Args:
            actor_id: ID of the actor
            timeout: Optional timeout in seconds

        Returns:
            True if all dependencies are ready, False if timed out

        Raises:
            ValueError: If the actor is not registered
            ActorDependencyError: If a dependency has failed
        """
        # Check if actor is registered
        if not await self.is_actor_registered(actor_id):
            raise ValueError(f"Cannot wait for dependencies: Actor {actor_id} not registered")

        # Get the actor's dependencies
        with self._state_lock:
            dependencies = self._dependencies.get(actor_id, set())
            optional_dependencies = self._optional_dependencies.get(actor_id, set())

        if not dependencies and not optional_dependencies:
            logger.debug(f"Actor {actor_id} has no dependencies, returning immediately")

            # Notify the dependency resolution synchronization point
            sync_point = self._sync_points.get("dependency_resolution_complete")
            if sync_point:
                await sync_point.reach(actor_id)
                # Check if all actors have reached this point
                await self._check_sync_point_completion("dependency_resolution_complete", ActorState.STARTING)

            return True

        # Log information about dependencies
        if dependencies:
            logger.debug(f"Actor {actor_id} has {len(dependencies)} required dependencies: {dependencies}")
        if optional_dependencies:
            logger.debug(f"Actor {actor_id} has {len(optional_dependencies)} optional dependencies: {optional_dependencies}")

        # Record dependency check start with the tracker
        tracker = get_tracker()
        if tracker:
            asyncio.create_task(tracker.record_event(
                actor_id=actor_id,
                step=InitializationStep.DEPENDENCY_CHECK,
                details={
                    "required_dependencies": list(dependencies),
                    "optional_dependencies": list(optional_dependencies),
                    "action": "start"
                }
            ))

        # Publish dependency check start event
        await self.event_bus.publish_simple(
            event_type=f"actor.dependency_check.start.{actor_id}",
            data={
                "actor_id": actor_id,
                "required_dependencies": list(dependencies),
                "optional_dependencies": list(optional_dependencies),
                "timestamp": time.time()
            },
            source="initializer"
        )

        # Wait for each dependency to be ready
        start_time = time.time()
        remaining_timeout = timeout
        dependency_errors = {}
        timed_out_dependencies = set()

        # Process required dependencies first
        # First check if any required dependencies have already failed
        for dependency_id in dependencies:
            with self._state_lock:
                state = self._actor_states.get(dependency_id)

            if state == ActorState.FAILED:
                error_msg = f"Required dependency {dependency_id} has failed"
                if dependency_id in self._initialization_errors:
                    error = self._initialization_errors[dependency_id]
                    error_msg = f"{error_msg}: {error}"

                logger.error(error_msg)
                dependency_errors[dependency_id] = error_msg

        # If any required dependencies have already failed, raise an error in fail-fast mode
        if dependency_errors and self._fail_fast:
            raise ActorDependencyError(
                message=f"Cannot wait for dependencies: Some required dependencies have failed",
                actor_id=actor_id,
                dependencies=dependency_errors
            )

        # Wait for each required dependency to be ready
        for dependency_id in dependencies:
            # Skip dependencies that have already failed
            if dependency_id in dependency_errors:
                continue

            # Calculate remaining timeout if a timeout was specified
            if timeout is not None:
                elapsed = time.time() - start_time
                remaining_timeout = max(0.0, timeout - elapsed)

                if remaining_timeout <= 0:
                    logger.warning(f"Timeout waiting for required dependency {dependency_id} of actor {actor_id}")
                    timed_out_dependencies.add(dependency_id)
                    continue

            # Wait for the dependency to be ready and verify its readiness
            logger.debug(f"Waiting for required dependency {dependency_id} of actor {actor_id}")
            try:
                if not await self.wait_for_actor_ready(dependency_id, remaining_timeout, verify_readiness=True):
                    timed_out_dependencies.add(dependency_id)
                    logger.warning(f"Timeout or readiness check failed for required dependency {dependency_id} of actor {actor_id}")
            except Exception as e:
                error_msg = f"Error waiting for required dependency {dependency_id}: {str(e)}"
                logger.error(error_msg)
                dependency_errors[dependency_id] = error_msg

        # Now process optional dependencies
        optional_dependency_errors = {}
        optional_timed_out_dependencies = set()

        # Check if any optional dependencies have already failed
        for dependency_id in optional_dependencies:
            with self._state_lock:
                state = self._actor_states.get(dependency_id)

            if state == ActorState.FAILED:
                error_msg = f"Optional dependency {dependency_id} has failed"
                if dependency_id in self._initialization_errors:
                    error = self._initialization_errors[dependency_id]
                    error_msg = f"{error_msg}: {error}"

                logger.warning(error_msg)  # Only warning for optional dependencies
                optional_dependency_errors[dependency_id] = error_msg

        # Wait for each optional dependency to be ready
        for dependency_id in optional_dependencies:
            # Skip dependencies that have already failed
            if dependency_id in optional_dependency_errors:
                continue

            # Calculate remaining timeout if a timeout was specified
            if timeout is not None:
                elapsed = time.time() - start_time
                remaining_timeout = max(0.0, timeout - elapsed)

                if remaining_timeout <= 0:
                    logger.warning(f"Timeout waiting for optional dependency {dependency_id} of actor {actor_id}")
                    optional_timed_out_dependencies.add(dependency_id)
                    continue

            # Wait for the dependency to be ready and verify its readiness
            logger.debug(f"Waiting for optional dependency {dependency_id} of actor {actor_id}")
            try:
                if not await self.wait_for_actor_ready(dependency_id, remaining_timeout, verify_readiness=True):
                    optional_timed_out_dependencies.add(dependency_id)
                    logger.warning(f"Timeout or readiness check failed for optional dependency {dependency_id} of actor {actor_id}")
            except Exception as e:
                error_msg = f"Error waiting for optional dependency {dependency_id}: {str(e)}"
                logger.warning(error_msg)  # Only warning for optional dependencies
                optional_dependency_errors[dependency_id] = error_msg

        # Check if we have any errors or timeouts for required dependencies
        if dependency_errors or timed_out_dependencies:
            # Record dependency check failure with the tracker
            if tracker:
                details = {
                    "required_errors": dependency_errors,
                    "required_timeouts": list(timed_out_dependencies),
                    "optional_errors": optional_dependency_errors,
                    "optional_timeouts": list(optional_timed_out_dependencies)
                }
                asyncio.create_task(tracker.record_event(
                    actor_id=actor_id,
                    step=InitializationStep.DEPENDENCY_CHECK,
                    details=details,
                    error=Exception(f"Dependency check failed: {len(dependency_errors)} required errors, {len(timed_out_dependencies)} required timeouts, {len(optional_dependency_errors)} optional errors, {len(optional_timed_out_dependencies)} optional timeouts")
                ))

            # Publish dependency check failure event
            await self.event_bus.publish_simple(
                event_type=f"actor.dependency_check.failure.{actor_id}",
                data={
                    "actor_id": actor_id,
                    "required_errors": dependency_errors,
                    "required_timeouts": list(timed_out_dependencies),
                    "optional_errors": optional_dependency_errors,
                    "optional_timeouts": list(optional_timed_out_dependencies),
                    "timestamp": time.time()
                },
                source="initializer",
                priority=EventPriority.HIGH
            )

            # If we're in fail-fast mode and have errors in required dependencies, raise an exception
            if dependency_errors and self._fail_fast:
                raise ActorDependencyError(
                    message=f"Cannot wait for dependencies: Some required dependencies have failed",
                    actor_id=actor_id,
                    dependencies=dependency_errors,
                    error_type=DependencyErrorType.FAILED
                )

            # If we have timeouts in required dependencies but no errors, raise a timeout error or return False
            if timed_out_dependencies:
                if self._fail_fast:
                    raise ActorDependencyError(
                        message=f"Timeout waiting for required dependencies: {', '.join(timed_out_dependencies)}",
                        actor_id=actor_id,
                        dependencies={dep_id: "Timeout waiting for required dependency" for dep_id in timed_out_dependencies},
                        error_type=DependencyErrorType.TIMEOUT
                    )
                return False

        # For optional dependencies, we only log warnings but don't fail the actor
        elif optional_dependency_errors or optional_timed_out_dependencies:
            # Record optional dependency check issues with the tracker
            if tracker:
                details = {
                    "required_errors": {},
                    "required_timeouts": [],
                    "optional_errors": optional_dependency_errors,
                    "optional_timeouts": list(optional_timed_out_dependencies)
                }
                asyncio.create_task(tracker.record_event(
                    actor_id=actor_id,
                    step=InitializationStep.DEPENDENCY_CHECK,
                    details=details,
                    error=None  # No error for optional dependencies
                ))

            # Log warnings about optional dependencies
            if optional_dependency_errors:
                logger.warning(f"Actor {actor_id} has {len(optional_dependency_errors)} failed optional dependencies: {list(optional_dependency_errors.keys())}")
            if optional_timed_out_dependencies:
                logger.warning(f"Actor {actor_id} has {len(optional_timed_out_dependencies)} timed out optional dependencies: {list(optional_timed_out_dependencies)}")

            # Publish event about optional dependency issues
            await self.event_bus.publish_simple(
                event_type=f"actor.optional_dependency_check.warning.{actor_id}",
                data={
                    "actor_id": actor_id,
                    "optional_errors": optional_dependency_errors,
                    "optional_timeouts": list(optional_timed_out_dependencies),
                    "timestamp": time.time()
                },
                source="initializer",
                priority=EventPriority.NORMAL
            )

        # Notify the dependency resolution synchronization point
        sync_point = self._sync_points.get("dependency_resolution_complete")
        if sync_point:
            await sync_point.reach(actor_id)
            # Check if all actors have reached this point
            await self._check_sync_point_completion("dependency_resolution_complete", ActorState.STARTING)

        # Record dependency check success with the tracker
        if tracker:
            asyncio.create_task(tracker.record_event(
                actor_id=actor_id,
                step=InitializationStep.DEPENDENCY_CHECK,
                details={
                    "action": "complete",
                    "success": True,
                    "required_dependencies": list(dependencies),
                    "optional_dependencies": list(optional_dependencies),
                    "optional_errors": optional_dependency_errors,
                    "optional_timeouts": list(optional_timed_out_dependencies)
                }
            ))

        # Publish dependency check success event
        await self.event_bus.publish_simple(
            event_type=f"actor.dependency_check.success.{actor_id}",
            data={
                "actor_id": actor_id,
                "required_dependencies": list(dependencies),
                "optional_dependencies": list(optional_dependencies),
                "optional_errors": optional_dependency_errors,
                "optional_timeouts": list(optional_timed_out_dependencies),
                "timestamp": time.time()
            },
            source="initializer"
        )

        logger.info(f"All dependencies of actor {actor_id} are ready")
        return True

    async def initialize_actor(self, actor: 'Actor', timeout: Optional[float] = None) -> bool:
        """
        Initialize an actor.

        This method handles the first phase of the two-phase initialization process.
        It calls the actor's initialize method and sets its state to INITIALIZED if successful.

        The method uses a circuit breaker to prevent cascading failures during initialization.
        If too many actors fail to initialize, the circuit breaker will open and prevent
        further initialization attempts until a cooling-off period has passed.

        Args:
            actor: The actor to initialize
            timeout: Optional timeout in seconds

        Returns:
            True if the actor was initialized successfully, False otherwise
        """
        actor_id = actor.actor_id

        # Check if actor is registered
        if not await self.is_actor_registered(actor_id):
            logger.error(f"Cannot initialize actor {actor_id}: Not registered")
            return False

        # Get the current state
        with self._state_lock:
            current_state = self._actor_states.get(actor_id)

        # Check if the actor is already initialized
        if current_state == ActorState.INITIALIZED:
            logger.info(f"Actor {actor_id} is already initialized")
            return True

        # Check if the actor is in a valid state for initialization
        if current_state != ActorState.CREATED:
            logger.error(f"Cannot initialize actor {actor_id}: Invalid state {current_state.value if current_state else 'None'}")
            return False

        # Get the circuit breaker for initialization
        circuit_breaker = self._circuit_breakers["initialize"]

        # Set the actor state to INITIALIZING
        try:
            await self.set_actor_state(actor_id, ActorState.INITIALIZING, phase="initialize")
        except Exception as e:
            logger.error(f"Failed to set actor {actor_id} state to INITIALIZING: {e}")
            return False

        # Initialize the actor with circuit breaker protection
        try:
            # Define the initialization function
            async def do_initialize() -> bool:
                # Wait for the registration synchronization point
                sync_point = self._sync_points.get("registration_complete")
                if sync_point and not sync_point.is_complete():
                    logger.info(f"Actor {actor_id} waiting for registration synchronization point")
                    if not await sync_point.wait(timeout):
                        logger.error(f"Timeout waiting for registration synchronization point for actor {actor_id}")
                        raise TimeoutError("Timeout waiting for registration synchronization point")

                # Call the actor's initialize method
                logger.info(f"Initializing actor {actor_id}")
                start_time = time.time()

                # Use a timeout if specified
                if timeout is not None:
                    try:
                        await asyncio.wait_for(actor.initialize(), timeout=timeout)
                    except asyncio.TimeoutError:
                        logger.error(f"Timeout initializing actor {actor_id}")
                        raise TimeoutError(f"Timeout initializing actor {actor_id}")
                else:
                    await actor.initialize()

                logger.info(f"Actor {actor_id} initialized in {time.time() - start_time:.2f} seconds")
                return True

            # Execute the initialization with circuit breaker protection
            try:
                # Publish initialization start event
                await self.event_bus.publish_simple(
                    event_type=f"actor.initialize.start.{actor_id}",
                    data={"actor_id": actor_id, "timestamp": time.time()},
                    source="initializer"
                )

                # Execute with circuit breaker
                await circuit_breaker.execute(do_initialize)

                # Set the actor state to INITIALIZED
                await self.set_actor_state(actor_id, ActorState.INITIALIZED, phase="initialize")

                # Publish initialization success event
                await self.event_bus.publish_simple(
                    event_type=f"actor.initialize.success.{actor_id}",
                    data={"actor_id": actor_id, "timestamp": time.time()},
                    source="initializer"
                )

                return True

            except CircuitBreakerOpenError as e:
                # The circuit breaker is open, too many initialization failures
                logger.error(f"Circuit breaker open for actor initialization: {e}")

                # Set the actor state to FAILED
                await self.set_actor_state(
                    actor_id=actor_id,
                    state=ActorState.FAILED,
                    error=e,
                    phase="initialize",
                    metadata={"circuit_breaker": "open"}
                )

                # Publish initialization failure event
                await self.event_bus.publish_simple(
                    event_type=f"actor.initialize.failure.{actor_id}",
                    data={
                        "actor_id": actor_id,
                        "error": str(e),
                        "circuit_breaker_open": True,
                        "timestamp": time.time()
                    },
                    source="initializer",
                    priority=EventPriority.HIGH
                )

                return False

        except Exception as e:
            logger.error(f"Error initializing actor {actor_id}: {e}")
            logger.error(traceback.format_exc())

            # Set the actor state to FAILED
            await self.set_actor_state(
                actor_id=actor_id,
                state=ActorState.FAILED,
                error=e,
                phase="initialize"
            )

            # Publish initialization failure event
            await self.event_bus.publish_simple(
                event_type=f"actor.initialize.failure.{actor_id}",
                data={
                    "actor_id": actor_id,
                    "error": str(e),
                    "timestamp": time.time()
                },
                source="initializer",
                priority=EventPriority.HIGH
            )

            return False

    async def start_actor(self, actor: 'Actor', timeout: Optional[float] = None) -> bool:
        """
        Start an actor.

        This method handles the second phase of the two-phase initialization process.
        It calls the actor's start method and sets its state to READY if successful.

        The method uses a circuit breaker to prevent cascading failures during startup.
        If too many actors fail to start, the circuit breaker will open and prevent
        further start attempts until a cooling-off period has passed.

        Args:
            actor: The actor to start
            timeout: Optional timeout in seconds

        Returns:
            True if the actor was started successfully, False otherwise
        """
        actor_id = actor.actor_id

        # Check if actor is registered
        if not await self.is_actor_registered(actor_id):
            logger.error(f"Cannot start actor {actor_id}: Not registered")
            return False

        # Get the current state
        with self._state_lock:
            current_state = self._actor_states.get(actor_id)

        # Check if the actor is already ready
        if current_state == ActorState.READY:
            logger.info(f"Actor {actor_id} is already ready")
            return True

        # Check if the actor is in a valid state for starting
        if current_state != ActorState.INITIALIZED:
            logger.error(f"Cannot start actor {actor_id}: Invalid state {current_state.value if current_state else 'None'}")
            return False

        # Get the circuit breakers
        start_circuit_breaker = self._circuit_breakers["start"]
        dependency_circuit_breaker = self._circuit_breakers["dependency"]

        # Wait for dependencies to be ready with circuit breaker protection
        try:
            # Define the dependency wait function
            async def wait_for_deps() -> bool:
                return await self.wait_for_dependencies(actor_id, timeout)

            # Execute with circuit breaker
            try:
                # Publish dependency check start event
                await self.event_bus.publish_simple(
                    event_type=f"actor.dependency_check.start.{actor_id}",
                    data={"actor_id": actor_id, "timestamp": time.time()},
                    source="initializer"
                )

                # Wait for dependencies with circuit breaker protection
                await dependency_circuit_breaker.execute(wait_for_deps)

                # Publish dependency check success event
                await self.event_bus.publish_simple(
                    event_type=f"actor.dependency_check.success.{actor_id}",
                    data={"actor_id": actor_id, "timestamp": time.time()},
                    source="initializer"
                )

            except CircuitBreakerOpenError as e:
                # The circuit breaker is open, too many dependency failures
                logger.error(f"Circuit breaker open for dependency checking: {e}")

                # Set the actor state to FAILED
                await self.set_actor_state(
                    actor_id=actor_id,
                    state=ActorState.FAILED,
                    error=e,
                    phase="dependency_check",
                    metadata={"circuit_breaker": "open"}
                )

                # Publish dependency check failure event
                await self.event_bus.publish_simple(
                    event_type=f"actor.dependency_check.failure.{actor_id}",
                    data={
                        "actor_id": actor_id,
                        "error": str(e),
                        "circuit_breaker_open": True,
                        "timestamp": time.time()
                    },
                    source="initializer",
                    priority=EventPriority.HIGH
                )

                return False

        except Exception as e:
            logger.error(f"Failed to wait for dependencies of actor {actor_id}: {e}")
            await self.set_actor_state(
                actor_id=actor_id,
                state=ActorState.FAILED,
                error=e,
                phase="dependency_check"
            )

            # Publish dependency check failure event
            await self.event_bus.publish_simple(
                event_type=f"actor.dependency_check.failure.{actor_id}",
                data={
                    "actor_id": actor_id,
                    "error": str(e),
                    "timestamp": time.time()
                },
                source="initializer",
                priority=EventPriority.HIGH
            )

            return False

        # Set the actor state to STARTING
        try:
            await self.set_actor_state(actor_id, ActorState.STARTING, phase="start")
        except Exception as e:
            logger.error(f"Failed to set actor {actor_id} state to STARTING: {e}")
            return False

        # Start the actor with circuit breaker protection
        try:
            # Define the start function
            async def do_start() -> bool:
                # Wait for the initialization synchronization point
                sync_point = self._sync_points.get("initialization_complete")
                if sync_point and not sync_point.is_complete():
                    logger.info(f"Actor {actor_id} waiting for initialization synchronization point")
                    if not await sync_point.wait(timeout):
                        logger.error(f"Timeout waiting for initialization synchronization point for actor {actor_id}")
                        raise TimeoutError("Timeout waiting for initialization synchronization point")

                # Call the actor's start method
                logger.info(f"Starting actor {actor_id}")
                start_time = time.time()

                # Use a timeout if specified
                if timeout is not None:
                    try:
                        await asyncio.wait_for(actor.start(), timeout=timeout)
                    except asyncio.TimeoutError:
                        logger.error(f"Timeout starting actor {actor_id}")
                        raise TimeoutError(f"Timeout starting actor {actor_id}")
                else:
                    await actor.start()

                logger.info(f"Actor {actor_id} started in {time.time() - start_time:.2f} seconds")
                return True

            # Execute the start with circuit breaker protection
            try:
                # Publish start event
                await self.event_bus.publish_simple(
                    event_type=f"actor.start.start.{actor_id}",
                    data={"actor_id": actor_id, "timestamp": time.time()},
                    source="initializer"
                )

                # Execute with circuit breaker
                await start_circuit_breaker.execute(do_start)

                # Set the actor state to READY
                await self.set_actor_state(actor_id, ActorState.READY, phase="start")

                # Check if the actor is actually ready with a more comprehensive check
                is_ready = await self.wait_for_actor_ready(actor_id, timeout=5.0, verify_readiness=True)
                if not is_ready:
                    logger.warning(f"Actor {actor_id} reports it is not ready after start")

                    # Get detailed readiness information
                    readiness_info = {}
                    try:
                        # Get the actor from the registry
                        from .actor_registry import get_registry
                        registry = get_registry()
                        actor_obj = registry.get_actor(actor_id)

                        if actor_obj:
                            # Check state machine state
                            if hasattr(actor_obj, 'state_machine'):
                                readiness_info["state_machine_state"] = actor_obj.state_machine.get_state().value

                            # Check running flag
                            if hasattr(actor_obj, '_is_running'):
                                readiness_info["is_running"] = actor_obj._is_running

                            # Check ready event
                            if hasattr(actor_obj, '_ready_event'):
                                readiness_info["ready_event_set"] = actor_obj._ready_event.is_set()

                            # Check process task
                            if hasattr(actor_obj, '_process_task'):
                                process_task = actor_obj._process_task
                                readiness_info["process_task_running"] = process_task is not None and not process_task.done()
                    except Exception as e:
                        logger.error(f"Error getting detailed readiness information: {e}")

                    # Set the actor state to FAILED
                    await self.set_actor_state(
                        actor_id=actor_id,
                        state=ActorState.FAILED,
                        error=ActorInitializationError(
                            message=f"Actor failed readiness check after start: {readiness_info}",
                            actor_id=actor_id,
                            phase="start",
                            state=ActorState.READY,
                            original_error=None
                        ),
                        phase="start"
                    )

                    # Publish start failure event with detailed information
                    await self.event_bus.publish_simple(
                        event_type=f"actor.start.failure.{actor_id}",
                        data={
                            "actor_id": actor_id,
                            "error": "Failed readiness check after start",
                            "readiness_info": readiness_info,
                            "timestamp": time.time()
                        },
                        source="initializer",
                        priority=EventPriority.HIGH
                    )

                    return False

                # Publish start success event
                await self.event_bus.publish_simple(
                    event_type=f"actor.start.success.{actor_id}",
                    data={"actor_id": actor_id, "timestamp": time.time()},
                    source="initializer"
                )

                return True

            except CircuitBreakerOpenError as e:
                # The circuit breaker is open, too many start failures
                logger.error(f"Circuit breaker open for actor start: {e}")

                # Set the actor state to FAILED
                await self.set_actor_state(
                    actor_id=actor_id,
                    state=ActorState.FAILED,
                    error=e,
                    phase="start",
                    metadata={"circuit_breaker": "open"}
                )

                # Publish start failure event
                await self.event_bus.publish_simple(
                    event_type=f"actor.start.failure.{actor_id}",
                    data={
                        "actor_id": actor_id,
                        "error": str(e),
                        "circuit_breaker_open": True,
                        "timestamp": time.time()
                    },
                    source="initializer",
                    priority=EventPriority.HIGH
                )

                return False

        except Exception as e:
            logger.error(f"Error starting actor {actor_id}: {e}")
            logger.error(traceback.format_exc())

            # Set the actor state to FAILED
            await self.set_actor_state(
                actor_id=actor_id,
                state=ActorState.FAILED,
                error=e,
                phase="start"
            )

            # Publish start failure event
            await self.event_bus.publish_simple(
                event_type=f"actor.start.failure.{actor_id}",
                data={
                    "actor_id": actor_id,
                    "error": str(e),
                    "timestamp": time.time()
                },
                source="initializer",
                priority=EventPriority.HIGH
            )

            return False

    def get_sync_points(self) -> Dict[str, SynchronizationPoint]:
        """
        Get the synchronization points.

        Returns:
            Dictionary of synchronization points
        """
        return self._sync_points

    async def cleanup_actor(self, actor_id: str) -> None:
        """
        Clean up resources for an actor.

        Args:
            actor_id: ID of the actor to clean up
        """
        # Check if actor is registered
        if not await self.is_actor_registered(actor_id):
            logger.warning(f"Cannot clean up actor {actor_id}: Not registered")
            return

        # Get the actor's resources
        with self._state_lock:
            resources = self._actor_resources.get(actor_id, [])

        # Clean up each resource
        for cleanup_func, args, kwargs in resources:
            try:
                if asyncio.iscoroutinefunction(cleanup_func):
                    await cleanup_func(*args, **kwargs)
                else:
                    cleanup_func(*args, **kwargs)
            except Exception as e:
                logger.error(f"Error cleaning up resource for actor {actor_id}: {e}")
                logger.error(traceback.format_exc())

        # Clear the actor's resources
        with self._state_lock:
            self._actor_resources[actor_id] = []

        logger.info(f"Cleaned up resources for actor {actor_id}")

    async def stop_actor(self, actor: 'Actor', timeout: Optional[float] = None) -> bool:
        """
        Stop an actor.

        Args:
            actor: The actor to stop
            timeout: Optional timeout in seconds

        Returns:
            True if the actor was stopped successfully, False otherwise
        """
        actor_id = actor.actor_id

        # Check if actor is registered
        if not await self.is_actor_registered(actor_id):
            logger.error(f"Cannot stop actor {actor_id}: Not registered")
            return False

        # Get the current state
        with self._state_lock:
            current_state = self._actor_states.get(actor_id)

        # Check if the actor is already stopped
        if current_state == ActorState.STOPPED:
            logger.info(f"Actor {actor_id} is already stopped")
            return True

        # Set the actor state to STOPPING
        try:
            await self.set_actor_state(actor_id, ActorState.STOPPING, phase="stop")
        except Exception as e:
            logger.error(f"Failed to set actor {actor_id} state to STOPPING: {e}")
            return False

        # Stop the actor
        try:
            # Call the actor's stop method
            logger.info(f"Stopping actor {actor_id}")
            start_time = time.time()

            # Use a timeout if specified
            if timeout is not None:
                try:
                    await asyncio.wait_for(actor.stop(), timeout=timeout)
                except asyncio.TimeoutError:
                    logger.error(f"Timeout stopping actor {actor_id}")
                    await self.set_actor_state(actor_id, ActorState.FAILED,
                                             error=TimeoutError(f"Timeout stopping actor {actor_id}"),
                                             phase="stop")
                    return False
            else:
                await actor.stop()

            # Clean up the actor's resources
            await self.cleanup_actor(actor_id)

            # Set the actor state to STOPPED
            await self.set_actor_state(actor_id, ActorState.STOPPED, phase="stop")

            logger.info(f"Actor {actor_id} stopped in {time.time() - start_time:.2f} seconds")
            return True
        except Exception as e:
            logger.error(f"Error stopping actor {actor_id}: {e}")
            logger.error(traceback.format_exc())

            # Set the actor state to FAILED
            await self.set_actor_state(actor_id, ActorState.FAILED, error=e, phase="stop")
            return False

    async def reset(self) -> None:
        """
        Reset the initializer.

        This method clears all state and resources, effectively resetting the initializer
        to its initial state.
        """
        async with self.initialization_lock:
            # Clean up all actors
            with self._state_lock:
                actor_ids = list(self._actor_states.keys())

            for actor_id in actor_ids:
                await self.cleanup_actor(actor_id)

            # Clear all state
            with self._state_lock:
                self._actor_states.clear()
                self._actor_details.clear()
                self._initialization_errors.clear()
                self._initialization_events.clear()
                self._readiness_events.clear()
                self._dependencies.clear()
                self._actor_resources.clear()
                self._actor_locks.clear()

            # Reset events
            self._global_ready_event = None  # Will be lazily initialized when needed

            # Reset synchronization points
            self._sync_points.clear()
            self._sync_points_created = None  # Will be lazily initialized when needed
            self._sync_points_initialized = False

            logger.info("Initializer reset")


# Singleton instance
_initializer = None


def get_initializer(fail_fast: bool = False, dependency_container: Optional[DependencyContainer] = None) -> ConsolidatedActorInitializer:
    """
    Get the singleton initializer instance.

    Args:
        fail_fast: If True, the initialization process will stop immediately
                  if any actor fails to initialize. If False, the process will
                  continue with other actors.
        dependency_container: Optional dependency container to use for resolving dependencies.
                             If not provided, the default container will be used.

    Returns:
        The initializer instance
    """
    global _initializer

    if _initializer is None:
        _initializer = ConsolidatedActorInitializer(fail_fast=fail_fast, dependency_container=dependency_container)
        logger.info("Created new initializer")

    return _initializer


def reset_initializer() -> None:
    """Reset the singleton initializer instance."""
    global _initializer

    # Don't try to reset the initializer, just set it to None
    # This avoids issues with asyncio.create_task outside of an event loop
    _initializer = None
    logger.info("Reset initializer")
