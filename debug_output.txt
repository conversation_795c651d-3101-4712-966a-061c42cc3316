<frozen runpy>:128: RuntimeWarning: 'vibe_check.cli.main' found in sys.modules after import of package 'vibe_check.cli', but prior to execution of 'vibe_check.cli.main'; this may result in unpredictable behaviour
2025-05-11 16:44:57,460 - vibe_check_cli - DEBUG - Starting Vibe Check CLI
2025-05-11 16:44:57,460 - vibe_check_cli - DEBUG - Python version: 3.13.3 (main, Apr 21 2025, 12:53:27) [Clang 17.0.0 (clang-1700.0.13.3)]
2025-05-11 16:44:57,460 - vibe_check_cli - DEBUG - Arguments: ['/Users/<USER>/Lokalne_Kody/PAT_project_analysis/vibe_check/cli/main.py', 'analyze', '--debug', '--debug-actor-system', './test_project']
2025-05-11 16:44:57,461 - vibe_check_cli.analyze - DEBUG - Analyzing project: ./test_project
2025-05-11 16:44:57,461 - vibe_check_cli.analyze - DEBUG - Config: None, Output: None
2025-05-11 16:44:57,461 - vibe_check_cli.analyze - DEBUG - Verbose: False, Quiet: False
2025-05-11 16:44:57,461 - vibe_check_cli.analyze - DEBUG - Security focused: False, Performance focused: False, Maintainability focused: False
2025-05-11 16:44:57,461 - vibe_check_cli.analyze - DEBUG - Preset: None
2025-05-11 16:44:57,461 - vibe_check_cli.analyze - DEBUG - Analyze trends: False, Report progress: False
2025-05-11 16:44:57,461 - vibe_check_cli.analyze - DEBUG - Custom report: False
2025-05-11 16:44:57,461 - vibe_check_cli.analyze - DEBUG - Use simple analyzer: False
Analyzing project: ./test_project
Debug mode enabled
Actor system debugging enabled
Analysis in progress...
2025-05-11 16:44:57,461 - vibe_check_cli.analyze_command - DEBUG - Starting analyze_command
2025-05-11 16:44:57,461 - vibe_check_cli.analyze_command - DEBUG - Project path: ./test_project
2025-05-11 16:44:57,461 - vibe_check_cli.analyze_command - DEBUG - Config path: None
2025-05-11 16:44:57,461 - vibe_check_cli.analyze_command - DEBUG - Output directory: None
2025-05-11 16:44:57,461 - vibe_check_cli.analyze_command - DEBUG - Verbose: False, Quiet: False
2025-05-11 16:44:57,461 - vibe_check_cli.analyze_command - DEBUG - Config override: {}
2025-05-11 16:44:57,461 - vibe_check_cli.analyze_command - DEBUG - Analyze trends: False, Report progress: False
2025-05-11 16:44:57,461 - vibe_check_cli.analyze_command - DEBUG - Use simple analyzer: False
2025-05-11 16:44:57,461 - vibe_check_cli.analyze_command - DEBUG - Additional kwargs: {'context': {'priorities': {'security': 0.33, 'performance': 0.33, 'maintainability': 0.34}}, 'debug': True, 'debug_actor_system': True, 'log_file': None, 'save_diagnostics': False, 'visualize_actor_system': False}
2025-05-11 16:44:57,461 - vibe_check_cli.analyze_command - DEBUG - Using actor system for analysis
2025-05-11 16:44:57,461 - vibe_check_cli.analyze_command - DEBUG - Calling analyze_project
2025-05-11 16:44:57,461 - vibe_check_cli.analyze_command - INFO - Setting up enhanced actor system logging with debug mode
2025-05-11 16:44:57,461 - vibe_check_actor_system - INFO - Actor system logging set up with level=10, debug_mode=True
2025-05-11 16:44:57,461 - vibe_check_actor_system - INFO - Debug mode enabled
2025-05-11 16:44:57,461 - vibe_check_cli.analyze_command - INFO - Enhanced actor system logging configured successfully
2025-05-11 16:44:57,461 - vibe_check_initialization_tracker - INFO - Created new initialization tracker
2025-05-11 16:44:57,461 - vibe_check_cli.analyze_command - INFO - Initialized actor system diagnostics tracker
2025-05-11 16:44:57,461 - vibe_check_orchestrator - INFO - analyze_project called with project_path=./test_project
2025-05-11 16:44:57,461 - vibe_check_orchestrator - INFO - config_path=None, output_dir=None
2025-05-11 16:44:57,461 - vibe_check_orchestrator - INFO - config_override={}, show_progress=True
2025-05-11 16:44:57,461 - vibe_check_orchestrator - INFO - Final context: {'priorities': {'security': 0.33, 'performance': 0.33, 'maintainability': 0.34}, 'show_progress': True, 'analyze_trends': False, 'report_progress': False, 'debug': True, 'debug_actor_system': True, 'log_file': None, 'save_diagnostics': False, 'visualize_actor_system': False}
2025-05-11 16:44:57,461 - vibe_check_orchestrator - INFO - Creating new event loop
2025-05-11 16:44:57,461 - asyncio - DEBUG - Using selector: KqueueSelector
2025-05-11 16:44:57,461 - vibe_check_orchestrator - INFO - Starting _analyze_project_async
2025-05-11 16:44:57,461 - vibe_check_orchestrator - INFO - _analyze_project_async called with project_path=./test_project
2025-05-11 16:44:57,461 - vibe_check_orchestrator - INFO - context={'priorities': {'security': 0.33, 'performance': 0.33, 'maintainability': 0.34}, 'show_progress': True, 'analyze_trends': False, 'report_progress': False, 'debug': True, 'debug_actor_system': True, 'log_file': None, 'save_diagnostics': False, 'visualize_actor_system': False}, config_path=None
2025-05-11 16:44:57,461 - vibe_check_orchestrator - INFO - Resetting orchestrator
2025-05-11 16:44:57,461 - vibe_check_orchestrator - INFO - Getting new orchestrator instance
2025-05-11 16:44:57,461 - vibe_check_orchestrator - INFO - Creating new Orchestrator instance (reset=False)
2025-05-11 16:44:57,461 - vibe_check_config_utils - DEBUG - Looking for default config at: /Users/<USER>/Lokalne_Kody/PAT_project_analysis/vibe_check/config/default_config.yaml
2025-05-11 16:44:57,461 - vibe_check_config_utils - DEBUG - Using default config: /Users/<USER>/Lokalne_Kody/PAT_project_analysis/vibe_check/config/default_config.yaml
2025-05-11 16:44:57,461 - vibe_check_config_utils - DEBUG - Loading config from: /Users/<USER>/Lokalne_Kody/PAT_project_analysis/vibe_check/config/default_config.yaml
2025-05-11 16:44:57,465 - vibe_check_config_utils - DEBUG - Loaded config: {'file_extensions': ['.py'], 'analyze_docs': False, 'exclude_patterns': ['**/__pycache__/**', '**/venv/**', '**/env/**', '**/.git/**', '**/node_modules/**', '**/.pytest_cache/**', '**/*.pyc'], 'progress_bar': {'enabled': True, 'style': 'rich', 'show_details': True, 'update_interval': 0.1}, 'tools': {'ruff': {'enabled': True, 'args': ['--select=E,F,W,I', '--ignore=E501']}, 'flake8': {'enabled': False, 'args': []}, 'black': {'enabled': False, 'args': ['--line-length=100']}, 'isort': {'enabled': False, 'args': []}, 'mypy': {'enabled': True, 'args': ['--ignore-missing-imports'], 'check_all_files': False}, 'pyright': {'enabled': False, 'args': []}, 'pylint': {'enabled': False, 'args': ['--disable=C0111,C0103', '--enable=E,F,W,R']}, 'pyflakes': {'enabled': False, 'args': []}, 'bandit': {'enabled': True, 'args': ['--recursive']}, 'pydocstyle': {'enabled': False, 'args': []}, 'markdown': {'enabled': True, 'args': []}, 'complexity': {'enabled': True}, 'custom_rules': {'enabled': False, 'args': []}}, 'reporting': {'formats': ['markdown', 'json'], 'generate_summary': True, 'generate_issue_report': True, 'generate_metrics_report': True, 'generate_recommendations': True, 'generate_prompts': False, 'generate_custom_report': False, 'custom_report': {'format': 'html', 'sections': {'summary': True, 'issues': True, 'metrics': True, 'recommendations': True, 'complexity': True, 'dependencies': True, 'documentation': True, 'trends': True}, 'metrics': {'file_count': True, 'line_count': True, 'complexity': True, 'issues': True, 'type_coverage': True, 'doc_coverage': True, 'dependencies': True}}}, 'visualization': {'enabled': True, 'formats': ['png', 'html'], 'generate_dependency_graph': True, 'generate_complexity_heatmap': True, 'generate_coverage_charts': True, 'generate_dashboard': True}, 'pre_analysis': {'enabled': True, 'auto_ignore': True}, 'performance': {'parallel': True, 'max_workers': 4, 'timeout': 60, 'cache_results': True, 'cache_dir': '.vibe_check_cache', 'profiling': {'enabled': False, 'output': 'vibe_check_profile.prof'}}}
2025-05-11 16:44:57,465 - vibe_check_execution_mode_manager - INFO - Initialized execution mode manager with mode: parallel
2025-05-11 16:44:57,465 - vibe_check_orchestrator - INFO - Orchestrator created with config: {'file_extensions': ['.py'], 'analyze_docs': False, 'exclude_patterns': ['**/__pycache__/**', '**/venv/**', '**/env/**', '**/.git/**', '**/node_modules/**', '**/.pytest_cache/**', '**/*.pyc'], 'progress_bar': {'enabled': True, 'style': 'rich', 'show_details': True, 'update_interval': 0.1}, 'tools': {'ruff': {'enabled': True, 'args': ['--select=E,F,W,I', '--ignore=E501']}, 'flake8': {'enabled': False, 'args': []}, 'black': {'enabled': False, 'args': ['--line-length=100']}, 'isort': {'enabled': False, 'args': []}, 'mypy': {'enabled': True, 'args': ['--ignore-missing-imports'], 'check_all_files': False}, 'pyright': {'enabled': False, 'args': []}, 'pylint': {'enabled': False, 'args': ['--disable=C0111,C0103', '--enable=E,F,W,R']}, 'pyflakes': {'enabled': False, 'args': []}, 'bandit': {'enabled': True, 'args': ['--recursive']}, 'pydocstyle': {'enabled': False, 'args': []}, 'markdown': {'enabled': True, 'args': []}, 'complexity': {'enabled': True}, 'custom_rules': {'enabled': False, 'args': []}}, 'reporting': {'formats': ['markdown', 'json'], 'generate_summary': True, 'generate_issue_report': True, 'generate_metrics_report': True, 'generate_recommendations': True, 'generate_prompts': False, 'generate_custom_report': False, 'custom_report': {'format': 'html', 'sections': {'summary': True, 'issues': True, 'metrics': True, 'recommendations': True, 'complexity': True, 'dependencies': True, 'documentation': True, 'trends': True}, 'metrics': {'file_count': True, 'line_count': True, 'complexity': True, 'issues': True, 'type_coverage': True, 'doc_coverage': True, 'dependencies': True}}}, 'visualization': {'enabled': True, 'formats': ['png', 'html'], 'generate_dependency_graph': True, 'generate_complexity_heatmap': True, 'generate_coverage_charts': True, 'generate_dashboard': True}, 'pre_analysis': {'enabled': True, 'auto_ignore': True}, 'performance': {'parallel': True, 'max_workers': 4, 'timeout': 60, 'cache_results': True, 'cache_dir': '.vibe_check_cache', 'profiling': {'enabled': False, 'output': 'vibe_check_profile.prof'}}}
2025-05-11 16:44:57,465 - vibe_check_orchestrator - INFO - Starting orchestrator.analyze_project
2025-05-11 16:44:57,465 - vibe_check_orchestrator - INFO - Orchestrator.analyze_project called with project_path=./test_project
2025-05-11 16:44:57,465 - vibe_check_orchestrator - INFO - context={'priorities': {'security': 0.33, 'performance': 0.33, 'maintainability': 0.34}, 'show_progress': True, 'analyze_trends': False, 'report_progress': False, 'debug': True, 'debug_actor_system': True, 'log_file': None, 'save_diagnostics': False, 'visualize_actor_system': False}
2025-05-11 16:44:57,465 - vibe_check_orchestrator - INFO - Converted project_path to Path object: test_project
2025-05-11 16:44:57,465 - vibe_check_orchestrator - INFO - Stored project_path: test_project
2025-05-11 16:44:57,465 - vibe_check_orchestrator - INFO - Ensured output directory exists: vibe_check_output
2025-05-11 16:44:57,465 - vibe_check_orchestrator - INFO - Initialized actor system diagnostics tracker
2025-05-11 16:44:57,465 - vibe_check_actor_registry - INFO - Reset actor registry to None
2025-05-11 16:44:57,465 - vibe_check_orchestrator - INFO - Reset actor registry
2025-05-11 16:44:57,465 - vibe_check_orchestrator - INFO - Creating actor system
2025-05-11 16:44:57,465 - vibe_check_orchestrator - INFO - Building actor system using ActorSystemBuilder
2025-05-11 16:44:57,465 - vibe_check_actor_registry - INFO - Reset actor registry to None
2025-05-11 16:44:57,465 - vibe_check_actor_registry - INFO - Created new actor registry instance
2025-05-11 16:44:57,465 - vibe_check_actor_registry - INFO - Registered actor supervisor_actor of type supervisor
2025-05-11 16:44:57,465 - vibe_check_actor_system - INFO - Actor supervisor_actor registered with registry
2025-05-11 16:44:57,465 - vibe_check_actor_registry - WARNING - Actor supervisor_actor already registered, updating
2025-05-11 16:44:57,465 - vibe_check_actor_registry - INFO - Registered actor supervisor_actor of type supervisor
2025-05-11 16:44:57,465 - vibe_check_actor_system_builder - INFO - Created supervisor actor
2025-05-11 16:44:57,465 - vibe_check_actor_registry - INFO - Registered actor report_actor of type report
2025-05-11 16:44:57,465 - vibe_check_actor_system - INFO - Actor report_actor registered with registry
2025-05-11 16:44:57,465 - vibe_check_actor_system - INFO - Report actor initialized with output dir: vibe_check_output
2025-05-11 16:44:57,465 - vibe_check_actor_registry - WARNING - Actor report_actor already registered, updating
2025-05-11 16:44:57,465 - vibe_check_actor_registry - INFO - Registered actor report_actor of type report
2025-05-11 16:44:57,466 - vibe_check_actor_registry - INFO - Registered actor visualization_actor of type visualization
2025-05-11 16:44:57,466 - vibe_check_actor_system - INFO - Actor visualization_actor registered with registry
2025-05-11 16:44:57,466 - vibe_check_actor_registry - WARNING - Actor visualization_actor already registered, updating
2025-05-11 16:44:57,466 - vibe_check_actor_registry - INFO - Registered actor visualization_actor of type visualization
2025-05-11 16:44:57,466 - vibe_check_actor_registry - INFO - Registered actor tool_actor of type tool
2025-05-11 16:44:57,466 - vibe_check_actor_system - INFO - Actor tool_actor registered with registry
2025-05-11 16:44:57,466 - vibe_check_tool_bridge - INFO - Available tools: mypy, complexity, pylint, bandit, custom_rules, ruff, doc_analyzer
2025-05-11 16:44:57,466 - vibe_check_actor_registry - WARNING - Actor tool_actor already registered, updating
2025-05-11 16:44:57,466 - vibe_check_actor_registry - INFO - Registered actor tool_actor of type tool
2025-05-11 16:44:57,466 - vibe_check_actor_registry - INFO - Registered actor file_actor_0 of type file
2025-05-11 16:44:57,466 - vibe_check_actor_system - INFO - Actor file_actor_0 registered with registry
2025-05-11 16:44:57,466 - vibe_check_actor_registry - WARNING - Actor file_actor_0 already registered, updating
2025-05-11 16:44:57,466 - vibe_check_actor_registry - INFO - Registered actor file_actor_0 of type file
2025-05-11 16:44:57,466 - vibe_check_actor_registry - INFO - Registered actor file_actor_1 of type file
2025-05-11 16:44:57,466 - vibe_check_actor_system - INFO - Actor file_actor_1 registered with registry
2025-05-11 16:44:57,466 - vibe_check_actor_registry - WARNING - Actor file_actor_1 already registered, updating
2025-05-11 16:44:57,466 - vibe_check_actor_registry - INFO - Registered actor file_actor_1 of type file
2025-05-11 16:44:57,466 - vibe_check_actor_registry - INFO - Registered actor file_actor_2 of type file
2025-05-11 16:44:57,466 - vibe_check_actor_system - INFO - Actor file_actor_2 registered with registry
2025-05-11 16:44:57,466 - vibe_check_actor_registry - WARNING - Actor file_actor_2 already registered, updating
2025-05-11 16:44:57,466 - vibe_check_actor_registry - INFO - Registered actor file_actor_2 of type file
2025-05-11 16:44:57,466 - vibe_check_actor_registry - INFO - Registered actor file_actor_3 of type file
2025-05-11 16:44:57,466 - vibe_check_actor_system - INFO - Actor file_actor_3 registered with registry
2025-05-11 16:44:57,466 - vibe_check_actor_registry - WARNING - Actor file_actor_3 already registered, updating
2025-05-11 16:44:57,466 - vibe_check_actor_registry - INFO - Registered actor file_actor_3 of type file
2025-05-11 16:44:57,466 - vibe_check_actor_registry - INFO - Registered actor file_actor_4 of type file
2025-05-11 16:44:57,466 - vibe_check_actor_system - INFO - Actor file_actor_4 registered with registry
2025-05-11 16:44:57,466 - vibe_check_actor_registry - WARNING - Actor file_actor_4 already registered, updating
2025-05-11 16:44:57,466 - vibe_check_actor_registry - INFO - Registered actor file_actor_4 of type file
2025-05-11 16:44:57,466 - vibe_check_actor_registry - INFO - Registered actor file_actor_5 of type file
2025-05-11 16:44:57,466 - vibe_check_actor_system - INFO - Actor file_actor_5 registered with registry
2025-05-11 16:44:57,466 - vibe_check_actor_registry - WARNING - Actor file_actor_5 already registered, updating
2025-05-11 16:44:57,466 - vibe_check_actor_registry - INFO - Registered actor file_actor_5 of type file
2025-05-11 16:44:57,467 - vibe_check_actor_registry - INFO - Registered actor file_actor_6 of type file
2025-05-11 16:44:57,467 - vibe_check_actor_system - INFO - Actor file_actor_6 registered with registry
2025-05-11 16:44:57,467 - vibe_check_actor_registry - WARNING - Actor file_actor_6 already registered, updating
2025-05-11 16:44:57,467 - vibe_check_actor_registry - INFO - Registered actor file_actor_6 of type file
2025-05-11 16:44:57,467 - vibe_check_actor_registry - INFO - Registered actor file_actor_7 of type file
2025-05-11 16:44:57,467 - vibe_check_actor_system - INFO - Actor file_actor_7 registered with registry
2025-05-11 16:44:57,467 - vibe_check_actor_registry - WARNING - Actor file_actor_7 already registered, updating
2025-05-11 16:44:57,467 - vibe_check_actor_registry - INFO - Registered actor file_actor_7 of type file
2025-05-11 16:44:57,467 - vibe_check_actor_registry - INFO - Registered actor file_actor_8 of type file
2025-05-11 16:44:57,467 - vibe_check_actor_system - INFO - Actor file_actor_8 registered with registry
2025-05-11 16:44:57,467 - vibe_check_actor_registry - WARNING - Actor file_actor_8 already registered, updating
2025-05-11 16:44:57,467 - vibe_check_actor_registry - INFO - Registered actor file_actor_8 of type file
2025-05-11 16:44:57,467 - vibe_check_actor_registry - INFO - Registered actor file_actor_9 of type file
2025-05-11 16:44:57,467 - vibe_check_actor_system - INFO - Actor file_actor_9 registered with registry
2025-05-11 16:44:57,467 - vibe_check_actor_registry - WARNING - Actor file_actor_9 already registered, updating
2025-05-11 16:44:57,467 - vibe_check_actor_registry - INFO - Registered actor file_actor_9 of type file
2025-05-11 16:44:57,467 - vibe_check_actor_registry - INFO - Registered actor file_actor_10 of type file
2025-05-11 16:44:57,467 - vibe_check_actor_system - INFO - Actor file_actor_10 registered with registry
2025-05-11 16:44:57,467 - vibe_check_actor_registry - WARNING - Actor file_actor_10 already registered, updating
2025-05-11 16:44:57,467 - vibe_check_actor_registry - INFO - Registered actor file_actor_10 of type file
2025-05-11 16:44:57,467 - vibe_check_actor_registry - INFO - Registered actor file_actor_11 of type file
2025-05-11 16:44:57,467 - vibe_check_actor_system - INFO - Actor file_actor_11 registered with registry
2025-05-11 16:44:57,467 - vibe_check_actor_registry - WARNING - Actor file_actor_11 already registered, updating
2025-05-11 16:44:57,467 - vibe_check_actor_registry - INFO - Registered actor file_actor_11 of type file
2025-05-11 16:44:57,467 - vibe_check_actor_registry - INFO - Registered actor file_actor_12 of type file
2025-05-11 16:44:57,467 - vibe_check_actor_system - INFO - Actor file_actor_12 registered with registry
2025-05-11 16:44:57,467 - vibe_check_actor_registry - WARNING - Actor file_actor_12 already registered, updating
2025-05-11 16:44:57,467 - vibe_check_actor_registry - INFO - Registered actor file_actor_12 of type file
2025-05-11 16:44:57,467 - vibe_check_actor_registry - INFO - Registered actor file_actor_13 of type file
2025-05-11 16:44:57,467 - vibe_check_actor_system - INFO - Actor file_actor_13 registered with registry
2025-05-11 16:44:57,467 - vibe_check_actor_registry - WARNING - Actor file_actor_13 already registered, updating
2025-05-11 16:44:57,467 - vibe_check_actor_registry - INFO - Registered actor file_actor_13 of type file
2025-05-11 16:44:57,467 - vibe_check_actor_registry - INFO - Registered actor project_actor of type project
2025-05-11 16:44:57,467 - vibe_check_actor_system - INFO - Actor project_actor registered with registry
2025-05-11 16:44:57,467 - vibe_check_actor_registry - WARNING - Actor project_actor already registered, updating
2025-05-11 16:44:57,467 - vibe_check_actor_registry - INFO - Registered actor project_actor of type project
2025-05-11 16:44:57,467 - vibe_check_orchestrator - INFO - Connecting actors using ActorConnector
2025-05-11 16:44:57,467 - vibe_check_actor_system - INFO - Set 14 file actors for project project_actor
2025-05-11 16:44:57,467 - vibe_check_actor_connector - INFO - Set 14 file actors for project actor
2025-05-11 16:44:57,467 - vibe_check_actor_system - INFO - Set tool actor tool_actor for file file_actor_0
2025-05-11 16:44:57,467 - vibe_check_actor_connector - DEBUG - Set tool actor tool_actor for file actor file_actor_0
2025-05-11 16:44:57,467 - vibe_check_actor_system - INFO - Set report actor report_actor for file file_actor_0
2025-05-11 16:44:57,467 - vibe_check_actor_connector - DEBUG - Set report actor for file actor file_actor_0
2025-05-11 16:44:57,467 - vibe_check_actor_system - INFO - Set tool actor tool_actor for file file_actor_1
2025-05-11 16:44:57,467 - vibe_check_actor_connector - DEBUG - Set tool actor tool_actor for file actor file_actor_1
2025-05-11 16:44:57,467 - vibe_check_actor_system - INFO - Set report actor report_actor for file file_actor_1
2025-05-11 16:44:57,467 - vibe_check_actor_connector - DEBUG - Set report actor for file actor file_actor_1
2025-05-11 16:44:57,467 - vibe_check_actor_system - INFO - Set tool actor tool_actor for file file_actor_2
2025-05-11 16:44:57,467 - vibe_check_actor_connector - DEBUG - Set tool actor tool_actor for file actor file_actor_2
2025-05-11 16:44:57,467 - vibe_check_actor_system - INFO - Set report actor report_actor for file file_actor_2
2025-05-11 16:44:57,467 - vibe_check_actor_connector - DEBUG - Set report actor for file actor file_actor_2
2025-05-11 16:44:57,467 - vibe_check_actor_system - INFO - Set tool actor tool_actor for file file_actor_3
2025-05-11 16:44:57,467 - vibe_check_actor_connector - DEBUG - Set tool actor tool_actor for file actor file_actor_3
2025-05-11 16:44:57,467 - vibe_check_actor_system - INFO - Set report actor report_actor for file file_actor_3
2025-05-11 16:44:57,467 - vibe_check_actor_connector - DEBUG - Set report actor for file actor file_actor_3
2025-05-11 16:44:57,467 - vibe_check_actor_system - INFO - Set tool actor tool_actor for file file_actor_4
2025-05-11 16:44:57,467 - vibe_check_actor_connector - DEBUG - Set tool actor tool_actor for file actor file_actor_4
2025-05-11 16:44:57,467 - vibe_check_actor_system - INFO - Set report actor report_actor for file file_actor_4
2025-05-11 16:44:57,467 - vibe_check_actor_connector - DEBUG - Set report actor for file actor file_actor_4
2025-05-11 16:44:57,467 - vibe_check_actor_system - INFO - Set tool actor tool_actor for file file_actor_5
2025-05-11 16:44:57,467 - vibe_check_actor_connector - DEBUG - Set tool actor tool_actor for file actor file_actor_5
2025-05-11 16:44:57,467 - vibe_check_actor_system - INFO - Set report actor report_actor for file file_actor_5
2025-05-11 16:44:57,467 - vibe_check_actor_connector - DEBUG - Set report actor for file actor file_actor_5
2025-05-11 16:44:57,467 - vibe_check_actor_system - INFO - Set tool actor tool_actor for file file_actor_6
2025-05-11 16:44:57,467 - vibe_check_actor_connector - DEBUG - Set tool actor tool_actor for file actor file_actor_6
2025-05-11 16:44:57,467 - vibe_check_actor_system - INFO - Set report actor report_actor for file file_actor_6
2025-05-11 16:44:57,467 - vibe_check_actor_connector - DEBUG - Set report actor for file actor file_actor_6
2025-05-11 16:44:57,467 - vibe_check_actor_system - INFO - Set tool actor tool_actor for file file_actor_7
2025-05-11 16:44:57,467 - vibe_check_actor_connector - DEBUG - Set tool actor tool_actor for file actor file_actor_7
2025-05-11 16:44:57,467 - vibe_check_actor_system - INFO - Set report actor report_actor for file file_actor_7
2025-05-11 16:44:57,467 - vibe_check_actor_connector - DEBUG - Set report actor for file actor file_actor_7
2025-05-11 16:44:57,467 - vibe_check_actor_system - INFO - Set tool actor tool_actor for file file_actor_8
2025-05-11 16:44:57,467 - vibe_check_actor_connector - DEBUG - Set tool actor tool_actor for file actor file_actor_8
2025-05-11 16:44:57,467 - vibe_check_actor_system - INFO - Set report actor report_actor for file file_actor_8
2025-05-11 16:44:57,467 - vibe_check_actor_connector - DEBUG - Set report actor for file actor file_actor_8
2025-05-11 16:44:57,467 - vibe_check_actor_system - INFO - Set tool actor tool_actor for file file_actor_9
2025-05-11 16:44:57,467 - vibe_check_actor_connector - DEBUG - Set tool actor tool_actor for file actor file_actor_9
2025-05-11 16:44:57,467 - vibe_check_actor_system - INFO - Set report actor report_actor for file file_actor_9
2025-05-11 16:44:57,467 - vibe_check_actor_connector - DEBUG - Set report actor for file actor file_actor_9
2025-05-11 16:44:57,467 - vibe_check_actor_system - INFO - Set tool actor tool_actor for file file_actor_10
2025-05-11 16:44:57,467 - vibe_check_actor_connector - DEBUG - Set tool actor tool_actor for file actor file_actor_10
2025-05-11 16:44:57,467 - vibe_check_actor_system - INFO - Set report actor report_actor for file file_actor_10
2025-05-11 16:44:57,467 - vibe_check_actor_connector - DEBUG - Set report actor for file actor file_actor_10
2025-05-11 16:44:57,467 - vibe_check_actor_system - INFO - Set tool actor tool_actor for file file_actor_11
2025-05-11 16:44:57,467 - vibe_check_actor_connector - DEBUG - Set tool actor tool_actor for file actor file_actor_11
2025-05-11 16:44:57,468 - vibe_check_actor_system - INFO - Set report actor report_actor for file file_actor_11
2025-05-11 16:44:57,468 - vibe_check_actor_connector - DEBUG - Set report actor for file actor file_actor_11
2025-05-11 16:44:57,468 - vibe_check_actor_system - INFO - Set tool actor tool_actor for file file_actor_12
2025-05-11 16:44:57,468 - vibe_check_actor_connector - DEBUG - Set tool actor tool_actor for file actor file_actor_12
2025-05-11 16:44:57,468 - vibe_check_actor_system - INFO - Set report actor report_actor for file file_actor_12
2025-05-11 16:44:57,468 - vibe_check_actor_connector - DEBUG - Set report actor for file actor file_actor_12
2025-05-11 16:44:57,468 - vibe_check_actor_system - INFO - Set tool actor tool_actor for file file_actor_13
2025-05-11 16:44:57,468 - vibe_check_actor_connector - DEBUG - Set tool actor tool_actor for file actor file_actor_13
2025-05-11 16:44:57,468 - vibe_check_actor_system - INFO - Set report actor report_actor for file file_actor_13
2025-05-11 16:44:57,468 - vibe_check_actor_connector - DEBUG - Set report actor for file actor file_actor_13
2025-05-11 16:44:57,468 - vibe_check_actor_system - INFO - Set visualization actor visualization_actor
2025-05-11 16:44:57,468 - vibe_check_actor_connector - DEBUG - Set visualization actor for report_actor
2025-05-11 16:44:57,468 - vibe_check_actor_system - INFO - Set report actor report_actor for tool tool_actor
2025-05-11 16:44:57,468 - vibe_check_actor_connector - DEBUG - Set report actor for tool_actor
2025-05-11 16:44:57,468 - vibe_check_actor_system - INFO - Set visualization actor visualization_actor for tool tool_actor
2025-05-11 16:44:57,468 - vibe_check_actor_connector - DEBUG - Set visualization actor for tool_actor
2025-05-11 16:44:57,468 - vibe_check_actor_system - INFO - Set report actor report_actor for file file_actor_0
2025-05-11 16:44:57,468 - vibe_check_actor_connector - DEBUG - Set report actor for file_actor_0
2025-05-11 16:44:57,468 - vibe_check_actor_system - INFO - Set report actor report_actor for file file_actor_1
2025-05-11 16:44:57,468 - vibe_check_actor_connector - DEBUG - Set report actor for file_actor_1
2025-05-11 16:44:57,468 - vibe_check_actor_system - INFO - Set report actor report_actor for file file_actor_2
2025-05-11 16:44:57,468 - vibe_check_actor_connector - DEBUG - Set report actor for file_actor_2
2025-05-11 16:44:57,468 - vibe_check_actor_system - INFO - Set report actor report_actor for file file_actor_3
2025-05-11 16:44:57,468 - vibe_check_actor_connector - DEBUG - Set report actor for file_actor_3
2025-05-11 16:44:57,468 - vibe_check_actor_system - INFO - Set report actor report_actor for file file_actor_4
2025-05-11 16:44:57,468 - vibe_check_actor_connector - DEBUG - Set report actor for file_actor_4
2025-05-11 16:44:57,468 - vibe_check_actor_system - INFO - Set report actor report_actor for file file_actor_5
2025-05-11 16:44:57,468 - vibe_check_actor_connector - DEBUG - Set report actor for file_actor_5
2025-05-11 16:44:57,468 - vibe_check_actor_system - INFO - Set report actor report_actor for file file_actor_6
2025-05-11 16:44:57,468 - vibe_check_actor_connector - DEBUG - Set report actor for file_actor_6
2025-05-11 16:44:57,468 - vibe_check_actor_system - INFO - Set report actor report_actor for file file_actor_7
2025-05-11 16:44:57,468 - vibe_check_actor_connector - DEBUG - Set report actor for file_actor_7
2025-05-11 16:44:57,468 - vibe_check_actor_system - INFO - Set report actor report_actor for file file_actor_8
2025-05-11 16:44:57,468 - vibe_check_actor_connector - DEBUG - Set report actor for file_actor_8
2025-05-11 16:44:57,468 - vibe_check_actor_system - INFO - Set report actor report_actor for file file_actor_9
2025-05-11 16:44:57,468 - vibe_check_actor_connector - DEBUG - Set report actor for file_actor_9
2025-05-11 16:44:57,468 - vibe_check_actor_system - INFO - Set report actor report_actor for file file_actor_10
2025-05-11 16:44:57,468 - vibe_check_actor_connector - DEBUG - Set report actor for file_actor_10
2025-05-11 16:44:57,468 - vibe_check_actor_system - INFO - Set report actor report_actor for file file_actor_11
2025-05-11 16:44:57,468 - vibe_check_actor_connector - DEBUG - Set report actor for file_actor_11
2025-05-11 16:44:57,468 - vibe_check_actor_system - INFO - Set report actor report_actor for file file_actor_12
2025-05-11 16:44:57,468 - vibe_check_actor_connector - DEBUG - Set report actor for file_actor_12
2025-05-11 16:44:57,468 - vibe_check_actor_system - INFO - Set report actor report_actor for file file_actor_13
2025-05-11 16:44:57,468 - vibe_check_actor_connector - DEBUG - Set report actor for file_actor_13
2025-05-11 16:44:57,468 - vibe_check_actor_system - INFO - Set report actor report_actor for project project_actor
2025-05-11 16:44:57,468 - vibe_check_actor_connector - DEBUG - Set report actor for project_actor
2025-05-11 16:44:57,468 - vibe_check_actor_system - INFO - Set visualization actor visualization_actor for project project_actor
2025-05-11 16:44:57,468 - vibe_check_actor_connector - DEBUG - Set visualization actor for project_actor
2025-05-11 16:44:57,468 - vibe_check_actor_connector - INFO - Created 55 stream subscription tasks
2025-05-11 16:44:57,468 - vibe_check_actor_connector - INFO - All actors connected successfully
2025-05-11 16:44:57,468 - vibe_check_orchestrator - INFO - Actor system created with 19 actors
2025-05-11 16:44:57,468 - vibe_check_initialization_tracker - INFO - Actor supervisor_actor registration
2025-05-11 16:44:57,468 - vibe_check_initialization_tracker - INFO - Actor report_actor registration
2025-05-11 16:44:57,468 - vibe_check_initialization_tracker - INFO - Actor visualization_actor registration
2025-05-11 16:44:57,468 - vibe_check_initialization_tracker - INFO - Actor tool_actor registration
2025-05-11 16:44:57,468 - vibe_check_initialization_tracker - INFO - Actor file_actor_0 registration
2025-05-11 16:44:57,468 - vibe_check_initialization_tracker - INFO - Actor file_actor_1 registration
2025-05-11 16:44:57,468 - vibe_check_initialization_tracker - INFO - Actor file_actor_2 registration
2025-05-11 16:44:57,468 - vibe_check_initialization_tracker - INFO - Actor file_actor_3 registration
2025-05-11 16:44:57,468 - vibe_check_initialization_tracker - INFO - Actor file_actor_4 registration
2025-05-11 16:44:57,468 - vibe_check_initialization_tracker - INFO - Actor file_actor_5 registration
2025-05-11 16:44:57,468 - vibe_check_initialization_tracker - INFO - Actor file_actor_6 registration
2025-05-11 16:44:57,468 - vibe_check_initialization_tracker - INFO - Actor file_actor_7 registration
2025-05-11 16:44:57,468 - vibe_check_initialization_tracker - INFO - Actor file_actor_8 registration
2025-05-11 16:44:57,468 - vibe_check_initialization_tracker - INFO - Actor file_actor_9 registration
2025-05-11 16:44:57,468 - vibe_check_initialization_tracker - INFO - Actor file_actor_10 registration
2025-05-11 16:44:57,468 - vibe_check_initialization_tracker - INFO - Actor file_actor_11 registration
2025-05-11 16:44:57,468 - vibe_check_initialization_tracker - INFO - Actor file_actor_12 registration
2025-05-11 16:44:57,468 - vibe_check_initialization_tracker - INFO - Actor file_actor_13 registration
2025-05-11 16:44:57,468 - vibe_check_initialization_tracker - INFO - Actor project_actor registration
2025-05-11 16:44:57,468 - vibe_check_orchestrator - INFO - Starting actors
2025-05-11 16:44:57,468 - vibe_check_orchestrator - INFO - Starting actors using ActorLifecycleManager
2025-05-11 16:44:57,468 - vibe_check_actor_initializer - INFO - Reset actor initializer to None
2025-05-11 16:44:57,468 - vibe_check_actor_initializer - INFO - Created new actor initializer instance
2025-05-11 16:44:57,468 - vibe_check_actor_lifecycle_manager - INFO - Reset actor initializer (fail_fast=False)
2025-05-11 16:44:57,468 - vibe_check_actor_lifecycle_manager - INFO - Phase 1: Initializing all actors
2025-05-11 16:44:57,468 - vibe_check_actor_initializer - INFO - [ACTOR INIT] Starting initialization of actor supervisor_actor
2025-05-11 16:44:57,468 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting registry for actor supervisor_actor
2025-05-11 16:44:57,468 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting actor supervisor_actor from registry
2025-05-11 16:44:57,468 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Successfully retrieved actor supervisor_actor from registry
2025-05-11 16:44:57,468 - vibe_check_actor_initializer - INFO - [ACTOR INIT] Actor supervisor_actor is of type supervisor
2025-05-11 16:44:57,468 - vibe_check_actor_initializer - WARNING - Cannot get state: Actor supervisor_actor not registered
2025-05-11 16:44:57,468 - vibe_check_actor_initializer - WARNING - [ACTOR INIT] Actor supervisor_actor not registered, will attempt to initialize anyway
2025-05-11 16:44:57,468 - vibe_check_actor_initializer - INFO - Registered actor supervisor_actor with initializer
2025-05-11 16:44:57,468 - vibe_check_actor_initializer - INFO - [ACTOR INIT] Auto-registered actor supervisor_actor
2025-05-11 16:44:57,468 - vibe_check_actor_initializer - DEBUG - [ACTOR INIT] Setting actor supervisor_actor state to INITIALIZING
2025-05-11 16:44:57,468 - vibe_check_actor_initializer - INFO - Actor supervisor_actor state changed: created -> initializing
2025-05-11 16:44:57,468 - vibe_check_actor_initializer - DEBUG - [ACTOR INIT] Calling initialize() method on actor supervisor_actor
2025-05-11 16:44:57,469 - vibe_check_actor_system - INFO - Actor supervisor_actor is already registered with legacy initializer
2025-05-11 16:44:57,469 - vibe_check_actor_initialization - INFO - Created actor initialization manager
2025-05-11 16:44:57,469 - vibe_check_actor_initialization - INFO - Registered actor supervisor_actor with initialization manager
2025-05-11 16:44:57,469 - vibe_check_actor_system - INFO - Actor supervisor_actor registered with initialization manager
2025-05-11 16:44:57,469 - vibe_check_actor_initialization - DEBUG - Actor supervisor_actor is already in state created, no change needed
2025-05-11 16:44:57,469 - vibe_check_initialization_tracker - INFO - Actor supervisor_actor registration
2025-05-11 16:44:57,469 - vibe_check_actor_initializer - INFO - Registered actor report_actor with initializer
2025-05-11 16:44:57,469 - vibe_check_actor_system - INFO - Actor report_actor registered with legacy initializer
2025-05-11 16:44:57,469 - vibe_check_actor_initializer - DEBUG - Registered cleanup resource for actor report_actor
2025-05-11 16:44:57,469 - vibe_check_actor_initializer - DEBUG - Actor report_actor is already in state created, no change needed
2025-05-11 16:44:57,469 - vibe_check_actor_initializer - INFO - Actor report_actor state changed: created -> created
2025-05-11 16:44:57,469 - vibe_check_actor_initialization - INFO - Registered actor report_actor with initialization manager
2025-05-11 16:44:57,469 - vibe_check_actor_system - INFO - Actor report_actor registered with initialization manager
2025-05-11 16:44:57,469 - vibe_check_actor_initialization - DEBUG - Actor report_actor is already in state created, no change needed
2025-05-11 16:44:57,469 - vibe_check_initialization_tracker - INFO - Actor report_actor registration
2025-05-11 16:44:57,469 - vibe_check_actor_initializer - INFO - Registered actor visualization_actor with initializer
2025-05-11 16:44:57,469 - vibe_check_actor_system - INFO - Actor visualization_actor registered with legacy initializer
2025-05-11 16:44:57,469 - vibe_check_actor_initializer - DEBUG - Registered cleanup resource for actor visualization_actor
2025-05-11 16:44:57,469 - vibe_check_actor_initializer - DEBUG - Actor visualization_actor is already in state created, no change needed
2025-05-11 16:44:57,469 - vibe_check_actor_initializer - INFO - Actor visualization_actor state changed: created -> created
2025-05-11 16:44:57,469 - vibe_check_actor_initialization - INFO - Registered actor visualization_actor with initialization manager
2025-05-11 16:44:57,469 - vibe_check_actor_system - INFO - Actor visualization_actor registered with initialization manager
2025-05-11 16:44:57,469 - vibe_check_actor_initialization - DEBUG - Actor visualization_actor is already in state created, no change needed
2025-05-11 16:44:57,469 - vibe_check_initialization_tracker - INFO - Actor visualization_actor registration
2025-05-11 16:44:57,469 - vibe_check_actor_initializer - INFO - Registered actor tool_actor with initializer
2025-05-11 16:44:57,469 - vibe_check_actor_system - INFO - Actor tool_actor registered with legacy initializer
2025-05-11 16:44:57,469 - vibe_check_actor_initializer - DEBUG - Registered cleanup resource for actor tool_actor
2025-05-11 16:44:57,469 - vibe_check_actor_initializer - DEBUG - Actor tool_actor is already in state created, no change needed
2025-05-11 16:44:57,469 - vibe_check_actor_initializer - INFO - Actor tool_actor state changed: created -> created
2025-05-11 16:44:57,469 - vibe_check_actor_initialization - INFO - Registered actor tool_actor with initialization manager
2025-05-11 16:44:57,469 - vibe_check_actor_system - INFO - Actor tool_actor registered with initialization manager
2025-05-11 16:44:57,469 - vibe_check_actor_initialization - DEBUG - Actor tool_actor is already in state created, no change needed
2025-05-11 16:44:57,469 - vibe_check_initialization_tracker - INFO - Actor tool_actor registration
2025-05-11 16:44:57,469 - vibe_check_actor_initializer - INFO - Registered actor file_actor_0 with initializer
2025-05-11 16:44:57,469 - vibe_check_actor_system - INFO - Actor file_actor_0 registered with legacy initializer
2025-05-11 16:44:57,469 - vibe_check_actor_initializer - DEBUG - Registered cleanup resource for actor file_actor_0
2025-05-11 16:44:57,469 - vibe_check_actor_initializer - DEBUG - Actor file_actor_0 is already in state created, no change needed
2025-05-11 16:44:57,469 - vibe_check_actor_initializer - INFO - Actor file_actor_0 state changed: created -> created
2025-05-11 16:44:57,469 - vibe_check_actor_initialization - INFO - Registered actor file_actor_0 with initialization manager
2025-05-11 16:44:57,469 - vibe_check_actor_system - INFO - Actor file_actor_0 registered with initialization manager
2025-05-11 16:44:57,469 - vibe_check_actor_initialization - DEBUG - Actor file_actor_0 is already in state created, no change needed
2025-05-11 16:44:57,469 - vibe_check_initialization_tracker - INFO - Actor file_actor_0 registration
2025-05-11 16:44:57,469 - vibe_check_actor_initializer - INFO - Registered actor file_actor_1 with initializer
2025-05-11 16:44:57,469 - vibe_check_actor_system - INFO - Actor file_actor_1 registered with legacy initializer
2025-05-11 16:44:57,469 - vibe_check_actor_initializer - DEBUG - Registered cleanup resource for actor file_actor_1
2025-05-11 16:44:57,469 - vibe_check_actor_initializer - DEBUG - Actor file_actor_1 is already in state created, no change needed
2025-05-11 16:44:57,469 - vibe_check_actor_initializer - INFO - Actor file_actor_1 state changed: created -> created
2025-05-11 16:44:57,469 - vibe_check_actor_initialization - INFO - Registered actor file_actor_1 with initialization manager
2025-05-11 16:44:57,469 - vibe_check_actor_system - INFO - Actor file_actor_1 registered with initialization manager
2025-05-11 16:44:57,469 - vibe_check_actor_initialization - DEBUG - Actor file_actor_1 is already in state created, no change needed
2025-05-11 16:44:57,469 - vibe_check_initialization_tracker - INFO - Actor file_actor_1 registration
2025-05-11 16:44:57,469 - vibe_check_actor_initializer - INFO - Registered actor file_actor_2 with initializer
2025-05-11 16:44:57,469 - vibe_check_actor_system - INFO - Actor file_actor_2 registered with legacy initializer
2025-05-11 16:44:57,469 - vibe_check_actor_initializer - DEBUG - Registered cleanup resource for actor file_actor_2
2025-05-11 16:44:57,469 - vibe_check_actor_initializer - DEBUG - Actor file_actor_2 is already in state created, no change needed
2025-05-11 16:44:57,469 - vibe_check_actor_initializer - INFO - Actor file_actor_2 state changed: created -> created
2025-05-11 16:44:57,469 - vibe_check_actor_initialization - INFO - Registered actor file_actor_2 with initialization manager
2025-05-11 16:44:57,469 - vibe_check_actor_system - INFO - Actor file_actor_2 registered with initialization manager
2025-05-11 16:44:57,469 - vibe_check_actor_initialization - DEBUG - Actor file_actor_2 is already in state created, no change needed
2025-05-11 16:44:57,469 - vibe_check_initialization_tracker - INFO - Actor file_actor_2 registration
2025-05-11 16:44:57,469 - vibe_check_actor_initializer - INFO - Registered actor file_actor_3 with initializer
2025-05-11 16:44:57,469 - vibe_check_actor_system - INFO - Actor file_actor_3 registered with legacy initializer
2025-05-11 16:44:57,469 - vibe_check_actor_initializer - DEBUG - Registered cleanup resource for actor file_actor_3
2025-05-11 16:44:57,469 - vibe_check_actor_initializer - DEBUG - Actor file_actor_3 is already in state created, no change needed
2025-05-11 16:44:57,469 - vibe_check_actor_initializer - INFO - Actor file_actor_3 state changed: created -> created
2025-05-11 16:44:57,469 - vibe_check_actor_initialization - INFO - Registered actor file_actor_3 with initialization manager
2025-05-11 16:44:57,470 - vibe_check_actor_system - INFO - Actor file_actor_3 registered with initialization manager
2025-05-11 16:44:57,470 - vibe_check_actor_initialization - DEBUG - Actor file_actor_3 is already in state created, no change needed
2025-05-11 16:44:57,470 - vibe_check_initialization_tracker - INFO - Actor file_actor_3 registration
2025-05-11 16:44:57,470 - vibe_check_actor_initializer - INFO - Registered actor file_actor_4 with initializer
2025-05-11 16:44:57,470 - vibe_check_actor_system - INFO - Actor file_actor_4 registered with legacy initializer
2025-05-11 16:44:57,470 - vibe_check_actor_initializer - DEBUG - Registered cleanup resource for actor file_actor_4
2025-05-11 16:44:57,470 - vibe_check_actor_initializer - DEBUG - Actor file_actor_4 is already in state created, no change needed
2025-05-11 16:44:57,470 - vibe_check_actor_initializer - INFO - Actor file_actor_4 state changed: created -> created
2025-05-11 16:44:57,470 - vibe_check_actor_initialization - INFO - Registered actor file_actor_4 with initialization manager
2025-05-11 16:44:57,470 - vibe_check_actor_system - INFO - Actor file_actor_4 registered with initialization manager
2025-05-11 16:44:57,470 - vibe_check_actor_initialization - DEBUG - Actor file_actor_4 is already in state created, no change needed
2025-05-11 16:44:57,470 - vibe_check_initialization_tracker - INFO - Actor file_actor_4 registration
2025-05-11 16:44:57,470 - vibe_check_actor_initializer - INFO - Registered actor file_actor_5 with initializer
2025-05-11 16:44:57,470 - vibe_check_actor_system - INFO - Actor file_actor_5 registered with legacy initializer
2025-05-11 16:44:57,470 - vibe_check_actor_initializer - DEBUG - Registered cleanup resource for actor file_actor_5
2025-05-11 16:44:57,470 - vibe_check_actor_initializer - DEBUG - Actor file_actor_5 is already in state created, no change needed
2025-05-11 16:44:57,470 - vibe_check_actor_initializer - INFO - Actor file_actor_5 state changed: created -> created
2025-05-11 16:44:57,470 - vibe_check_actor_initialization - INFO - Registered actor file_actor_5 with initialization manager
2025-05-11 16:44:57,470 - vibe_check_actor_system - INFO - Actor file_actor_5 registered with initialization manager
2025-05-11 16:44:57,470 - vibe_check_actor_initialization - DEBUG - Actor file_actor_5 is already in state created, no change needed
2025-05-11 16:44:57,470 - vibe_check_initialization_tracker - INFO - Actor file_actor_5 registration
2025-05-11 16:44:57,470 - vibe_check_actor_initializer - INFO - Registered actor file_actor_6 with initializer
2025-05-11 16:44:57,470 - vibe_check_actor_system - INFO - Actor file_actor_6 registered with legacy initializer
2025-05-11 16:44:57,470 - vibe_check_actor_initializer - DEBUG - Registered cleanup resource for actor file_actor_6
2025-05-11 16:44:57,470 - vibe_check_actor_initializer - DEBUG - Actor file_actor_6 is already in state created, no change needed
2025-05-11 16:44:57,470 - vibe_check_actor_initializer - INFO - Actor file_actor_6 state changed: created -> created
2025-05-11 16:44:57,470 - vibe_check_actor_initialization - INFO - Registered actor file_actor_6 with initialization manager
2025-05-11 16:44:57,470 - vibe_check_actor_system - INFO - Actor file_actor_6 registered with initialization manager
2025-05-11 16:44:57,470 - vibe_check_actor_initialization - DEBUG - Actor file_actor_6 is already in state created, no change needed
2025-05-11 16:44:57,470 - vibe_check_initialization_tracker - INFO - Actor file_actor_6 registration
2025-05-11 16:44:57,470 - vibe_check_actor_initializer - INFO - Registered actor file_actor_7 with initializer
2025-05-11 16:44:57,470 - vibe_check_actor_system - INFO - Actor file_actor_7 registered with legacy initializer
2025-05-11 16:44:57,470 - vibe_check_actor_initializer - DEBUG - Registered cleanup resource for actor file_actor_7
2025-05-11 16:44:57,470 - vibe_check_actor_initializer - DEBUG - Actor file_actor_7 is already in state created, no change needed
2025-05-11 16:44:57,470 - vibe_check_actor_initializer - INFO - Actor file_actor_7 state changed: created -> created
2025-05-11 16:44:57,470 - vibe_check_actor_initialization - INFO - Registered actor file_actor_7 with initialization manager
2025-05-11 16:44:57,470 - vibe_check_actor_system - INFO - Actor file_actor_7 registered with initialization manager
2025-05-11 16:44:57,470 - vibe_check_actor_initialization - DEBUG - Actor file_actor_7 is already in state created, no change needed
2025-05-11 16:44:57,470 - vibe_check_initialization_tracker - INFO - Actor file_actor_7 registration
2025-05-11 16:44:57,470 - vibe_check_actor_initializer - INFO - Registered actor file_actor_8 with initializer
2025-05-11 16:44:57,470 - vibe_check_actor_system - INFO - Actor file_actor_8 registered with legacy initializer
2025-05-11 16:44:57,470 - vibe_check_actor_initializer - DEBUG - Registered cleanup resource for actor file_actor_8
2025-05-11 16:44:57,470 - vibe_check_actor_initializer - DEBUG - Actor file_actor_8 is already in state created, no change needed
2025-05-11 16:44:57,470 - vibe_check_actor_initializer - INFO - Actor file_actor_8 state changed: created -> created
2025-05-11 16:44:57,470 - vibe_check_actor_initialization - INFO - Registered actor file_actor_8 with initialization manager
2025-05-11 16:44:57,470 - vibe_check_actor_system - INFO - Actor file_actor_8 registered with initialization manager
2025-05-11 16:44:57,470 - vibe_check_actor_initialization - DEBUG - Actor file_actor_8 is already in state created, no change needed
2025-05-11 16:44:57,470 - vibe_check_initialization_tracker - INFO - Actor file_actor_8 registration
2025-05-11 16:44:57,470 - vibe_check_actor_initializer - INFO - Registered actor file_actor_9 with initializer
2025-05-11 16:44:57,470 - vibe_check_actor_system - INFO - Actor file_actor_9 registered with legacy initializer
2025-05-11 16:44:57,470 - vibe_check_actor_initializer - DEBUG - Registered cleanup resource for actor file_actor_9
2025-05-11 16:44:57,470 - vibe_check_actor_initializer - DEBUG - Actor file_actor_9 is already in state created, no change needed
2025-05-11 16:44:57,470 - vibe_check_actor_initializer - INFO - Actor file_actor_9 state changed: created -> created
2025-05-11 16:44:57,470 - vibe_check_actor_initialization - INFO - Registered actor file_actor_9 with initialization manager
2025-05-11 16:44:57,470 - vibe_check_actor_system - INFO - Actor file_actor_9 registered with initialization manager
2025-05-11 16:44:57,470 - vibe_check_actor_initialization - DEBUG - Actor file_actor_9 is already in state created, no change needed
2025-05-11 16:44:57,470 - vibe_check_initialization_tracker - INFO - Actor file_actor_9 registration
2025-05-11 16:44:57,470 - vibe_check_actor_initializer - INFO - Registered actor file_actor_10 with initializer
2025-05-11 16:44:57,470 - vibe_check_actor_system - INFO - Actor file_actor_10 registered with legacy initializer
2025-05-11 16:44:57,470 - vibe_check_actor_initializer - DEBUG - Registered cleanup resource for actor file_actor_10
2025-05-11 16:44:57,470 - vibe_check_actor_initializer - DEBUG - Actor file_actor_10 is already in state created, no change needed
2025-05-11 16:44:57,470 - vibe_check_actor_initializer - INFO - Actor file_actor_10 state changed: created -> created
2025-05-11 16:44:57,470 - vibe_check_actor_initialization - INFO - Registered actor file_actor_10 with initialization manager
2025-05-11 16:44:57,470 - vibe_check_actor_system - INFO - Actor file_actor_10 registered with initialization manager
2025-05-11 16:44:57,470 - vibe_check_actor_initialization - DEBUG - Actor file_actor_10 is already in state created, no change needed
2025-05-11 16:44:57,470 - vibe_check_initialization_tracker - INFO - Actor file_actor_10 registration
2025-05-11 16:44:57,470 - vibe_check_actor_initializer - INFO - Registered actor file_actor_11 with initializer
2025-05-11 16:44:57,470 - vibe_check_actor_system - INFO - Actor file_actor_11 registered with legacy initializer
2025-05-11 16:44:57,470 - vibe_check_actor_initializer - DEBUG - Registered cleanup resource for actor file_actor_11
2025-05-11 16:44:57,470 - vibe_check_actor_initializer - DEBUG - Actor file_actor_11 is already in state created, no change needed
2025-05-11 16:44:57,470 - vibe_check_actor_initializer - INFO - Actor file_actor_11 state changed: created -> created
2025-05-11 16:44:57,470 - vibe_check_actor_initialization - INFO - Registered actor file_actor_11 with initialization manager
2025-05-11 16:44:57,470 - vibe_check_actor_system - INFO - Actor file_actor_11 registered with initialization manager
2025-05-11 16:44:57,470 - vibe_check_actor_initialization - DEBUG - Actor file_actor_11 is already in state created, no change needed
2025-05-11 16:44:57,470 - vibe_check_initialization_tracker - INFO - Actor file_actor_11 registration
2025-05-11 16:44:57,470 - vibe_check_actor_initializer - INFO - Registered actor file_actor_12 with initializer
2025-05-11 16:44:57,470 - vibe_check_actor_system - INFO - Actor file_actor_12 registered with legacy initializer
2025-05-11 16:44:57,470 - vibe_check_actor_initializer - DEBUG - Registered cleanup resource for actor file_actor_12
2025-05-11 16:44:57,470 - vibe_check_actor_initializer - DEBUG - Actor file_actor_12 is already in state created, no change needed
2025-05-11 16:44:57,470 - vibe_check_actor_initializer - INFO - Actor file_actor_12 state changed: created -> created
2025-05-11 16:44:57,470 - vibe_check_actor_initialization - INFO - Registered actor file_actor_12 with initialization manager
2025-05-11 16:44:57,470 - vibe_check_actor_system - INFO - Actor file_actor_12 registered with initialization manager
2025-05-11 16:44:57,470 - vibe_check_actor_initialization - DEBUG - Actor file_actor_12 is already in state created, no change needed
2025-05-11 16:44:57,470 - vibe_check_initialization_tracker - INFO - Actor file_actor_12 registration
2025-05-11 16:44:57,470 - vibe_check_actor_initializer - INFO - Registered actor file_actor_13 with initializer
2025-05-11 16:44:57,470 - vibe_check_actor_system - INFO - Actor file_actor_13 registered with legacy initializer
2025-05-11 16:44:57,470 - vibe_check_actor_initializer - DEBUG - Registered cleanup resource for actor file_actor_13
2025-05-11 16:44:57,470 - vibe_check_actor_initializer - DEBUG - Actor file_actor_13 is already in state created, no change needed
2025-05-11 16:44:57,470 - vibe_check_actor_initializer - INFO - Actor file_actor_13 state changed: created -> created
2025-05-11 16:44:57,470 - vibe_check_actor_initialization - INFO - Registered actor file_actor_13 with initialization manager
2025-05-11 16:44:57,470 - vibe_check_actor_system - INFO - Actor file_actor_13 registered with initialization manager
2025-05-11 16:44:57,470 - vibe_check_actor_initialization - DEBUG - Actor file_actor_13 is already in state created, no change needed
2025-05-11 16:44:57,470 - vibe_check_initialization_tracker - INFO - Actor file_actor_13 registration
2025-05-11 16:44:57,471 - vibe_check_actor_initializer - INFO - Registered actor project_actor with initializer
2025-05-11 16:44:57,471 - vibe_check_actor_system - INFO - Actor project_actor registered with legacy initializer
2025-05-11 16:44:57,471 - vibe_check_actor_initializer - DEBUG - Registered cleanup resource for actor project_actor
2025-05-11 16:44:57,471 - vibe_check_actor_initializer - DEBUG - Actor project_actor is already in state created, no change needed
2025-05-11 16:44:57,471 - vibe_check_actor_initializer - INFO - Actor project_actor state changed: created -> created
2025-05-11 16:44:57,471 - vibe_check_actor_initialization - INFO - Registered actor project_actor with initialization manager
2025-05-11 16:44:57,471 - vibe_check_actor_system - INFO - Actor project_actor registered with initialization manager
2025-05-11 16:44:57,471 - vibe_check_actor_initialization - DEBUG - Actor project_actor is already in state created, no change needed
2025-05-11 16:44:57,471 - vibe_check_initialization_tracker - INFO - Actor project_actor registration
2025-05-11 16:44:57,471 - vibe_check_actor_registry - INFO - Actor file_actor_0 subscribed to stream tool_results
2025-05-11 16:44:57,471 - vibe_check_actor_system - INFO - Actor file_actor_0 subscribed to stream tool_results
2025-05-11 16:44:57,471 - vibe_check_actor_registry - INFO - Actor file_actor_0 subscribed to stream report
2025-05-11 16:44:57,471 - vibe_check_actor_system - INFO - Actor file_actor_0 subscribed to stream report
2025-05-11 16:44:57,471 - vibe_check_actor_registry - INFO - Actor file_actor_1 subscribed to stream tool_results
2025-05-11 16:44:57,471 - vibe_check_actor_system - INFO - Actor file_actor_1 subscribed to stream tool_results
2025-05-11 16:44:57,471 - vibe_check_actor_registry - INFO - Actor file_actor_1 subscribed to stream report
2025-05-11 16:44:57,471 - vibe_check_actor_system - INFO - Actor file_actor_1 subscribed to stream report
2025-05-11 16:44:57,471 - vibe_check_actor_registry - INFO - Actor file_actor_2 subscribed to stream tool_results
2025-05-11 16:44:57,471 - vibe_check_actor_system - INFO - Actor file_actor_2 subscribed to stream tool_results
2025-05-11 16:44:57,471 - vibe_check_actor_registry - INFO - Actor file_actor_2 subscribed to stream report
2025-05-11 16:44:57,471 - vibe_check_actor_system - INFO - Actor file_actor_2 subscribed to stream report
2025-05-11 16:44:57,471 - vibe_check_actor_registry - INFO - Actor file_actor_3 subscribed to stream tool_results
2025-05-11 16:44:57,471 - vibe_check_actor_system - INFO - Actor file_actor_3 subscribed to stream tool_results
2025-05-11 16:44:57,471 - vibe_check_actor_registry - INFO - Actor file_actor_3 subscribed to stream report
2025-05-11 16:44:57,471 - vibe_check_actor_system - INFO - Actor file_actor_3 subscribed to stream report
2025-05-11 16:44:57,471 - vibe_check_actor_registry - INFO - Actor file_actor_4 subscribed to stream tool_results
2025-05-11 16:44:57,471 - vibe_check_actor_system - INFO - Actor file_actor_4 subscribed to stream tool_results
2025-05-11 16:44:57,471 - vibe_check_actor_registry - INFO - Actor file_actor_4 subscribed to stream report
2025-05-11 16:44:57,471 - vibe_check_actor_system - INFO - Actor file_actor_4 subscribed to stream report
2025-05-11 16:44:57,471 - vibe_check_actor_registry - INFO - Actor file_actor_5 subscribed to stream tool_results
2025-05-11 16:44:57,471 - vibe_check_actor_system - INFO - Actor file_actor_5 subscribed to stream tool_results
2025-05-11 16:44:57,471 - vibe_check_actor_registry - INFO - Actor file_actor_5 subscribed to stream report
2025-05-11 16:44:57,471 - vibe_check_actor_system - INFO - Actor file_actor_5 subscribed to stream report
2025-05-11 16:44:57,471 - vibe_check_actor_registry - INFO - Actor file_actor_6 subscribed to stream tool_results
2025-05-11 16:44:57,471 - vibe_check_actor_system - INFO - Actor file_actor_6 subscribed to stream tool_results
2025-05-11 16:44:57,471 - vibe_check_actor_registry - INFO - Actor file_actor_6 subscribed to stream report
2025-05-11 16:44:57,471 - vibe_check_actor_system - INFO - Actor file_actor_6 subscribed to stream report
2025-05-11 16:44:57,471 - vibe_check_actor_registry - INFO - Actor file_actor_7 subscribed to stream tool_results
2025-05-11 16:44:57,471 - vibe_check_actor_system - INFO - Actor file_actor_7 subscribed to stream tool_results
2025-05-11 16:44:57,471 - vibe_check_actor_registry - INFO - Actor file_actor_7 subscribed to stream report
2025-05-11 16:44:57,471 - vibe_check_actor_system - INFO - Actor file_actor_7 subscribed to stream report
2025-05-11 16:44:57,471 - vibe_check_actor_registry - INFO - Actor file_actor_8 subscribed to stream tool_results
2025-05-11 16:44:57,471 - vibe_check_actor_system - INFO - Actor file_actor_8 subscribed to stream tool_results
2025-05-11 16:44:57,471 - vibe_check_actor_registry - INFO - Actor file_actor_8 subscribed to stream report
2025-05-11 16:44:57,471 - vibe_check_actor_system - INFO - Actor file_actor_8 subscribed to stream report
2025-05-11 16:44:57,471 - vibe_check_actor_registry - INFO - Actor file_actor_9 subscribed to stream tool_results
2025-05-11 16:44:57,471 - vibe_check_actor_system - INFO - Actor file_actor_9 subscribed to stream tool_results
2025-05-11 16:44:57,471 - vibe_check_actor_registry - INFO - Actor file_actor_9 subscribed to stream report
2025-05-11 16:44:57,471 - vibe_check_actor_system - INFO - Actor file_actor_9 subscribed to stream report
2025-05-11 16:44:57,471 - vibe_check_actor_registry - INFO - Actor file_actor_10 subscribed to stream tool_results
2025-05-11 16:44:57,471 - vibe_check_actor_system - INFO - Actor file_actor_10 subscribed to stream tool_results
2025-05-11 16:44:57,471 - vibe_check_actor_registry - INFO - Actor file_actor_10 subscribed to stream report
2025-05-11 16:44:57,471 - vibe_check_actor_system - INFO - Actor file_actor_10 subscribed to stream report
2025-05-11 16:44:57,471 - vibe_check_actor_registry - INFO - Actor file_actor_11 subscribed to stream tool_results
2025-05-11 16:44:57,471 - vibe_check_actor_system - INFO - Actor file_actor_11 subscribed to stream tool_results
2025-05-11 16:44:57,471 - vibe_check_actor_registry - INFO - Actor file_actor_11 subscribed to stream report
2025-05-11 16:44:57,471 - vibe_check_actor_system - INFO - Actor file_actor_11 subscribed to stream report
2025-05-11 16:44:57,471 - vibe_check_actor_registry - INFO - Actor file_actor_12 subscribed to stream tool_results
2025-05-11 16:44:57,471 - vibe_check_actor_system - INFO - Actor file_actor_12 subscribed to stream tool_results
2025-05-11 16:44:57,471 - vibe_check_actor_registry - INFO - Actor file_actor_12 subscribed to stream report
2025-05-11 16:44:57,471 - vibe_check_actor_system - INFO - Actor file_actor_12 subscribed to stream report
2025-05-11 16:44:57,471 - vibe_check_actor_registry - INFO - Actor file_actor_13 subscribed to stream tool_results
2025-05-11 16:44:57,471 - vibe_check_actor_system - INFO - Actor file_actor_13 subscribed to stream tool_results
2025-05-11 16:44:57,471 - vibe_check_actor_registry - INFO - Actor file_actor_13 subscribed to stream report
2025-05-11 16:44:57,471 - vibe_check_actor_system - INFO - Actor file_actor_13 subscribed to stream report
2025-05-11 16:44:57,471 - vibe_check_actor_registry - INFO - Actor tool_actor subscribed to stream report
2025-05-11 16:44:57,471 - vibe_check_actor_system - INFO - Actor tool_actor subscribed to stream report
2025-05-11 16:44:57,471 - vibe_check_actor_registry - INFO - Actor tool_actor subscribed to stream visualization
2025-05-11 16:44:57,471 - vibe_check_actor_system - INFO - Actor tool_actor subscribed to stream visualization
2025-05-11 16:44:57,471 - vibe_check_actor_system - WARNING - Actor file_actor_0 is already subscribed to stream report
2025-05-11 16:44:57,471 - vibe_check_actor_system - WARNING - Actor file_actor_1 is already subscribed to stream report
2025-05-11 16:44:57,471 - vibe_check_actor_system - WARNING - Actor file_actor_2 is already subscribed to stream report
2025-05-11 16:44:57,471 - vibe_check_actor_system - WARNING - Actor file_actor_3 is already subscribed to stream report
2025-05-11 16:44:57,471 - vibe_check_actor_system - WARNING - Actor file_actor_4 is already subscribed to stream report
2025-05-11 16:44:57,471 - vibe_check_actor_system - WARNING - Actor file_actor_5 is already subscribed to stream report
2025-05-11 16:44:57,471 - vibe_check_actor_system - WARNING - Actor file_actor_6 is already subscribed to stream report
2025-05-11 16:44:57,471 - vibe_check_actor_system - WARNING - Actor file_actor_7 is already subscribed to stream report
2025-05-11 16:44:57,471 - vibe_check_actor_system - WARNING - Actor file_actor_8 is already subscribed to stream report
2025-05-11 16:44:57,471 - vibe_check_actor_system - WARNING - Actor file_actor_9 is already subscribed to stream report
2025-05-11 16:44:57,471 - vibe_check_actor_system - WARNING - Actor file_actor_10 is already subscribed to stream report
2025-05-11 16:44:57,471 - vibe_check_actor_system - WARNING - Actor file_actor_11 is already subscribed to stream report
2025-05-11 16:44:57,471 - vibe_check_actor_system - WARNING - Actor file_actor_12 is already subscribed to stream report
2025-05-11 16:44:57,471 - vibe_check_actor_system - WARNING - Actor file_actor_13 is already subscribed to stream report
2025-05-11 16:44:57,471 - vibe_check_actor_registry - INFO - Actor project_actor subscribed to stream report
2025-05-11 16:44:57,471 - vibe_check_actor_system - INFO - Actor project_actor subscribed to stream report
2025-05-11 16:44:57,471 - vibe_check_actor_registry - INFO - Actor project_actor subscribed to stream visualization
2025-05-11 16:44:57,471 - vibe_check_actor_system - INFO - Actor project_actor subscribed to stream visualization
2025-05-11 16:44:57,471 - vibe_check_actor_registry - INFO - Actor supervisor_actor subscribed to stream project_status
2025-05-11 16:44:57,471 - vibe_check_actor_system - INFO - Actor supervisor_actor subscribed to stream project_status
2025-05-11 16:44:57,471 - vibe_check_actor_registry - INFO - Actor report_actor subscribed to stream project_status
2025-05-11 16:44:57,471 - vibe_check_actor_system - INFO - Actor report_actor subscribed to stream project_status
2025-05-11 16:44:57,471 - vibe_check_actor_registry - INFO - Actor visualization_actor subscribed to stream project_status
2025-05-11 16:44:57,471 - vibe_check_actor_system - INFO - Actor visualization_actor subscribed to stream project_status
2025-05-11 16:44:57,471 - vibe_check_actor_registry - INFO - Actor tool_actor subscribed to stream project_status
2025-05-11 16:44:57,471 - vibe_check_actor_system - INFO - Actor tool_actor subscribed to stream project_status
2025-05-11 16:44:57,471 - vibe_check_actor_registry - INFO - Actor file_actor_0 subscribed to stream project_status
2025-05-11 16:44:57,471 - vibe_check_actor_system - INFO - Actor file_actor_0 subscribed to stream project_status
2025-05-11 16:44:57,471 - vibe_check_actor_registry - INFO - Actor file_actor_1 subscribed to stream project_status
2025-05-11 16:44:57,471 - vibe_check_actor_system - INFO - Actor file_actor_1 subscribed to stream project_status
2025-05-11 16:44:57,471 - vibe_check_actor_registry - INFO - Actor file_actor_2 subscribed to stream project_status
2025-05-11 16:44:57,471 - vibe_check_actor_system - INFO - Actor file_actor_2 subscribed to stream project_status
2025-05-11 16:44:57,471 - vibe_check_actor_registry - INFO - Actor file_actor_3 subscribed to stream project_status
2025-05-11 16:44:57,472 - vibe_check_actor_system - INFO - Actor file_actor_3 subscribed to stream project_status
2025-05-11 16:44:57,472 - vibe_check_actor_registry - INFO - Actor file_actor_4 subscribed to stream project_status
2025-05-11 16:44:57,472 - vibe_check_actor_system - INFO - Actor file_actor_4 subscribed to stream project_status
2025-05-11 16:44:57,472 - vibe_check_actor_registry - INFO - Actor file_actor_5 subscribed to stream project_status
2025-05-11 16:44:57,472 - vibe_check_actor_system - INFO - Actor file_actor_5 subscribed to stream project_status
2025-05-11 16:44:57,472 - vibe_check_actor_registry - INFO - Actor file_actor_6 subscribed to stream project_status
2025-05-11 16:44:57,472 - vibe_check_actor_system - INFO - Actor file_actor_6 subscribed to stream project_status
2025-05-11 16:44:57,472 - vibe_check_actor_registry - INFO - Actor file_actor_7 subscribed to stream project_status
2025-05-11 16:44:57,472 - vibe_check_actor_system - INFO - Actor file_actor_7 subscribed to stream project_status
2025-05-11 16:44:57,472 - vibe_check_actor_registry - INFO - Actor file_actor_8 subscribed to stream project_status
2025-05-11 16:44:57,472 - vibe_check_actor_system - INFO - Actor file_actor_8 subscribed to stream project_status
2025-05-11 16:44:57,472 - vibe_check_actor_registry - INFO - Actor file_actor_9 subscribed to stream project_status
2025-05-11 16:44:57,472 - vibe_check_actor_system - INFO - Actor file_actor_9 subscribed to stream project_status
2025-05-11 16:44:57,472 - vibe_check_actor_registry - INFO - Actor file_actor_10 subscribed to stream project_status
2025-05-11 16:44:57,472 - vibe_check_actor_system - INFO - Actor file_actor_10 subscribed to stream project_status
2025-05-11 16:44:57,472 - vibe_check_actor_registry - INFO - Actor file_actor_11 subscribed to stream project_status
2025-05-11 16:44:57,472 - vibe_check_actor_system - INFO - Actor file_actor_11 subscribed to stream project_status
2025-05-11 16:44:57,472 - vibe_check_actor_registry - INFO - Actor file_actor_12 subscribed to stream project_status
2025-05-11 16:44:57,472 - vibe_check_actor_system - INFO - Actor file_actor_12 subscribed to stream project_status
2025-05-11 16:44:57,472 - vibe_check_actor_registry - INFO - Actor file_actor_13 subscribed to stream project_status
2025-05-11 16:44:57,472 - vibe_check_actor_system - INFO - Actor file_actor_13 subscribed to stream project_status
2025-05-11 16:44:57,472 - vibe_check_actor_registry - INFO - Actor project_actor subscribed to stream project_status
2025-05-11 16:44:57,472 - vibe_check_actor_system - INFO - Actor project_actor subscribed to stream project_status
2025-05-11 16:44:57,472 - vibe_check_actor_registry - INFO - Actor supervisor_actor subscribed to stream metrics
2025-05-11 16:44:57,472 - vibe_check_actor_system - INFO - Actor supervisor_actor subscribed to stream metrics
2025-05-11 16:44:57,472 - vibe_check_actor_registry - INFO - Actor report_actor subscribed to stream metrics
2025-05-11 16:44:57,472 - vibe_check_actor_system - INFO - Actor report_actor subscribed to stream metrics
2025-05-11 16:44:57,472 - vibe_check_actor_registry - INFO - Actor visualization_actor subscribed to stream metrics
2025-05-11 16:44:57,472 - vibe_check_actor_system - INFO - Actor visualization_actor subscribed to stream metrics
2025-05-11 16:44:57,472 - vibe_check_actor_registry - INFO - Actor tool_actor subscribed to stream metrics
2025-05-11 16:44:57,472 - vibe_check_actor_system - INFO - Actor tool_actor subscribed to stream metrics
2025-05-11 16:44:57,472 - vibe_check_actor_registry - INFO - Actor file_actor_0 subscribed to stream metrics
2025-05-11 16:44:57,472 - vibe_check_actor_system - INFO - Actor file_actor_0 subscribed to stream metrics
2025-05-11 16:44:57,472 - vibe_check_actor_registry - INFO - Actor file_actor_1 subscribed to stream metrics
2025-05-11 16:44:57,472 - vibe_check_actor_system - INFO - Actor file_actor_1 subscribed to stream metrics
2025-05-11 16:44:57,472 - vibe_check_actor_registry - INFO - Actor file_actor_2 subscribed to stream metrics
2025-05-11 16:44:57,472 - vibe_check_actor_system - INFO - Actor file_actor_2 subscribed to stream metrics
2025-05-11 16:44:57,472 - vibe_check_actor_registry - INFO - Actor file_actor_3 subscribed to stream metrics
2025-05-11 16:44:57,472 - vibe_check_actor_system - INFO - Actor file_actor_3 subscribed to stream metrics
2025-05-11 16:44:57,472 - vibe_check_actor_registry - INFO - Actor file_actor_4 subscribed to stream metrics
2025-05-11 16:44:57,472 - vibe_check_actor_system - INFO - Actor file_actor_4 subscribed to stream metrics
2025-05-11 16:44:57,472 - vibe_check_actor_registry - INFO - Actor file_actor_5 subscribed to stream metrics
2025-05-11 16:44:57,472 - vibe_check_actor_system - INFO - Actor file_actor_5 subscribed to stream metrics
2025-05-11 16:44:57,472 - vibe_check_actor_registry - INFO - Actor file_actor_6 subscribed to stream metrics
2025-05-11 16:44:57,472 - vibe_check_actor_system - INFO - Actor file_actor_6 subscribed to stream metrics
2025-05-11 16:44:57,472 - vibe_check_actor_registry - INFO - Actor file_actor_7 subscribed to stream metrics
2025-05-11 16:44:57,472 - vibe_check_actor_system - INFO - Actor file_actor_7 subscribed to stream metrics
2025-05-11 16:44:57,472 - vibe_check_actor_registry - INFO - Actor file_actor_8 subscribed to stream metrics
2025-05-11 16:44:57,472 - vibe_check_actor_system - INFO - Actor file_actor_8 subscribed to stream metrics
2025-05-11 16:44:57,472 - vibe_check_actor_registry - INFO - Actor file_actor_9 subscribed to stream metrics
2025-05-11 16:44:57,472 - vibe_check_actor_system - INFO - Actor file_actor_9 subscribed to stream metrics
2025-05-11 16:44:57,472 - vibe_check_actor_registry - INFO - Actor file_actor_10 subscribed to stream metrics
2025-05-11 16:44:57,472 - vibe_check_actor_system - INFO - Actor file_actor_10 subscribed to stream metrics
2025-05-11 16:44:57,472 - vibe_check_actor_registry - INFO - Actor file_actor_11 subscribed to stream metrics
2025-05-11 16:44:57,472 - vibe_check_actor_system - INFO - Actor file_actor_11 subscribed to stream metrics
2025-05-11 16:44:57,472 - vibe_check_actor_registry - INFO - Actor file_actor_12 subscribed to stream metrics
2025-05-11 16:44:57,472 - vibe_check_actor_system - INFO - Actor file_actor_12 subscribed to stream metrics
2025-05-11 16:44:57,472 - vibe_check_actor_registry - INFO - Actor file_actor_13 subscribed to stream metrics
2025-05-11 16:44:57,472 - vibe_check_actor_system - INFO - Actor file_actor_13 subscribed to stream metrics
2025-05-11 16:44:57,472 - vibe_check_actor_registry - INFO - Actor project_actor subscribed to stream metrics
2025-05-11 16:44:57,472 - vibe_check_actor_system - INFO - Actor project_actor subscribed to stream metrics
2025-05-11 16:44:57,472 - vibe_check_actor_registry - INFO - Actor file_actor_0 subscribed to stream file_analysis
2025-05-11 16:44:57,472 - vibe_check_actor_system - INFO - Actor file_actor_0 subscribed to stream file_analysis
2025-05-11 16:44:57,472 - vibe_check_actor_registry - INFO - Actor file_actor_1 subscribed to stream file_analysis
2025-05-11 16:44:57,472 - vibe_check_actor_system - INFO - Actor file_actor_1 subscribed to stream file_analysis
2025-05-11 16:44:57,472 - vibe_check_actor_registry - INFO - Actor file_actor_2 subscribed to stream file_analysis
2025-05-11 16:44:57,472 - vibe_check_actor_system - INFO - Actor file_actor_2 subscribed to stream file_analysis
2025-05-11 16:44:57,472 - vibe_check_actor_registry - INFO - Actor file_actor_3 subscribed to stream file_analysis
2025-05-11 16:44:57,472 - vibe_check_actor_system - INFO - Actor file_actor_3 subscribed to stream file_analysis
2025-05-11 16:44:57,472 - vibe_check_actor_registry - INFO - Actor file_actor_4 subscribed to stream file_analysis
2025-05-11 16:44:57,472 - vibe_check_actor_system - INFO - Actor file_actor_4 subscribed to stream file_analysis
2025-05-11 16:44:57,472 - vibe_check_actor_registry - INFO - Actor file_actor_5 subscribed to stream file_analysis
2025-05-11 16:44:57,472 - vibe_check_actor_system - INFO - Actor file_actor_5 subscribed to stream file_analysis
2025-05-11 16:44:57,472 - vibe_check_actor_registry - INFO - Actor file_actor_6 subscribed to stream file_analysis
2025-05-11 16:44:57,472 - vibe_check_actor_system - INFO - Actor file_actor_6 subscribed to stream file_analysis
2025-05-11 16:44:57,472 - vibe_check_actor_registry - INFO - Actor file_actor_7 subscribed to stream file_analysis
2025-05-11 16:44:57,472 - vibe_check_actor_system - INFO - Actor file_actor_7 subscribed to stream file_analysis
2025-05-11 16:44:57,472 - vibe_check_actor_registry - INFO - Actor file_actor_8 subscribed to stream file_analysis
2025-05-11 16:44:57,472 - vibe_check_actor_system - INFO - Actor file_actor_8 subscribed to stream file_analysis
2025-05-11 16:44:57,472 - vibe_check_actor_registry - INFO - Actor file_actor_9 subscribed to stream file_analysis
2025-05-11 16:44:57,472 - vibe_check_actor_system - INFO - Actor file_actor_9 subscribed to stream file_analysis
2025-05-11 16:44:57,472 - vibe_check_actor_registry - INFO - Actor file_actor_10 subscribed to stream file_analysis
2025-05-11 16:44:57,472 - vibe_check_actor_system - INFO - Actor file_actor_10 subscribed to stream file_analysis
2025-05-11 16:44:57,472 - vibe_check_actor_registry - INFO - Actor file_actor_11 subscribed to stream file_analysis
2025-05-11 16:44:57,472 - vibe_check_actor_system - INFO - Actor file_actor_11 subscribed to stream file_analysis
2025-05-11 16:44:57,472 - vibe_check_actor_registry - INFO - Actor file_actor_12 subscribed to stream file_analysis
2025-05-11 16:44:57,472 - vibe_check_actor_system - INFO - Actor file_actor_12 subscribed to stream file_analysis
2025-05-11 16:44:57,472 - vibe_check_actor_registry - INFO - Actor file_actor_13 subscribed to stream file_analysis
2025-05-11 16:44:57,472 - vibe_check_actor_system - INFO - Actor file_actor_13 subscribed to stream file_analysis
2025-05-11 16:44:57,472 - vibe_check_actor_registry - INFO - Actor tool_actor subscribed to stream tool_results
2025-05-11 16:44:57,472 - vibe_check_actor_system - INFO - Actor tool_actor subscribed to stream tool_results
2025-05-11 16:44:57,472 - vibe_check_actor_registry - INFO - Actor report_actor subscribed to stream report
2025-05-11 16:44:57,472 - vibe_check_actor_system - INFO - Actor report_actor subscribed to stream report
2025-05-11 16:44:57,472 - vibe_check_actor_registry - INFO - Actor visualization_actor subscribed to stream visualization
2025-05-11 16:44:57,472 - vibe_check_actor_system - INFO - Actor visualization_actor subscribed to stream visualization
2025-05-11 16:44:57,472 - vibe_check_initialization_tracker - INFO - Actor supervisor_actor registration
2025-05-11 16:44:57,472 - vibe_check_initialization_tracker - INFO - Actor supervisor_actor initialization_start
2025-05-11 16:44:57,472 - vibe_check_initialization_tracker - INFO - Actor supervisor_actor registration
2025-05-11 16:44:57,472 - vibe_check_synchronization - INFO - Created synchronization point registration_complete with 0 required actors
2025-05-11 16:44:57,472 - vibe_check_synchronization - INFO - Created synchronization point initialization_complete with 0 required actors
2025-05-11 16:44:57,472 - vibe_check_actor_system.initializer - DEBUG - Operation 'create_sync_points' took 0.000051 seconds
2025-05-11 16:44:57,473 - vibe_check_actor_system.initializer - DEBUG - Component state: {'sync_points': ['registration_complete', 'initialization_complete'], 'sync_points_created': True, 'creation_time': 5.1021575927734375e-05, 'timestamp': 1746974697.473011}
2025-05-11 16:44:57,473 - vibe_check_actor_initializer - INFO - Simplified synchronization points created successfully
2025-05-11 16:44:57,473 - vibe_check_initialization_tracker - INFO - Actor report_actor registration
2025-05-11 16:44:57,473 - vibe_check_initialization_tracker - INFO - Actor report_actor registration
2025-05-11 16:44:57,473 - vibe_check_initialization_tracker - INFO - Actor visualization_actor registration
2025-05-11 16:44:57,473 - vibe_check_initialization_tracker - INFO - Actor visualization_actor registration
2025-05-11 16:44:57,473 - vibe_check_initialization_tracker - INFO - Actor tool_actor registration
2025-05-11 16:44:57,473 - vibe_check_initialization_tracker - INFO - Actor tool_actor registration
2025-05-11 16:44:57,473 - vibe_check_initialization_tracker - INFO - Actor file_actor_0 registration
2025-05-11 16:44:57,473 - vibe_check_initialization_tracker - INFO - Actor file_actor_0 registration
2025-05-11 16:44:57,473 - vibe_check_initialization_tracker - INFO - Actor file_actor_1 registration
2025-05-11 16:44:57,473 - vibe_check_initialization_tracker - INFO - Actor file_actor_1 registration
2025-05-11 16:44:57,473 - vibe_check_initialization_tracker - INFO - Actor file_actor_2 registration
2025-05-11 16:44:57,473 - vibe_check_initialization_tracker - INFO - Actor file_actor_2 registration
2025-05-11 16:44:57,473 - vibe_check_initialization_tracker - INFO - Actor file_actor_3 registration
2025-05-11 16:44:57,473 - vibe_check_initialization_tracker - INFO - Actor file_actor_3 registration
2025-05-11 16:44:57,473 - vibe_check_initialization_tracker - INFO - Actor file_actor_4 registration
2025-05-11 16:44:57,473 - vibe_check_initialization_tracker - INFO - Actor file_actor_4 registration
2025-05-11 16:44:57,473 - vibe_check_initialization_tracker - INFO - Actor file_actor_5 registration
2025-05-11 16:44:57,473 - vibe_check_initialization_tracker - INFO - Actor file_actor_5 registration
2025-05-11 16:44:57,473 - vibe_check_initialization_tracker - INFO - Actor file_actor_6 registration
2025-05-11 16:44:57,473 - vibe_check_initialization_tracker - INFO - Actor file_actor_6 registration
2025-05-11 16:44:57,473 - vibe_check_initialization_tracker - INFO - Actor file_actor_7 registration
2025-05-11 16:44:57,473 - vibe_check_initialization_tracker - INFO - Actor file_actor_7 registration
2025-05-11 16:44:57,473 - vibe_check_initialization_tracker - INFO - Actor file_actor_8 registration
2025-05-11 16:44:57,473 - vibe_check_initialization_tracker - INFO - Actor file_actor_8 registration
2025-05-11 16:44:57,473 - vibe_check_initialization_tracker - INFO - Actor file_actor_9 registration
2025-05-11 16:44:57,473 - vibe_check_initialization_tracker - INFO - Actor file_actor_9 registration
2025-05-11 16:44:57,473 - vibe_check_initialization_tracker - INFO - Actor file_actor_10 registration
2025-05-11 16:44:57,473 - vibe_check_initialization_tracker - INFO - Actor file_actor_10 registration
2025-05-11 16:44:57,473 - vibe_check_initialization_tracker - INFO - Actor file_actor_11 registration
2025-05-11 16:44:57,473 - vibe_check_initialization_tracker - INFO - Actor file_actor_11 registration
2025-05-11 16:44:57,473 - vibe_check_initialization_tracker - INFO - Actor file_actor_12 registration
2025-05-11 16:44:57,473 - vibe_check_initialization_tracker - INFO - Actor file_actor_12 registration
2025-05-11 16:44:57,473 - vibe_check_initialization_tracker - INFO - Actor file_actor_13 registration
2025-05-11 16:44:57,473 - vibe_check_initialization_tracker - INFO - Actor file_actor_13 registration
2025-05-11 16:44:57,473 - vibe_check_initialization_tracker - INFO - Actor project_actor registration
2025-05-11 16:44:57,473 - vibe_check_initialization_tracker - INFO - Actor project_actor registration
2025-05-11 16:44:57,473 - vibe_check_actor_initializer - INFO - Registered actor supervisor_actor with initializer
2025-05-11 16:44:57,473 - vibe_check_actor_system.supervisor_actor - INFO - Initialization phase 'registration' completed
2025-05-11 16:44:57,473 - vibe_check_actor_system.supervisor_actor - DEBUG - Initialization details: {'actor_type': None, 'tags': [], 'timestamp': 1746974697.473414}
2025-05-11 16:44:57,473 - vibe_check_actor_system.initializer - DEBUG - Component state: {'registered_actors': ['supervisor_actor'], 'actor_count': 1, 'timestamp': 1746974697.4734561}
2025-05-11 16:44:57,473 - vibe_check_synchronization - DEBUG - Actor supervisor_actor reached synchronization point registration_complete
2025-05-11 16:44:57,473 - vibe_check_actor_system - INFO - Actor supervisor_actor registered with new ActorInitializer
2025-05-11 16:44:57,473 - vibe_check_actor_initializer - INFO - Registered actor report_actor with initializer
2025-05-11 16:44:57,473 - vibe_check_actor_system.report_actor - INFO - Initialization phase 'registration' completed
2025-05-11 16:44:57,473 - vibe_check_actor_system.report_actor - DEBUG - Initialization details: {'actor_type': None, 'tags': [], 'timestamp': 1746974697.473505}
2025-05-11 16:44:57,473 - vibe_check_actor_system.initializer - DEBUG - Component state: {'registered_actors': ['supervisor_actor', 'report_actor'], 'actor_count': 2, 'timestamp': 1746974697.473548}
2025-05-11 16:44:57,473 - vibe_check_synchronization - DEBUG - Actor report_actor reached synchronization point registration_complete
2025-05-11 16:44:57,473 - vibe_check_actor_system - INFO - Actor report_actor registered with new ActorInitializer
2025-05-11 16:44:57,473 - vibe_check_actor_initializer - INFO - Registered actor visualization_actor with initializer
2025-05-11 16:44:57,473 - vibe_check_actor_system.visualization_actor - INFO - Initialization phase 'registration' completed
2025-05-11 16:44:57,473 - vibe_check_actor_system.visualization_actor - DEBUG - Initialization details: {'actor_type': None, 'tags': [], 'timestamp': 1746974697.473591}
2025-05-11 16:44:57,473 - vibe_check_actor_system.initializer - DEBUG - Component state: {'registered_actors': ['supervisor_actor', 'report_actor', 'visualization_actor'], 'actor_count': 3, 'timestamp': 1746974697.473623}
2025-05-11 16:44:57,473 - vibe_check_synchronization - DEBUG - Actor visualization_actor reached synchronization point registration_complete
2025-05-11 16:44:57,473 - vibe_check_actor_system - INFO - Actor visualization_actor registered with new ActorInitializer
2025-05-11 16:44:57,473 - vibe_check_actor_initializer - INFO - Registered actor tool_actor with initializer
2025-05-11 16:44:57,473 - vibe_check_actor_system.tool_actor - INFO - Initialization phase 'registration' completed
2025-05-11 16:44:57,473 - vibe_check_actor_system.tool_actor - DEBUG - Initialization details: {'actor_type': None, 'tags': [], 'timestamp': 1746974697.473665}
2025-05-11 16:44:57,473 - vibe_check_actor_system.initializer - DEBUG - Component state: {'registered_actors': ['supervisor_actor', 'report_actor', 'visualization_actor', 'tool_actor'], 'actor_count': 4, 'timestamp': 1746974697.473696}
2025-05-11 16:44:57,473 - vibe_check_synchronization - DEBUG - Actor tool_actor reached synchronization point registration_complete
2025-05-11 16:44:57,473 - vibe_check_actor_system - INFO - Actor tool_actor registered with new ActorInitializer
2025-05-11 16:44:57,473 - vibe_check_actor_initializer - INFO - Registered actor file_actor_0 with initializer
2025-05-11 16:44:57,473 - vibe_check_actor_system.file_actor_0 - INFO - Initialization phase 'registration' completed
2025-05-11 16:44:57,473 - vibe_check_actor_system.file_actor_0 - DEBUG - Initialization details: {'actor_type': None, 'tags': [], 'timestamp': 1746974697.473737}
2025-05-11 16:44:57,473 - vibe_check_actor_system.initializer - DEBUG - Component state: {'registered_actors': ['supervisor_actor', 'report_actor', 'visualization_actor', 'tool_actor', 'file_actor_0'], 'actor_count': 5, 'timestamp': 1746974697.473768}
2025-05-11 16:44:57,473 - vibe_check_synchronization - DEBUG - Actor file_actor_0 reached synchronization point registration_complete
2025-05-11 16:44:57,473 - vibe_check_actor_system - INFO - Actor file_actor_0 registered with new ActorInitializer
2025-05-11 16:44:57,473 - vibe_check_actor_initializer - INFO - Registered actor file_actor_1 with initializer
2025-05-11 16:44:57,473 - vibe_check_actor_system.file_actor_1 - INFO - Initialization phase 'registration' completed
2025-05-11 16:44:57,473 - vibe_check_actor_system.file_actor_1 - DEBUG - Initialization details: {'actor_type': None, 'tags': [], 'timestamp': 1746974697.4738078}
2025-05-11 16:44:57,473 - vibe_check_actor_system.initializer - DEBUG - Component state: {'registered_actors': ['supervisor_actor', 'report_actor', 'visualization_actor', 'tool_actor', 'file_actor_0', 'file_actor_1'], 'actor_count': 6, 'timestamp': 1746974697.473838}
2025-05-11 16:44:57,473 - vibe_check_synchronization - DEBUG - Actor file_actor_1 reached synchronization point registration_complete
2025-05-11 16:44:57,473 - vibe_check_actor_system - INFO - Actor file_actor_1 registered with new ActorInitializer
2025-05-11 16:44:57,473 - vibe_check_actor_initializer - INFO - Registered actor file_actor_2 with initializer
2025-05-11 16:44:57,473 - vibe_check_actor_system.file_actor_2 - INFO - Initialization phase 'registration' completed
2025-05-11 16:44:57,473 - vibe_check_actor_system.file_actor_2 - DEBUG - Initialization details: {'actor_type': None, 'tags': [], 'timestamp': 1746974697.473881}
2025-05-11 16:44:57,473 - vibe_check_actor_system.initializer - DEBUG - Component state: {'registered_actors': ['supervisor_actor', 'report_actor', 'visualization_actor', 'tool_actor', 'file_actor_0', 'file_actor_1', 'file_actor_2'], 'actor_count': 7, 'timestamp': 1746974697.473911}
2025-05-11 16:44:57,473 - vibe_check_synchronization - DEBUG - Actor file_actor_2 reached synchronization point registration_complete
2025-05-11 16:44:57,473 - vibe_check_actor_system - INFO - Actor file_actor_2 registered with new ActorInitializer
2025-05-11 16:44:57,473 - vibe_check_actor_initializer - INFO - Registered actor file_actor_3 with initializer
2025-05-11 16:44:57,473 - vibe_check_actor_system.file_actor_3 - INFO - Initialization phase 'registration' completed
2025-05-11 16:44:57,473 - vibe_check_actor_system.file_actor_3 - DEBUG - Initialization details: {'actor_type': None, 'tags': [], 'timestamp': 1746974697.4739509}
2025-05-11 16:44:57,473 - vibe_check_actor_system.initializer - DEBUG - Component state: {'registered_actors': ['supervisor_actor', 'report_actor', 'visualization_actor', 'tool_actor', 'file_actor_0', 'file_actor_1', 'file_actor_2', 'file_actor_3'], 'actor_count': 8, 'timestamp': 1746974697.473981}
2025-05-11 16:44:57,473 - vibe_check_synchronization - DEBUG - Actor file_actor_3 reached synchronization point registration_complete
2025-05-11 16:44:57,474 - vibe_check_actor_system - INFO - Actor file_actor_3 registered with new ActorInitializer
2025-05-11 16:44:57,474 - vibe_check_actor_initializer - INFO - Registered actor file_actor_4 with initializer
2025-05-11 16:44:57,474 - vibe_check_actor_system.file_actor_4 - INFO - Initialization phase 'registration' completed
2025-05-11 16:44:57,474 - vibe_check_actor_system.file_actor_4 - DEBUG - Initialization details: {'actor_type': None, 'tags': [], 'timestamp': 1746974697.4740212}
2025-05-11 16:44:57,474 - vibe_check_actor_system.initializer - DEBUG - Component state: {'registered_actors': ['supervisor_actor', 'report_actor', 'visualization_actor', 'tool_actor', 'file_actor_0', 'file_actor_1', 'file_actor_2', 'file_actor_3', 'file_actor_4'], 'actor_count': 9, 'timestamp': 1746974697.474051}
2025-05-11 16:44:57,474 - vibe_check_synchronization - DEBUG - Actor file_actor_4 reached synchronization point registration_complete
2025-05-11 16:44:57,474 - vibe_check_actor_system - INFO - Actor file_actor_4 registered with new ActorInitializer
2025-05-11 16:44:57,474 - vibe_check_actor_initializer - INFO - Registered actor file_actor_5 with initializer
2025-05-11 16:44:57,474 - vibe_check_actor_system.file_actor_5 - INFO - Initialization phase 'registration' completed
2025-05-11 16:44:57,474 - vibe_check_actor_system.file_actor_5 - DEBUG - Initialization details: {'actor_type': None, 'tags': [], 'timestamp': 1746974697.47409}
2025-05-11 16:44:57,474 - vibe_check_actor_system.initializer - DEBUG - Component state: {'registered_actors': ['supervisor_actor', 'report_actor', 'visualization_actor', 'tool_actor', 'file_actor_0', 'file_actor_1', 'file_actor_2', 'file_actor_3', 'file_actor_4', 'file_actor_5'], 'actor_count': 10, 'timestamp': 1746974697.4741209}
2025-05-11 16:44:57,474 - vibe_check_synchronization - DEBUG - Actor file_actor_5 reached synchronization point registration_complete
2025-05-11 16:44:57,474 - vibe_check_actor_system - INFO - Actor file_actor_5 registered with new ActorInitializer
2025-05-11 16:44:57,474 - vibe_check_actor_initializer - INFO - Registered actor file_actor_6 with initializer
2025-05-11 16:44:57,474 - vibe_check_actor_system.file_actor_6 - INFO - Initialization phase 'registration' completed
2025-05-11 16:44:57,474 - vibe_check_actor_system.file_actor_6 - DEBUG - Initialization details: {'actor_type': None, 'tags': [], 'timestamp': 1746974697.474161}
2025-05-11 16:44:57,474 - vibe_check_actor_system.initializer - DEBUG - Component state: {'registered_actors': ['supervisor_actor', 'report_actor', 'visualization_actor', 'tool_actor', 'file_actor_0', 'file_actor_1', 'file_actor_2', 'file_actor_3', 'file_actor_4', 'file_actor_5', 'file_actor_6'], 'actor_count': 11, 'timestamp': 1746974697.474195}
2025-05-11 16:44:57,474 - vibe_check_synchronization - DEBUG - Actor file_actor_6 reached synchronization point registration_complete
2025-05-11 16:44:57,474 - vibe_check_actor_system - INFO - Actor file_actor_6 registered with new ActorInitializer
2025-05-11 16:44:57,474 - vibe_check_actor_initializer - INFO - Registered actor file_actor_7 with initializer
2025-05-11 16:44:57,474 - vibe_check_actor_system.file_actor_7 - INFO - Initialization phase 'registration' completed
2025-05-11 16:44:57,474 - vibe_check_actor_system.file_actor_7 - DEBUG - Initialization details: {'actor_type': None, 'tags': [], 'timestamp': 1746974697.4742348}
2025-05-11 16:44:57,474 - vibe_check_actor_system.initializer - DEBUG - Component state: {'registered_actors': ['supervisor_actor', 'report_actor', 'visualization_actor', 'tool_actor', 'file_actor_0', 'file_actor_1', 'file_actor_2', 'file_actor_3', 'file_actor_4', 'file_actor_5', 'file_actor_6', 'file_actor_7'], 'actor_count': 12, 'timestamp': 1746974697.474266}
2025-05-11 16:44:57,474 - vibe_check_synchronization - DEBUG - Actor file_actor_7 reached synchronization point registration_complete
2025-05-11 16:44:57,474 - vibe_check_actor_system - INFO - Actor file_actor_7 registered with new ActorInitializer
2025-05-11 16:44:57,474 - vibe_check_actor_initializer - INFO - Registered actor file_actor_8 with initializer
2025-05-11 16:44:57,474 - vibe_check_actor_system.file_actor_8 - INFO - Initialization phase 'registration' completed
2025-05-11 16:44:57,474 - vibe_check_actor_system.file_actor_8 - DEBUG - Initialization details: {'actor_type': None, 'tags': [], 'timestamp': 1746974697.474306}
2025-05-11 16:44:57,474 - vibe_check_actor_system.initializer - DEBUG - Component state: {'registered_actors': ['supervisor_actor', 'report_actor', 'visualization_actor', 'tool_actor', 'file_actor_0', 'file_actor_1', 'file_actor_2', 'file_actor_3', 'file_actor_4', 'file_actor_5', 'file_actor_6', 'file_actor_7', 'file_actor_8'], 'actor_count': 13, 'timestamp': 1746974697.474345}
2025-05-11 16:44:57,474 - vibe_check_synchronization - DEBUG - Actor file_actor_8 reached synchronization point registration_complete
2025-05-11 16:44:57,474 - vibe_check_actor_system - INFO - Actor file_actor_8 registered with new ActorInitializer
2025-05-11 16:44:57,474 - vibe_check_actor_initializer - INFO - Registered actor file_actor_9 with initializer
2025-05-11 16:44:57,474 - vibe_check_actor_system.file_actor_9 - INFO - Initialization phase 'registration' completed
2025-05-11 16:44:57,474 - vibe_check_actor_system.file_actor_9 - DEBUG - Initialization details: {'actor_type': None, 'tags': [], 'timestamp': 1746974697.4743848}
2025-05-11 16:44:57,474 - vibe_check_actor_system.initializer - DEBUG - Component state: {'registered_actors': ['supervisor_actor', 'report_actor', 'visualization_actor', 'tool_actor', 'file_actor_0', 'file_actor_1', 'file_actor_2', 'file_actor_3', 'file_actor_4', 'file_actor_5', 'file_actor_6', 'file_actor_7', 'file_actor_8', 'file_actor_9'], 'actor_count': 14, 'timestamp': 1746974697.474416}
2025-05-11 16:44:57,474 - vibe_check_synchronization - DEBUG - Actor file_actor_9 reached synchronization point registration_complete
2025-05-11 16:44:57,474 - vibe_check_actor_system - INFO - Actor file_actor_9 registered with new ActorInitializer
2025-05-11 16:44:57,474 - vibe_check_actor_initializer - INFO - Registered actor file_actor_10 with initializer
2025-05-11 16:44:57,474 - vibe_check_actor_system.file_actor_10 - INFO - Initialization phase 'registration' completed
2025-05-11 16:44:57,474 - vibe_check_actor_system.file_actor_10 - DEBUG - Initialization details: {'actor_type': None, 'tags': [], 'timestamp': 1746974697.474455}
2025-05-11 16:44:57,474 - vibe_check_actor_system.initializer - DEBUG - Component state: {'registered_actors': ['supervisor_actor', 'report_actor', 'visualization_actor', 'tool_actor', 'file_actor_0', 'file_actor_1', 'file_actor_2', 'file_actor_3', 'file_actor_4', 'file_actor_5', 'file_actor_6', 'file_actor_7', 'file_actor_8', 'file_actor_9', 'file_actor_10'], 'actor_count': 15, 'timestamp': 1746974697.474488}
2025-05-11 16:44:57,474 - vibe_check_synchronization - DEBUG - Actor file_actor_10 reached synchronization point registration_complete
2025-05-11 16:44:57,474 - vibe_check_actor_system - INFO - Actor file_actor_10 registered with new ActorInitializer
2025-05-11 16:44:57,474 - vibe_check_actor_initializer - INFO - Registered actor file_actor_11 with initializer
2025-05-11 16:44:57,474 - vibe_check_actor_system.file_actor_11 - INFO - Initialization phase 'registration' completed
2025-05-11 16:44:57,474 - vibe_check_actor_system.file_actor_11 - DEBUG - Initialization details: {'actor_type': None, 'tags': [], 'timestamp': 1746974697.474527}
2025-05-11 16:44:57,474 - vibe_check_actor_system.initializer - DEBUG - Component state: {'registered_actors': ['supervisor_actor', 'report_actor', 'visualization_actor', 'tool_actor', 'file_actor_0', 'file_actor_1', 'file_actor_2', 'file_actor_3', 'file_actor_4', 'file_actor_5', 'file_actor_6', 'file_actor_7', 'file_actor_8', 'file_actor_9', 'file_actor_10', 'file_actor_11'], 'actor_count': 16, 'timestamp': 1746974697.474557}
2025-05-11 16:44:57,474 - vibe_check_synchronization - DEBUG - Actor file_actor_11 reached synchronization point registration_complete
2025-05-11 16:44:57,474 - vibe_check_actor_system - INFO - Actor file_actor_11 registered with new ActorInitializer
2025-05-11 16:44:57,474 - vibe_check_actor_initializer - INFO - Registered actor file_actor_12 with initializer
2025-05-11 16:44:57,474 - vibe_check_actor_system.file_actor_12 - INFO - Initialization phase 'registration' completed
2025-05-11 16:44:57,474 - vibe_check_actor_system.file_actor_12 - DEBUG - Initialization details: {'actor_type': None, 'tags': [], 'timestamp': 1746974697.474596}
2025-05-11 16:44:57,474 - vibe_check_actor_system.initializer - DEBUG - Component state: {'registered_actors': ['supervisor_actor', 'report_actor', 'visualization_actor', 'tool_actor', 'file_actor_0', 'file_actor_1', 'file_actor_2', 'file_actor_3', 'file_actor_4', 'file_actor_5', 'file_actor_6', 'file_actor_7', 'file_actor_8', 'file_actor_9', 'file_actor_10', 'file_actor_11', 'file_actor_12'], 'actor_count': 17, 'timestamp': 1746974697.474626}
2025-05-11 16:44:57,474 - vibe_check_synchronization - DEBUG - Actor file_actor_12 reached synchronization point registration_complete
2025-05-11 16:44:57,474 - vibe_check_actor_system - INFO - Actor file_actor_12 registered with new ActorInitializer
2025-05-11 16:44:57,474 - vibe_check_actor_initializer - INFO - Registered actor file_actor_13 with initializer
2025-05-11 16:44:57,474 - vibe_check_actor_system.file_actor_13 - INFO - Initialization phase 'registration' completed
2025-05-11 16:44:57,474 - vibe_check_actor_system.file_actor_13 - DEBUG - Initialization details: {'actor_type': None, 'tags': [], 'timestamp': 1746974697.4746659}
2025-05-11 16:44:57,474 - vibe_check_actor_system.initializer - DEBUG - Component state: {'registered_actors': ['supervisor_actor', 'report_actor', 'visualization_actor', 'tool_actor', 'file_actor_0', 'file_actor_1', 'file_actor_2', 'file_actor_3', 'file_actor_4', 'file_actor_5', 'file_actor_6', 'file_actor_7', 'file_actor_8', 'file_actor_9', 'file_actor_10', 'file_actor_11', 'file_actor_12', 'file_actor_13'], 'actor_count': 18, 'timestamp': 1746974697.474696}
2025-05-11 16:44:57,474 - vibe_check_synchronization - DEBUG - Actor file_actor_13 reached synchronization point registration_complete
2025-05-11 16:44:57,474 - vibe_check_actor_system - INFO - Actor file_actor_13 registered with new ActorInitializer
2025-05-11 16:44:57,474 - vibe_check_actor_initializer - INFO - Registered actor project_actor with initializer
2025-05-11 16:44:57,474 - vibe_check_actor_system.project_actor - INFO - Initialization phase 'registration' completed
2025-05-11 16:44:57,474 - vibe_check_actor_system.project_actor - DEBUG - Initialization details: {'actor_type': None, 'tags': [], 'timestamp': 1746974697.474735}
2025-05-11 16:44:57,474 - vibe_check_actor_system.initializer - DEBUG - Component state: {'registered_actors': ['supervisor_actor', 'report_actor', 'visualization_actor', 'tool_actor', 'file_actor_0', 'file_actor_1', 'file_actor_2', 'file_actor_3', 'file_actor_4', 'file_actor_5', 'file_actor_6', 'file_actor_7', 'file_actor_8', 'file_actor_9', 'file_actor_10', 'file_actor_11', 'file_actor_12', 'file_actor_13', 'project_actor'], 'actor_count': 19, 'timestamp': 1746974697.4747689}
2025-05-11 16:44:57,474 - vibe_check_synchronization - DEBUG - Actor project_actor reached synchronization point registration_complete
2025-05-11 16:44:57,474 - vibe_check_actor_system - INFO - Actor project_actor registered with new ActorInitializer
2025-05-11 16:44:57,474 - vibe_check_actor_system - INFO - Actor supervisor_actor registration task with new manager completed
2025-05-11 16:44:57,474 - vibe_check_actor_system - INFO - Actor supervisor_actor is already registered with legacy initializer
2025-05-11 16:44:57,474 - vibe_check_actor_initialization - INFO - Actor supervisor_actor state changed: created -> initializing
2025-05-11 16:44:57,474 - vibe_check_actor_initializer - DEBUG - Actor supervisor_actor is already in state initializing, no change needed
2025-05-11 16:44:57,474 - vibe_check_initialization_tracker - INFO - Actor supervisor_actor initialization_start
2025-05-11 16:44:57,474 - vibe_check_actor_system - INFO - No state file found for actor supervisor_actor
2025-05-11 16:44:57,474 - vibe_check_actor_initialization - INFO - Actor supervisor_actor state changed: initializing -> initialized
2025-05-11 16:44:57,474 - vibe_check_actor_initializer - INFO - Actor supervisor_actor state changed: initializing -> initialized
2025-05-11 16:44:57,474 - vibe_check_initialization_tracker - INFO - Actor supervisor_actor initialization_complete
2025-05-11 16:44:57,474 - vibe_check_actor_system - INFO - Actor supervisor_actor initialized successfully
2025-05-11 16:44:57,475 - vibe_check_actor_system - INFO - Actor supervisor_actor is already registered with initialization manager
2025-05-11 16:44:57,475 - vibe_check_actor_system - INFO - Actor supervisor_actor is already registered with legacy initializer
2025-05-11 16:44:57,475 - vibe_check_actor_initializer - INFO - Actor supervisor_actor state changed: REGISTERED -> INITIALIZING
2025-05-11 16:44:57,475 - vibe_check_actor_system.supervisor_actor - INFO - State transition: REGISTERED -> INITIALIZING
2025-05-11 16:44:57,475 - vibe_check_actor_system.supervisor_actor - DEBUG - State transition details: {'phase': 'initialize'}
2025-05-11 16:44:57,475 - vibe_check_actor_system.supervisor_actor - DEBUG - Operation 'state_transition_REGISTERED_to_INITIALIZING' took 0.000032 seconds
2025-05-11 16:44:57,475 - vibe_check_actor_system.initializer - DEBUG - Component state: {'actor_id': 'supervisor_actor', 'state': 'INITIALIZING', 'previous_state': 'REGISTERED', 'timestamp': 1746974697.475044, 'details': {'phase': 'initialize'}}
2025-05-11 16:44:57,475 - vibe_check_actor_system - INFO - No state file found for actor supervisor_actor
2025-05-11 16:44:57,475 - vibe_check_actor_registry - WARNING - Actor supervisor_actor already registered, updating
2025-05-11 16:44:57,475 - vibe_check_actor_registry - INFO - Registered actor supervisor_actor of type supervisor
2025-05-11 16:44:57,475 - vibe_check_actor_initializer - INFO - Actor supervisor_actor state changed: INITIALIZING -> INITIALIZED
2025-05-11 16:44:57,475 - vibe_check_actor_system.supervisor_actor - INFO - State transition: INITIALIZING -> INITIALIZED
2025-05-11 16:44:57,475 - vibe_check_actor_system.supervisor_actor - DEBUG - State transition details: {'phase': 'initialize'}
2025-05-11 16:44:57,475 - vibe_check_actor_system.supervisor_actor - DEBUG - Operation 'state_transition_INITIALIZING_to_INITIALIZED' took 0.000028 seconds
2025-05-11 16:44:57,475 - vibe_check_actor_system.initializer - DEBUG - Component state: {'actor_id': 'supervisor_actor', 'state': 'INITIALIZED', 'previous_state': 'INITIALIZING', 'timestamp': 1746974697.4751449, 'details': {'phase': 'initialize'}}
2025-05-11 16:44:57,475 - vibe_check_synchronization - DEBUG - Actor supervisor_actor reached synchronization point initialization_complete
2025-05-11 16:44:57,475 - vibe_check_actor_system - INFO - Actor supervisor_actor initialized successfully
2025-05-11 16:44:57,475 - vibe_check_actor_system.actor.supervisor_actor - ERROR - Error during state transition to FAILED: Actor supervisor_actor: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds
2025-05-11 16:44:57,475 - vibe_check_actor_system.actor.supervisor_actor - ERROR - Traceback:
NoneType: None

2025-05-11 16:44:57,475 - vibe_check_actor_initializer - INFO - Actor supervisor_actor state changed: INITIALIZED -> FAILED
2025-05-11 16:44:57,475 - vibe_check_actor_system.supervisor_actor - INFO - State transition: INITIALIZED -> FAILED
2025-05-11 16:44:57,475 - vibe_check_actor_system.supervisor_actor - DEBUG - State transition details: {'phase': 'initialize'}
2025-05-11 16:44:57,475 - vibe_check_actor_system.supervisor_actor - DEBUG - Operation 'state_transition_INITIALIZED_to_FAILED' took 0.000136 seconds
2025-05-11 16:44:57,475 - vibe_check_actor_system.initializer - DEBUG - Component state: {'actor_id': 'supervisor_actor', 'state': 'FAILED', 'previous_state': 'INITIALIZED', 'timestamp': 1746974697.47523, 'details': {'phase': 'initialize'}}
2025-05-11 16:44:57,475 - vibe_check_actor_initializer - DEBUG - Cleaned up resources for actor supervisor_actor
2025-05-11 16:44:57,475 - vibe_check_actor_system - INFO - Cleaning up resources for actor supervisor_actor
2025-05-11 16:44:57,475 - vibe_check_actor_system - INFO - Cleared mailbox for actor supervisor_actor
2025-05-11 16:44:57,475 - vibe_check_actor_registry - INFO - Unregistered actor supervisor_actor
2025-05-11 16:44:57,475 - vibe_check_actor_system - INFO - Unregistered actor supervisor_actor from registry during cleanup
2025-05-11 16:44:57,475 - vibe_check_actor_initialization - INFO - Actor supervisor_actor state changed: initialized -> stopped
2025-05-11 16:44:57,475 - vibe_check_actor_system - INFO - Set actor supervisor_actor state to STOPPED in initialization manager
2025-05-11 16:44:57,475 - vibe_check_actor_initializer - ERROR - Invalid state transition for actor supervisor_actor: initialized -> stopped
2025-05-11 16:44:57,475 - vibe_check_actor_initializer - DEBUG - Valid transitions from initialized are: ['stopping', 'starting', 'rollback', 'failed']
2025-05-11 16:44:57,475 - vibe_check_actor_initializer - WARNING - Forcing transition to stopped state for actor supervisor_actor
2025-05-11 16:44:57,475 - vibe_check_actor_initializer - INFO - Actor supervisor_actor state changed: initialized -> stopped
2025-05-11 16:44:57,475 - vibe_check_actor_system - INFO - Set actor supervisor_actor state to STOPPED in legacy initializer
2025-05-11 16:44:57,475 - vibe_check_actor_system - INFO - Cleaned up resources for actor supervisor_actor
2025-05-11 16:44:57,475 - vibe_check_actor_system - ERROR - Error in initialization process for actor supervisor_actor: Actor supervisor_actor: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds
2025-05-11 16:44:57,475 - vibe_check_actor_initializer - ERROR - Actor supervisor_actor: Invalid state transition: FAILED -> FAILED
2025-05-11 16:44:57,475 - vibe_check_actor_system - ERROR - Error rolling back actor supervisor_actor: Actor supervisor_actor: Invalid state transition: FAILED -> FAILED (state: FAILED)
2025-05-11 16:44:57,475 - vibe_check_actor_system - INFO - Cleaning up resources for actor supervisor_actor
2025-05-11 16:44:57,475 - vibe_check_actor_system - INFO - Cleared mailbox for actor supervisor_actor
2025-05-11 16:44:57,475 - vibe_check_actor_registry - WARNING - Actor supervisor_actor not registered, cannot unregister
2025-05-11 16:44:57,475 - vibe_check_actor_system - INFO - Unregistered actor supervisor_actor from registry during cleanup
2025-05-11 16:44:57,475 - vibe_check_actor_initialization - DEBUG - Actor supervisor_actor is already in state stopped, no change needed
2025-05-11 16:44:57,475 - vibe_check_actor_system - INFO - Set actor supervisor_actor state to STOPPED in initialization manager
2025-05-11 16:44:57,475 - vibe_check_actor_initializer - DEBUG - Actor supervisor_actor is already in state stopped, no change needed
2025-05-11 16:44:57,475 - vibe_check_actor_initializer - INFO - Actor supervisor_actor state changed: stopped -> stopped
2025-05-11 16:44:57,475 - vibe_check_actor_system - INFO - Set actor supervisor_actor state to STOPPED in legacy initializer
2025-05-11 16:44:57,475 - vibe_check_actor_system - INFO - Cleaned up resources for actor supervisor_actor
2025-05-11 16:44:57,475 - vibe_check_actor_initializer - ERROR - [ACTOR INIT] Failed to initialize actor supervisor_actor: Actor supervisor_actor: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds
2025-05-11 16:44:57,476 - vibe_check_actor_initializer - ERROR - [ACTOR INIT] Traceback for actor supervisor_actor:
Traceback (most recent call last):
  File "/Users/<USER>/Lokalne_Kody/PAT_project_analysis/vibe_check/core/actor_system/actor_initializer.py", line 936, in initialize_actor
    await asyncio.wait_for(actor.initialize(), timeout)
  File "/Users/<USER>/.pyenv/versions/3.13.3/lib/python3.13/asyncio/tasks.py", line 507, in wait_for
    return await fut
           ^^^^^^^^^
  File "/Users/<USER>/Lokalne_Kody/PAT_project_analysis/vibe_check/core/actor_system/actor.py", line 1109, in initialize
    raise timeout_error
vibe_check.core.actor_system.exceptions.ActorTimeoutError: Actor supervisor_actor: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds

2025-05-11 16:44:57,476 - vibe_check_actor_initializer - DEBUG - [ACTOR INIT] Setting actor supervisor_actor state to FAILED
2025-05-11 16:44:57,476 - vibe_check_actor_initializer - ERROR - Invalid state transition for actor supervisor_actor: stopped -> failed
2025-05-11 16:44:57,476 - vibe_check_actor_initializer - DEBUG - Valid transitions from stopped are: ['initializing', 'created']
2025-05-11 16:44:57,476 - vibe_check_actor_initializer - WARNING - Forcing transition to failed state for actor supervisor_actor
2025-05-11 16:44:57,476 - vibe_check_actor_initializer - INFO - Actor supervisor_actor state changed: stopped -> failed
2025-05-11 16:44:57,476 - vibe_check_actor_initializer - ERROR - Actor supervisor_actor failed: Actor supervisor_actor failed during initialize phase (state: stopped): Actor supervisor_actor: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds
Original error: Actor supervisor_actor: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds
2025-05-11 16:44:57,476 - vibe_check_actor_lifecycle_manager - ERROR - Failed to initialize supervisor actor: Actor supervisor_actor: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds
2025-05-11 16:44:57,476 - vibe_check_actor_lifecycle_manager - INFO - Found 19 actors of 5 types: ['report', 'visualization', 'tool', 'file', 'project']
2025-05-11 16:44:57,476 - vibe_check_actor_lifecycle_manager - INFO - Initializing 1 actors of type report
2025-05-11 16:44:57,476 - vibe_check_actor_lifecycle_manager - INFO - Initializing actor report_actor (type: report)
2025-05-11 16:44:57,476 - vibe_check_actor_initializer - INFO - [ACTOR INIT] Starting initialization of actor report_actor
2025-05-11 16:44:57,476 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting registry for actor report_actor
2025-05-11 16:44:57,476 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting actor report_actor from registry
2025-05-11 16:44:57,476 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Successfully retrieved actor report_actor from registry
2025-05-11 16:44:57,476 - vibe_check_actor_initializer - INFO - [ACTOR INIT] Actor report_actor is of type report
2025-05-11 16:44:57,476 - vibe_check_actor_initializer - DEBUG - [ACTOR INIT] Setting actor report_actor state to INITIALIZING
2025-05-11 16:44:57,476 - vibe_check_actor_initializer - INFO - Actor report_actor state changed: created -> initializing
2025-05-11 16:44:57,476 - vibe_check_actor_initializer - DEBUG - [ACTOR INIT] Calling initialize() method on actor report_actor
2025-05-11 16:44:57,476 - vibe_check_actor_system - INFO - Actor report_actor is already registered with initialization manager
2025-05-11 16:44:57,476 - vibe_check_actor_system - INFO - Actor report_actor is already registered with legacy initializer
2025-05-11 16:44:57,476 - vibe_check_actor_initialization - INFO - Actor report_actor state changed: created -> initializing
2025-05-11 16:44:57,476 - vibe_check_actor_initializer - DEBUG - Actor report_actor is already in state initializing, no change needed
2025-05-11 16:44:57,476 - vibe_check_initialization_tracker - INFO - Actor report_actor initialization_start
2025-05-11 16:44:57,476 - vibe_check_actor_system - INFO - No state file found for actor report_actor
2025-05-11 16:44:57,476 - vibe_check_actor_system - DEBUG - Actor report_actor has no _initialize method
2025-05-11 16:44:57,476 - vibe_check_actor_initialization - INFO - Actor report_actor state changed: initializing -> initialized
2025-05-11 16:44:57,476 - vibe_check_actor_initializer - INFO - Actor report_actor state changed: initializing -> initialized
2025-05-11 16:44:57,476 - vibe_check_initialization_tracker - INFO - Actor report_actor initialization_complete
2025-05-11 16:44:57,476 - vibe_check_actor_system - INFO - Actor report_actor initialized successfully
2025-05-11 16:44:57,476 - vibe_check_actor_system - INFO - Actor report_actor is already registered with initialization manager
2025-05-11 16:44:57,476 - vibe_check_actor_system - INFO - Actor report_actor is already registered with legacy initializer
2025-05-11 16:44:57,476 - vibe_check_actor_initializer - INFO - Actor report_actor state changed: REGISTERED -> INITIALIZING
2025-05-11 16:44:57,476 - vibe_check_actor_system.report_actor - INFO - State transition: REGISTERED -> INITIALIZING
2025-05-11 16:44:57,476 - vibe_check_actor_system.report_actor - DEBUG - State transition details: {'phase': 'initialize'}
2025-05-11 16:44:57,476 - vibe_check_actor_system.report_actor - DEBUG - Operation 'state_transition_REGISTERED_to_INITIALIZING' took 0.000030 seconds
2025-05-11 16:44:57,477 - vibe_check_actor_system.initializer - DEBUG - Component state: {'actor_id': 'report_actor', 'state': 'INITIALIZING', 'previous_state': 'REGISTERED', 'timestamp': 1746974697.4769652, 'details': {'phase': 'initialize'}}
2025-05-11 16:44:57,477 - vibe_check_actor_system - INFO - No state file found for actor report_actor
2025-05-11 16:44:57,477 - vibe_check_actor_registry - WARNING - Actor report_actor already registered, updating
2025-05-11 16:44:57,477 - vibe_check_actor_registry - INFO - Registered actor report_actor of type report
2025-05-11 16:44:57,477 - vibe_check_actor_system - DEBUG - Actor report_actor has no _initialize method
2025-05-11 16:44:57,477 - vibe_check_actor_initializer - INFO - Actor report_actor state changed: INITIALIZING -> INITIALIZED
2025-05-11 16:44:57,477 - vibe_check_actor_system.report_actor - INFO - State transition: INITIALIZING -> INITIALIZED
2025-05-11 16:44:57,477 - vibe_check_actor_system.report_actor - DEBUG - State transition details: {'phase': 'initialize'}
2025-05-11 16:44:57,477 - vibe_check_actor_system.report_actor - DEBUG - Operation 'state_transition_INITIALIZING_to_INITIALIZED' took 0.000027 seconds
2025-05-11 16:44:57,477 - vibe_check_actor_system.initializer - DEBUG - Component state: {'actor_id': 'report_actor', 'state': 'INITIALIZED', 'previous_state': 'INITIALIZING', 'timestamp': 1746974697.477071, 'details': {'phase': 'initialize'}}
2025-05-11 16:44:57,477 - vibe_check_synchronization - DEBUG - Actor report_actor reached synchronization point initialization_complete
2025-05-11 16:44:57,477 - vibe_check_actor_system - INFO - Actor report_actor initialized successfully
2025-05-11 16:44:57,477 - vibe_check_actor_system.actor.report_actor - ERROR - Error during state transition to FAILED: Actor report_actor: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds
2025-05-11 16:44:57,477 - vibe_check_actor_system.actor.report_actor - ERROR - Traceback:
NoneType: None

2025-05-11 16:44:57,477 - vibe_check_actor_initializer - INFO - Actor report_actor state changed: INITIALIZED -> FAILED
2025-05-11 16:44:57,477 - vibe_check_actor_system.report_actor - INFO - State transition: INITIALIZED -> FAILED
2025-05-11 16:44:57,477 - vibe_check_actor_system.report_actor - DEBUG - State transition details: {'phase': 'initialize'}
2025-05-11 16:44:57,477 - vibe_check_actor_system.report_actor - DEBUG - Operation 'state_transition_INITIALIZED_to_FAILED' took 0.000069 seconds
2025-05-11 16:44:57,477 - vibe_check_actor_system.initializer - DEBUG - Component state: {'actor_id': 'report_actor', 'state': 'FAILED', 'previous_state': 'INITIALIZED', 'timestamp': 1746974697.477155, 'details': {'phase': 'initialize'}}
2025-05-11 16:44:57,477 - vibe_check_actor_initializer - DEBUG - Cleaned up resources for actor report_actor
2025-05-11 16:44:57,477 - vibe_check_actor_system - INFO - Cleaning up resources for actor report_actor
2025-05-11 16:44:57,477 - vibe_check_actor_system - INFO - Cleared mailbox for actor report_actor
2025-05-11 16:44:57,477 - vibe_check_actor_registry - INFO - Unregistered actor report_actor
2025-05-11 16:44:57,477 - vibe_check_actor_system - INFO - Unregistered actor report_actor from registry during cleanup
2025-05-11 16:44:57,477 - vibe_check_actor_initialization - INFO - Actor report_actor state changed: initialized -> stopped
2025-05-11 16:44:57,477 - vibe_check_actor_system - INFO - Set actor report_actor state to STOPPED in initialization manager
2025-05-11 16:44:57,477 - vibe_check_actor_initializer - ERROR - Invalid state transition for actor report_actor: initialized -> stopped
2025-05-11 16:44:57,477 - vibe_check_actor_initializer - DEBUG - Valid transitions from initialized are: ['stopping', 'starting', 'rollback', 'failed']
2025-05-11 16:44:57,477 - vibe_check_actor_initializer - WARNING - Forcing transition to stopped state for actor report_actor
2025-05-11 16:44:57,477 - vibe_check_actor_initializer - INFO - Actor report_actor state changed: initialized -> stopped
2025-05-11 16:44:57,477 - vibe_check_actor_system - INFO - Set actor report_actor state to STOPPED in legacy initializer
2025-05-11 16:44:57,477 - vibe_check_actor_system - INFO - Cleaned up resources for actor report_actor
2025-05-11 16:44:57,477 - vibe_check_actor_system - ERROR - Error in initialization process for actor report_actor: Actor report_actor: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds
2025-05-11 16:44:57,477 - vibe_check_actor_initializer - ERROR - Actor report_actor: Invalid state transition: FAILED -> FAILED
2025-05-11 16:44:57,477 - vibe_check_actor_system - ERROR - Error rolling back actor report_actor: Actor report_actor: Invalid state transition: FAILED -> FAILED (state: FAILED)
2025-05-11 16:44:57,477 - vibe_check_actor_system - INFO - Cleaning up resources for actor report_actor
2025-05-11 16:44:57,477 - vibe_check_actor_system - INFO - Cleared mailbox for actor report_actor
2025-05-11 16:44:57,477 - vibe_check_actor_registry - WARNING - Actor report_actor not registered, cannot unregister
2025-05-11 16:44:57,477 - vibe_check_actor_system - INFO - Unregistered actor report_actor from registry during cleanup
2025-05-11 16:44:57,477 - vibe_check_actor_initialization - DEBUG - Actor report_actor is already in state stopped, no change needed
2025-05-11 16:44:57,477 - vibe_check_actor_system - INFO - Set actor report_actor state to STOPPED in initialization manager
2025-05-11 16:44:57,477 - vibe_check_actor_initializer - DEBUG - Actor report_actor is already in state stopped, no change needed
2025-05-11 16:44:57,477 - vibe_check_actor_initializer - INFO - Actor report_actor state changed: stopped -> stopped
2025-05-11 16:44:57,477 - vibe_check_actor_system - INFO - Set actor report_actor state to STOPPED in legacy initializer
2025-05-11 16:44:57,477 - vibe_check_actor_system - INFO - Cleaned up resources for actor report_actor
2025-05-11 16:44:57,477 - vibe_check_actor_initializer - ERROR - [ACTOR INIT] Failed to initialize actor report_actor: Actor report_actor: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds
2025-05-11 16:44:57,477 - vibe_check_actor_initializer - ERROR - [ACTOR INIT] Traceback for actor report_actor:
Traceback (most recent call last):
  File "/Users/<USER>/Lokalne_Kody/PAT_project_analysis/vibe_check/core/actor_system/actor_initializer.py", line 936, in initialize_actor
    await asyncio.wait_for(actor.initialize(), timeout)
  File "/Users/<USER>/.pyenv/versions/3.13.3/lib/python3.13/asyncio/tasks.py", line 507, in wait_for
    return await fut
           ^^^^^^^^^
  File "/Users/<USER>/Lokalne_Kody/PAT_project_analysis/vibe_check/core/actor_system/actor.py", line 1109, in initialize
    raise timeout_error
vibe_check.core.actor_system.exceptions.ActorTimeoutError: Actor report_actor: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds

2025-05-11 16:44:57,477 - vibe_check_actor_initializer - DEBUG - [ACTOR INIT] Setting actor report_actor state to FAILED
2025-05-11 16:44:57,477 - vibe_check_actor_initializer - ERROR - Invalid state transition for actor report_actor: stopped -> failed
2025-05-11 16:44:57,477 - vibe_check_actor_initializer - DEBUG - Valid transitions from stopped are: ['initializing', 'created']
2025-05-11 16:44:57,477 - vibe_check_actor_initializer - WARNING - Forcing transition to failed state for actor report_actor
2025-05-11 16:44:57,477 - vibe_check_actor_initializer - INFO - Actor report_actor state changed: stopped -> failed
2025-05-11 16:44:57,477 - vibe_check_actor_initializer - ERROR - Actor report_actor failed: Actor report_actor failed during initialize phase (state: stopped): Actor report_actor: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds
Original error: Actor report_actor: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds
2025-05-11 16:44:57,477 - vibe_check_actor_lifecycle_manager - WARNING - Failed to initialize actor report_actor (attempt 1/3): Actor report_actor: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds
2025-05-11 16:44:57,477 - vibe_check_initialization_tracker - INFO - Actor supervisor_actor initialization_start
2025-05-11 16:44:57,478 - vibe_check_initialization_tracker - INFO - Actor supervisor_actor initialization_complete
2025-05-11 16:44:57,478 - vibe_check_initialization_tracker - INFO - Actor supervisor_actor initialization_complete
2025-05-11 16:44:57,478 - vibe_check_initialization_tracker - ERROR - Actor supervisor_actor failed: Actor supervisor_actor: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds
2025-05-11 16:44:57,478 - vibe_check_initialization_tracker - INFO - Actor report_actor initialization_start
2025-05-11 16:44:57,478 - vibe_check_initialization_tracker - INFO - Actor report_actor initialization_start
2025-05-11 16:44:57,478 - vibe_check_initialization_tracker - INFO - Actor report_actor initialization_complete
2025-05-11 16:44:57,478 - vibe_check_initialization_tracker - INFO - Actor report_actor initialization_complete
2025-05-11 16:44:57,478 - vibe_check_initialization_tracker - ERROR - Actor report_actor failed: Actor report_actor: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds
2025-05-11 16:44:57,579 - vibe_check_actor_lifecycle_manager - INFO - Initializing actor report_actor (type: report)
2025-05-11 16:44:57,579 - vibe_check_actor_initializer - INFO - [ACTOR INIT] Starting initialization of actor report_actor
2025-05-11 16:44:57,579 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting registry for actor report_actor
2025-05-11 16:44:57,579 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting actor report_actor from registry
2025-05-11 16:44:57,579 - vibe_check_actor_initializer - WARNING - [ACTOR REGISTRY] Actor report_actor not found in registry
2025-05-11 16:44:57,579 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Available actors in registry: ['visualization_actor', 'tool_actor', 'file_actor_0', 'file_actor_1', 'file_actor_2', 'file_actor_3', 'file_actor_4', 'file_actor_5', 'file_actor_6', 'file_actor_7', 'file_actor_8', 'file_actor_9', 'file_actor_10', 'file_actor_11', 'file_actor_12', 'file_actor_13', 'project_actor']
2025-05-11 16:44:57,579 - vibe_check_actor_initializer - WARNING - [ACTOR INIT] Actor report_actor not found in registry, retrying (1/3)
2025-05-11 16:44:57,680 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting registry for actor report_actor
2025-05-11 16:44:57,680 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting actor report_actor from registry
2025-05-11 16:44:57,680 - vibe_check_actor_initializer - WARNING - [ACTOR REGISTRY] Actor report_actor not found in registry
2025-05-11 16:44:57,680 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Available actors in registry: ['visualization_actor', 'tool_actor', 'file_actor_0', 'file_actor_1', 'file_actor_2', 'file_actor_3', 'file_actor_4', 'file_actor_5', 'file_actor_6', 'file_actor_7', 'file_actor_8', 'file_actor_9', 'file_actor_10', 'file_actor_11', 'file_actor_12', 'file_actor_13', 'project_actor']
2025-05-11 16:44:57,680 - vibe_check_actor_initializer - WARNING - [ACTOR INIT] Actor report_actor not found in registry, retrying (2/3)
2025-05-11 16:44:57,881 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting registry for actor report_actor
2025-05-11 16:44:57,881 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting actor report_actor from registry
2025-05-11 16:44:57,881 - vibe_check_actor_initializer - WARNING - [ACTOR REGISTRY] Actor report_actor not found in registry
2025-05-11 16:44:57,881 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Available actors in registry: ['visualization_actor', 'tool_actor', 'file_actor_0', 'file_actor_1', 'file_actor_2', 'file_actor_3', 'file_actor_4', 'file_actor_5', 'file_actor_6', 'file_actor_7', 'file_actor_8', 'file_actor_9', 'file_actor_10', 'file_actor_11', 'file_actor_12', 'file_actor_13', 'project_actor']
2025-05-11 16:44:57,881 - vibe_check_actor_initializer - ERROR - [ACTOR INIT] Actor report_actor not found in registry after 3 attempts
2025-05-11 16:44:57,881 - vibe_check_actor_lifecycle_manager - WARNING - Failed to initialize actor report_actor (attempt 2/3): Actor report_actor not found in registry after 3 attempts
2025-05-11 16:44:58,083 - vibe_check_actor_lifecycle_manager - INFO - Initializing actor report_actor (type: report)
2025-05-11 16:44:58,083 - vibe_check_actor_initializer - INFO - [ACTOR INIT] Starting initialization of actor report_actor
2025-05-11 16:44:58,083 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting registry for actor report_actor
2025-05-11 16:44:58,083 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting actor report_actor from registry
2025-05-11 16:44:58,083 - vibe_check_actor_initializer - WARNING - [ACTOR REGISTRY] Actor report_actor not found in registry
2025-05-11 16:44:58,083 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Available actors in registry: ['visualization_actor', 'tool_actor', 'file_actor_0', 'file_actor_1', 'file_actor_2', 'file_actor_3', 'file_actor_4', 'file_actor_5', 'file_actor_6', 'file_actor_7', 'file_actor_8', 'file_actor_9', 'file_actor_10', 'file_actor_11', 'file_actor_12', 'file_actor_13', 'project_actor']
2025-05-11 16:44:58,083 - vibe_check_actor_initializer - WARNING - [ACTOR INIT] Actor report_actor not found in registry, retrying (1/3)
2025-05-11 16:44:58,184 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting registry for actor report_actor
2025-05-11 16:44:58,184 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting actor report_actor from registry
2025-05-11 16:44:58,184 - vibe_check_actor_initializer - WARNING - [ACTOR REGISTRY] Actor report_actor not found in registry
2025-05-11 16:44:58,184 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Available actors in registry: ['visualization_actor', 'tool_actor', 'file_actor_0', 'file_actor_1', 'file_actor_2', 'file_actor_3', 'file_actor_4', 'file_actor_5', 'file_actor_6', 'file_actor_7', 'file_actor_8', 'file_actor_9', 'file_actor_10', 'file_actor_11', 'file_actor_12', 'file_actor_13', 'project_actor']
2025-05-11 16:44:58,184 - vibe_check_actor_initializer - WARNING - [ACTOR INIT] Actor report_actor not found in registry, retrying (2/3)
2025-05-11 16:44:58,385 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting registry for actor report_actor
2025-05-11 16:44:58,386 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting actor report_actor from registry
2025-05-11 16:44:58,386 - vibe_check_actor_initializer - WARNING - [ACTOR REGISTRY] Actor report_actor not found in registry
2025-05-11 16:44:58,386 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Available actors in registry: ['visualization_actor', 'tool_actor', 'file_actor_0', 'file_actor_1', 'file_actor_2', 'file_actor_3', 'file_actor_4', 'file_actor_5', 'file_actor_6', 'file_actor_7', 'file_actor_8', 'file_actor_9', 'file_actor_10', 'file_actor_11', 'file_actor_12', 'file_actor_13', 'project_actor']
2025-05-11 16:44:58,386 - vibe_check_actor_initializer - ERROR - [ACTOR INIT] Actor report_actor not found in registry after 3 attempts
2025-05-11 16:44:58,386 - vibe_check_actor_lifecycle_manager - ERROR - Failed to initialize actor report_actor after 3 attempts: Actor report_actor not found in registry after 3 attempts
2025-05-11 16:44:58,386 - vibe_check_actor_lifecycle_manager - ERROR - Traceback (most recent call last):
  File "/Users/<USER>/Lokalne_Kody/PAT_project_analysis/vibe_check/core/orchestration/actor_lifecycle_manager.py", line 174, in _initialize_all_actors
    await initializer.initialize_actor(actor_id)
  File "/Users/<USER>/Lokalne_Kody/PAT_project_analysis/vibe_check/core/actor_system/actor_initializer.py", line 883, in initialize_actor
    raise ValueError(error_msg)
ValueError: Actor report_actor not found in registry after 3 attempts

2025-05-11 16:44:58,386 - vibe_check_actor_lifecycle_manager - INFO - Completed initialization of 1 actors of type report
2025-05-11 16:44:58,386 - vibe_check_actor_lifecycle_manager - INFO - Initializing 1 actors of type visualization
2025-05-11 16:44:58,386 - vibe_check_actor_lifecycle_manager - INFO - Initializing actor visualization_actor (type: visualization)
2025-05-11 16:44:58,386 - vibe_check_actor_initializer - INFO - [ACTOR INIT] Starting initialization of actor visualization_actor
2025-05-11 16:44:58,386 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting registry for actor visualization_actor
2025-05-11 16:44:58,386 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting actor visualization_actor from registry
2025-05-11 16:44:58,386 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Successfully retrieved actor visualization_actor from registry
2025-05-11 16:44:58,386 - vibe_check_actor_initializer - INFO - [ACTOR INIT] Actor visualization_actor is of type visualization
2025-05-11 16:44:58,386 - vibe_check_actor_initializer - DEBUG - [ACTOR INIT] Setting actor visualization_actor state to INITIALIZING
2025-05-11 16:44:58,386 - vibe_check_actor_initializer - INFO - Actor visualization_actor state changed: created -> initializing
2025-05-11 16:44:58,386 - vibe_check_actor_initializer - DEBUG - [ACTOR INIT] Calling initialize() method on actor visualization_actor
2025-05-11 16:44:58,386 - vibe_check_actor_system - INFO - Actor visualization_actor is already registered with initialization manager
2025-05-11 16:44:58,386 - vibe_check_actor_system - INFO - Actor visualization_actor is already registered with legacy initializer
2025-05-11 16:44:58,387 - vibe_check_actor_initialization - INFO - Actor visualization_actor state changed: created -> initializing
2025-05-11 16:44:58,387 - vibe_check_actor_initializer - DEBUG - Actor visualization_actor is already in state initializing, no change needed
2025-05-11 16:44:58,387 - vibe_check_initialization_tracker - INFO - Actor visualization_actor initialization_start
2025-05-11 16:44:58,387 - vibe_check_actor_system - INFO - No state file found for actor visualization_actor
2025-05-11 16:44:58,387 - vibe_check_actor_system - DEBUG - Actor visualization_actor has no _initialize method
2025-05-11 16:44:58,387 - vibe_check_actor_initialization - INFO - Actor visualization_actor state changed: initializing -> initialized
2025-05-11 16:44:58,387 - vibe_check_actor_initializer - INFO - Actor visualization_actor state changed: initializing -> initialized
2025-05-11 16:44:58,387 - vibe_check_initialization_tracker - INFO - Actor visualization_actor initialization_complete
2025-05-11 16:44:58,387 - vibe_check_actor_system - INFO - Actor visualization_actor initialized successfully
2025-05-11 16:44:58,387 - vibe_check_actor_system - INFO - Actor visualization_actor is already registered with initialization manager
2025-05-11 16:44:58,387 - vibe_check_actor_system - INFO - Actor visualization_actor is already registered with legacy initializer
2025-05-11 16:44:58,387 - vibe_check_actor_initializer - INFO - Actor visualization_actor state changed: REGISTERED -> INITIALIZING
2025-05-11 16:44:58,387 - vibe_check_actor_system.visualization_actor - INFO - State transition: REGISTERED -> INITIALIZING
2025-05-11 16:44:58,387 - vibe_check_actor_system.visualization_actor - DEBUG - State transition details: {'phase': 'initialize'}
2025-05-11 16:44:58,387 - vibe_check_actor_system.visualization_actor - DEBUG - Operation 'state_transition_REGISTERED_to_INITIALIZING' took 0.000037 seconds
2025-05-11 16:44:58,387 - vibe_check_actor_system.initializer - DEBUG - Component state: {'actor_id': 'visualization_actor', 'state': 'INITIALIZING', 'previous_state': 'REGISTERED', 'timestamp': 1746974698.38721, 'details': {'phase': 'initialize'}}
2025-05-11 16:44:58,387 - vibe_check_actor_system - INFO - No state file found for actor visualization_actor
2025-05-11 16:44:58,387 - vibe_check_actor_registry - WARNING - Actor visualization_actor already registered, updating
2025-05-11 16:44:58,387 - vibe_check_actor_registry - INFO - Registered actor visualization_actor of type visualization
2025-05-11 16:44:58,387 - vibe_check_actor_system - DEBUG - Actor visualization_actor has no _initialize method
2025-05-11 16:44:58,387 - vibe_check_actor_initializer - INFO - Actor visualization_actor state changed: INITIALIZING -> INITIALIZED
2025-05-11 16:44:58,387 - vibe_check_actor_system.visualization_actor - INFO - State transition: INITIALIZING -> INITIALIZED
2025-05-11 16:44:58,387 - vibe_check_actor_system.visualization_actor - DEBUG - State transition details: {'phase': 'initialize'}
2025-05-11 16:44:58,387 - vibe_check_actor_system.visualization_actor - DEBUG - Operation 'state_transition_INITIALIZING_to_INITIALIZED' took 0.000028 seconds
2025-05-11 16:44:58,387 - vibe_check_actor_system.initializer - DEBUG - Component state: {'actor_id': 'visualization_actor', 'state': 'INITIALIZED', 'previous_state': 'INITIALIZING', 'timestamp': 1746974698.387331, 'details': {'phase': 'initialize'}}
2025-05-11 16:44:58,387 - vibe_check_synchronization - DEBUG - Actor visualization_actor reached synchronization point initialization_complete
2025-05-11 16:44:58,387 - vibe_check_actor_system - INFO - Actor visualization_actor initialized successfully
2025-05-11 16:44:58,387 - vibe_check_actor_system.actor.visualization_actor - ERROR - Error during state transition to FAILED: Actor visualization_actor: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds
2025-05-11 16:44:58,387 - vibe_check_actor_system.actor.visualization_actor - ERROR - Traceback:
NoneType: None

2025-05-11 16:44:58,387 - vibe_check_actor_initializer - INFO - Actor visualization_actor state changed: INITIALIZED -> FAILED
2025-05-11 16:44:58,387 - vibe_check_actor_system.visualization_actor - INFO - State transition: INITIALIZED -> FAILED
2025-05-11 16:44:58,387 - vibe_check_actor_system.visualization_actor - DEBUG - State transition details: {'phase': 'initialize'}
2025-05-11 16:44:58,387 - vibe_check_actor_system.visualization_actor - DEBUG - Operation 'state_transition_INITIALIZED_to_FAILED' took 0.000084 seconds
2025-05-11 16:44:58,387 - vibe_check_actor_system.initializer - DEBUG - Component state: {'actor_id': 'visualization_actor', 'state': 'FAILED', 'previous_state': 'INITIALIZED', 'timestamp': 1746974698.387415, 'details': {'phase': 'initialize'}}
2025-05-11 16:44:58,387 - vibe_check_actor_initializer - DEBUG - Cleaned up resources for actor visualization_actor
2025-05-11 16:44:58,387 - vibe_check_actor_system - INFO - Cleaning up resources for actor visualization_actor
2025-05-11 16:44:58,387 - vibe_check_actor_system - INFO - Cleared mailbox for actor visualization_actor
2025-05-11 16:44:58,387 - vibe_check_actor_registry - INFO - Unregistered actor visualization_actor
2025-05-11 16:44:58,387 - vibe_check_actor_system - INFO - Unregistered actor visualization_actor from registry during cleanup
2025-05-11 16:44:58,387 - vibe_check_actor_initialization - INFO - Actor visualization_actor state changed: initialized -> stopped
2025-05-11 16:44:58,387 - vibe_check_actor_system - INFO - Set actor visualization_actor state to STOPPED in initialization manager
2025-05-11 16:44:58,387 - vibe_check_actor_initializer - ERROR - Invalid state transition for actor visualization_actor: initialized -> stopped
2025-05-11 16:44:58,387 - vibe_check_actor_initializer - DEBUG - Valid transitions from initialized are: ['stopping', 'starting', 'rollback', 'failed']
2025-05-11 16:44:58,387 - vibe_check_actor_initializer - WARNING - Forcing transition to stopped state for actor visualization_actor
2025-05-11 16:44:58,387 - vibe_check_actor_initializer - INFO - Actor visualization_actor state changed: initialized -> stopped
2025-05-11 16:44:58,387 - vibe_check_actor_system - INFO - Set actor visualization_actor state to STOPPED in legacy initializer
2025-05-11 16:44:58,387 - vibe_check_actor_system - INFO - Cleaned up resources for actor visualization_actor
2025-05-11 16:44:58,387 - vibe_check_actor_system - ERROR - Error in initialization process for actor visualization_actor: Actor visualization_actor: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds
2025-05-11 16:44:58,387 - vibe_check_actor_initializer - ERROR - Actor visualization_actor: Invalid state transition: FAILED -> FAILED
2025-05-11 16:44:58,387 - vibe_check_actor_system - ERROR - Error rolling back actor visualization_actor: Actor visualization_actor: Invalid state transition: FAILED -> FAILED (state: FAILED)
2025-05-11 16:44:58,387 - vibe_check_actor_system - INFO - Cleaning up resources for actor visualization_actor
2025-05-11 16:44:58,387 - vibe_check_actor_system - INFO - Cleared mailbox for actor visualization_actor
2025-05-11 16:44:58,387 - vibe_check_actor_registry - WARNING - Actor visualization_actor not registered, cannot unregister
2025-05-11 16:44:58,387 - vibe_check_actor_system - INFO - Unregistered actor visualization_actor from registry during cleanup
2025-05-11 16:44:58,387 - vibe_check_actor_initialization - DEBUG - Actor visualization_actor is already in state stopped, no change needed
2025-05-11 16:44:58,387 - vibe_check_actor_system - INFO - Set actor visualization_actor state to STOPPED in initialization manager
2025-05-11 16:44:58,387 - vibe_check_actor_initializer - DEBUG - Actor visualization_actor is already in state stopped, no change needed
2025-05-11 16:44:58,387 - vibe_check_actor_initializer - INFO - Actor visualization_actor state changed: stopped -> stopped
2025-05-11 16:44:58,387 - vibe_check_actor_system - INFO - Set actor visualization_actor state to STOPPED in legacy initializer
2025-05-11 16:44:58,387 - vibe_check_actor_system - INFO - Cleaned up resources for actor visualization_actor
2025-05-11 16:44:58,387 - vibe_check_actor_initializer - ERROR - [ACTOR INIT] Failed to initialize actor visualization_actor: Actor visualization_actor: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds
2025-05-11 16:44:58,388 - vibe_check_actor_initializer - ERROR - [ACTOR INIT] Traceback for actor visualization_actor:
Traceback (most recent call last):
  File "/Users/<USER>/Lokalne_Kody/PAT_project_analysis/vibe_check/core/actor_system/actor_initializer.py", line 936, in initialize_actor
    await asyncio.wait_for(actor.initialize(), timeout)
  File "/Users/<USER>/.pyenv/versions/3.13.3/lib/python3.13/asyncio/tasks.py", line 507, in wait_for
    return await fut
           ^^^^^^^^^
  File "/Users/<USER>/Lokalne_Kody/PAT_project_analysis/vibe_check/core/actor_system/actor.py", line 1109, in initialize
    raise timeout_error
vibe_check.core.actor_system.exceptions.ActorTimeoutError: Actor visualization_actor: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds

2025-05-11 16:44:58,388 - vibe_check_actor_initializer - DEBUG - [ACTOR INIT] Setting actor visualization_actor state to FAILED
2025-05-11 16:44:58,388 - vibe_check_actor_initializer - ERROR - Invalid state transition for actor visualization_actor: stopped -> failed
2025-05-11 16:44:58,388 - vibe_check_actor_initializer - DEBUG - Valid transitions from stopped are: ['initializing', 'created']
2025-05-11 16:44:58,388 - vibe_check_actor_initializer - WARNING - Forcing transition to failed state for actor visualization_actor
2025-05-11 16:44:58,388 - vibe_check_actor_initializer - INFO - Actor visualization_actor state changed: stopped -> failed
2025-05-11 16:44:58,388 - vibe_check_actor_initializer - ERROR - Actor visualization_actor failed: Actor visualization_actor failed during initialize phase (state: stopped): Actor visualization_actor: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds
Original error: Actor visualization_actor: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds
2025-05-11 16:44:58,388 - vibe_check_actor_lifecycle_manager - WARNING - Failed to initialize actor visualization_actor (attempt 1/3): Actor visualization_actor: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds
2025-05-11 16:44:58,388 - vibe_check_initialization_tracker - INFO - Actor visualization_actor initialization_start
2025-05-11 16:44:58,388 - vibe_check_initialization_tracker - INFO - Actor visualization_actor initialization_start
2025-05-11 16:44:58,388 - vibe_check_initialization_tracker - INFO - Actor visualization_actor initialization_complete
2025-05-11 16:44:58,388 - vibe_check_initialization_tracker - INFO - Actor visualization_actor initialization_complete
2025-05-11 16:44:58,388 - vibe_check_initialization_tracker - ERROR - Actor visualization_actor failed: Actor visualization_actor: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds
2025-05-11 16:44:58,489 - vibe_check_actor_lifecycle_manager - INFO - Initializing actor visualization_actor (type: visualization)
2025-05-11 16:44:58,489 - vibe_check_actor_initializer - INFO - [ACTOR INIT] Starting initialization of actor visualization_actor
2025-05-11 16:44:58,489 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting registry for actor visualization_actor
2025-05-11 16:44:58,489 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting actor visualization_actor from registry
2025-05-11 16:44:58,489 - vibe_check_actor_initializer - WARNING - [ACTOR REGISTRY] Actor visualization_actor not found in registry
2025-05-11 16:44:58,489 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Available actors in registry: ['tool_actor', 'file_actor_0', 'file_actor_1', 'file_actor_2', 'file_actor_3', 'file_actor_4', 'file_actor_5', 'file_actor_6', 'file_actor_7', 'file_actor_8', 'file_actor_9', 'file_actor_10', 'file_actor_11', 'file_actor_12', 'file_actor_13', 'project_actor']
2025-05-11 16:44:58,489 - vibe_check_actor_initializer - WARNING - [ACTOR INIT] Actor visualization_actor not found in registry, retrying (1/3)
2025-05-11 16:44:58,590 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting registry for actor visualization_actor
2025-05-11 16:44:58,590 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting actor visualization_actor from registry
2025-05-11 16:44:58,591 - vibe_check_actor_initializer - WARNING - [ACTOR REGISTRY] Actor visualization_actor not found in registry
2025-05-11 16:44:58,591 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Available actors in registry: ['tool_actor', 'file_actor_0', 'file_actor_1', 'file_actor_2', 'file_actor_3', 'file_actor_4', 'file_actor_5', 'file_actor_6', 'file_actor_7', 'file_actor_8', 'file_actor_9', 'file_actor_10', 'file_actor_11', 'file_actor_12', 'file_actor_13', 'project_actor']
2025-05-11 16:44:58,591 - vibe_check_actor_initializer - WARNING - [ACTOR INIT] Actor visualization_actor not found in registry, retrying (2/3)
2025-05-11 16:44:58,791 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting registry for actor visualization_actor
2025-05-11 16:44:58,791 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting actor visualization_actor from registry
2025-05-11 16:44:58,792 - vibe_check_actor_initializer - WARNING - [ACTOR REGISTRY] Actor visualization_actor not found in registry
2025-05-11 16:44:58,792 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Available actors in registry: ['tool_actor', 'file_actor_0', 'file_actor_1', 'file_actor_2', 'file_actor_3', 'file_actor_4', 'file_actor_5', 'file_actor_6', 'file_actor_7', 'file_actor_8', 'file_actor_9', 'file_actor_10', 'file_actor_11', 'file_actor_12', 'file_actor_13', 'project_actor']
2025-05-11 16:44:58,792 - vibe_check_actor_initializer - ERROR - [ACTOR INIT] Actor visualization_actor not found in registry after 3 attempts
2025-05-11 16:44:58,792 - vibe_check_actor_lifecycle_manager - WARNING - Failed to initialize actor visualization_actor (attempt 2/3): Actor visualization_actor not found in registry after 3 attempts
2025-05-11 16:44:58,993 - vibe_check_actor_lifecycle_manager - INFO - Initializing actor visualization_actor (type: visualization)
2025-05-11 16:44:58,993 - vibe_check_actor_initializer - INFO - [ACTOR INIT] Starting initialization of actor visualization_actor
2025-05-11 16:44:58,993 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting registry for actor visualization_actor
2025-05-11 16:44:58,993 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting actor visualization_actor from registry
2025-05-11 16:44:58,993 - vibe_check_actor_initializer - WARNING - [ACTOR REGISTRY] Actor visualization_actor not found in registry
2025-05-11 16:44:58,993 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Available actors in registry: ['tool_actor', 'file_actor_0', 'file_actor_1', 'file_actor_2', 'file_actor_3', 'file_actor_4', 'file_actor_5', 'file_actor_6', 'file_actor_7', 'file_actor_8', 'file_actor_9', 'file_actor_10', 'file_actor_11', 'file_actor_12', 'file_actor_13', 'project_actor']
2025-05-11 16:44:58,993 - vibe_check_actor_initializer - WARNING - [ACTOR INIT] Actor visualization_actor not found in registry, retrying (1/3)
2025-05-11 16:44:59,094 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting registry for actor visualization_actor
2025-05-11 16:44:59,094 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting actor visualization_actor from registry
2025-05-11 16:44:59,094 - vibe_check_actor_initializer - WARNING - [ACTOR REGISTRY] Actor visualization_actor not found in registry
2025-05-11 16:44:59,094 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Available actors in registry: ['tool_actor', 'file_actor_0', 'file_actor_1', 'file_actor_2', 'file_actor_3', 'file_actor_4', 'file_actor_5', 'file_actor_6', 'file_actor_7', 'file_actor_8', 'file_actor_9', 'file_actor_10', 'file_actor_11', 'file_actor_12', 'file_actor_13', 'project_actor']
2025-05-11 16:44:59,094 - vibe_check_actor_initializer - WARNING - [ACTOR INIT] Actor visualization_actor not found in registry, retrying (2/3)
2025-05-11 16:44:59,294 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting registry for actor visualization_actor
2025-05-11 16:44:59,295 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting actor visualization_actor from registry
2025-05-11 16:44:59,295 - vibe_check_actor_initializer - WARNING - [ACTOR REGISTRY] Actor visualization_actor not found in registry
2025-05-11 16:44:59,295 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Available actors in registry: ['tool_actor', 'file_actor_0', 'file_actor_1', 'file_actor_2', 'file_actor_3', 'file_actor_4', 'file_actor_5', 'file_actor_6', 'file_actor_7', 'file_actor_8', 'file_actor_9', 'file_actor_10', 'file_actor_11', 'file_actor_12', 'file_actor_13', 'project_actor']
2025-05-11 16:44:59,295 - vibe_check_actor_initializer - ERROR - [ACTOR INIT] Actor visualization_actor not found in registry after 3 attempts
2025-05-11 16:44:59,295 - vibe_check_actor_lifecycle_manager - ERROR - Failed to initialize actor visualization_actor after 3 attempts: Actor visualization_actor not found in registry after 3 attempts
2025-05-11 16:44:59,295 - vibe_check_actor_lifecycle_manager - ERROR - Traceback (most recent call last):
  File "/Users/<USER>/Lokalne_Kody/PAT_project_analysis/vibe_check/core/orchestration/actor_lifecycle_manager.py", line 174, in _initialize_all_actors
    await initializer.initialize_actor(actor_id)
  File "/Users/<USER>/Lokalne_Kody/PAT_project_analysis/vibe_check/core/actor_system/actor_initializer.py", line 883, in initialize_actor
    raise ValueError(error_msg)
ValueError: Actor visualization_actor not found in registry after 3 attempts

2025-05-11 16:44:59,295 - vibe_check_actor_lifecycle_manager - INFO - Completed initialization of 1 actors of type visualization
2025-05-11 16:44:59,295 - vibe_check_actor_lifecycle_manager - INFO - Initializing 1 actors of type tool
2025-05-11 16:44:59,296 - vibe_check_actor_lifecycle_manager - INFO - Initializing actor tool_actor (type: tool)
2025-05-11 16:44:59,296 - vibe_check_actor_initializer - INFO - [ACTOR INIT] Starting initialization of actor tool_actor
2025-05-11 16:44:59,296 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting registry for actor tool_actor
2025-05-11 16:44:59,296 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting actor tool_actor from registry
2025-05-11 16:44:59,296 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Successfully retrieved actor tool_actor from registry
2025-05-11 16:44:59,296 - vibe_check_actor_initializer - INFO - [ACTOR INIT] Actor tool_actor is of type tool
2025-05-11 16:44:59,296 - vibe_check_actor_initializer - DEBUG - [ACTOR INIT] Setting actor tool_actor state to INITIALIZING
2025-05-11 16:44:59,296 - vibe_check_actor_initializer - INFO - Actor tool_actor state changed: created -> initializing
2025-05-11 16:44:59,296 - vibe_check_actor_initializer - DEBUG - [ACTOR INIT] Calling initialize() method on actor tool_actor
2025-05-11 16:44:59,296 - vibe_check_actor_system - INFO - Actor tool_actor is already registered with initialization manager
2025-05-11 16:44:59,296 - vibe_check_actor_system - INFO - Actor tool_actor is already registered with legacy initializer
2025-05-11 16:44:59,296 - vibe_check_actor_initialization - INFO - Actor tool_actor state changed: created -> initializing
2025-05-11 16:44:59,296 - vibe_check_actor_initializer - DEBUG - Actor tool_actor is already in state initializing, no change needed
2025-05-11 16:44:59,296 - vibe_check_initialization_tracker - INFO - Actor tool_actor initialization_start
2025-05-11 16:44:59,296 - vibe_check_actor_system - INFO - No state file found for actor tool_actor
2025-05-11 16:44:59,296 - vibe_check_actor_system - DEBUG - Actor tool_actor has no _initialize method
2025-05-11 16:44:59,296 - vibe_check_actor_initialization - INFO - Actor tool_actor state changed: initializing -> initialized
2025-05-11 16:44:59,296 - vibe_check_actor_initializer - INFO - Actor tool_actor state changed: initializing -> initialized
2025-05-11 16:44:59,296 - vibe_check_initialization_tracker - INFO - Actor tool_actor initialization_complete
2025-05-11 16:44:59,296 - vibe_check_actor_system - INFO - Actor tool_actor initialized successfully
2025-05-11 16:44:59,296 - vibe_check_actor_system - INFO - Actor tool_actor is already registered with initialization manager
2025-05-11 16:44:59,296 - vibe_check_actor_system - INFO - Actor tool_actor is already registered with legacy initializer
2025-05-11 16:44:59,296 - vibe_check_actor_initializer - INFO - Actor tool_actor state changed: REGISTERED -> INITIALIZING
2025-05-11 16:44:59,296 - vibe_check_actor_system.tool_actor - INFO - State transition: REGISTERED -> INITIALIZING
2025-05-11 16:44:59,296 - vibe_check_actor_system.tool_actor - DEBUG - State transition details: {'phase': 'initialize'}
2025-05-11 16:44:59,296 - vibe_check_actor_system.tool_actor - DEBUG - Operation 'state_transition_REGISTERED_to_INITIALIZING' took 0.000061 seconds
2025-05-11 16:44:59,296 - vibe_check_actor_system.initializer - DEBUG - Component state: {'actor_id': 'tool_actor', 'state': 'INITIALIZING', 'previous_state': 'REGISTERED', 'timestamp': 1746974699.296567, 'details': {'phase': 'initialize'}}
2025-05-11 16:44:59,296 - vibe_check_actor_system - INFO - No state file found for actor tool_actor
2025-05-11 16:44:59,296 - vibe_check_actor_registry - WARNING - Actor tool_actor already registered, updating
2025-05-11 16:44:59,296 - vibe_check_actor_registry - INFO - Registered actor tool_actor of type tool
2025-05-11 16:44:59,296 - vibe_check_actor_system - DEBUG - Actor tool_actor has no _initialize method
2025-05-11 16:44:59,296 - vibe_check_actor_initializer - INFO - Actor tool_actor state changed: INITIALIZING -> INITIALIZED
2025-05-11 16:44:59,296 - vibe_check_actor_system.tool_actor - INFO - State transition: INITIALIZING -> INITIALIZED
2025-05-11 16:44:59,296 - vibe_check_actor_system.tool_actor - DEBUG - State transition details: {'phase': 'initialize'}
2025-05-11 16:44:59,296 - vibe_check_actor_system.tool_actor - DEBUG - Operation 'state_transition_INITIALIZING_to_INITIALIZED' took 0.000042 seconds
2025-05-11 16:44:59,296 - vibe_check_actor_system.initializer - DEBUG - Component state: {'actor_id': 'tool_actor', 'state': 'INITIALIZED', 'previous_state': 'INITIALIZING', 'timestamp': 1746974699.296758, 'details': {'phase': 'initialize'}}
2025-05-11 16:44:59,296 - vibe_check_synchronization - DEBUG - Actor tool_actor reached synchronization point initialization_complete
2025-05-11 16:44:59,296 - vibe_check_actor_system - INFO - Actor tool_actor initialized successfully
2025-05-11 16:44:59,296 - vibe_check_actor_system.actor.tool_actor - ERROR - Error during state transition to FAILED: Actor tool_actor: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds
2025-05-11 16:44:59,296 - vibe_check_actor_system.actor.tool_actor - ERROR - Traceback:
NoneType: None

2025-05-11 16:44:59,296 - vibe_check_actor_initializer - INFO - Actor tool_actor state changed: INITIALIZED -> FAILED
2025-05-11 16:44:59,296 - vibe_check_actor_system.tool_actor - INFO - State transition: INITIALIZED -> FAILED
2025-05-11 16:44:59,297 - vibe_check_actor_system.tool_actor - DEBUG - State transition details: {'phase': 'initialize'}
2025-05-11 16:44:59,297 - vibe_check_actor_system.tool_actor - DEBUG - Operation 'state_transition_INITIALIZED_to_FAILED' took 0.000134 seconds
2025-05-11 16:44:59,297 - vibe_check_actor_system.initializer - DEBUG - Component state: {'actor_id': 'tool_actor', 'state': 'FAILED', 'previous_state': 'INITIALIZED', 'timestamp': 1746974699.296888, 'details': {'phase': 'initialize'}}
2025-05-11 16:44:59,297 - vibe_check_actor_initializer - DEBUG - Cleaned up resources for actor tool_actor
2025-05-11 16:44:59,297 - vibe_check_actor_system - INFO - Cleaning up resources for actor tool_actor
2025-05-11 16:44:59,297 - vibe_check_actor_system - INFO - Cleared mailbox for actor tool_actor
2025-05-11 16:44:59,297 - vibe_check_actor_registry - INFO - Unregistered actor tool_actor
2025-05-11 16:44:59,297 - vibe_check_actor_system - INFO - Unregistered actor tool_actor from registry during cleanup
2025-05-11 16:44:59,297 - vibe_check_actor_initialization - INFO - Actor tool_actor state changed: initialized -> stopped
2025-05-11 16:44:59,297 - vibe_check_actor_system - INFO - Set actor tool_actor state to STOPPED in initialization manager
2025-05-11 16:44:59,297 - vibe_check_actor_initializer - ERROR - Invalid state transition for actor tool_actor: initialized -> stopped
2025-05-11 16:44:59,297 - vibe_check_actor_initializer - DEBUG - Valid transitions from initialized are: ['stopping', 'starting', 'rollback', 'failed']
2025-05-11 16:44:59,297 - vibe_check_actor_initializer - WARNING - Forcing transition to stopped state for actor tool_actor
2025-05-11 16:44:59,297 - vibe_check_actor_initializer - INFO - Actor tool_actor state changed: initialized -> stopped
2025-05-11 16:44:59,297 - vibe_check_actor_system - INFO - Set actor tool_actor state to STOPPED in legacy initializer
2025-05-11 16:44:59,297 - vibe_check_actor_system - INFO - Cleaned up resources for actor tool_actor
2025-05-11 16:44:59,297 - vibe_check_actor_system - ERROR - Error in initialization process for actor tool_actor: Actor tool_actor: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds
2025-05-11 16:44:59,297 - vibe_check_actor_initializer - ERROR - Actor tool_actor: Invalid state transition: FAILED -> FAILED
2025-05-11 16:44:59,297 - vibe_check_actor_system - ERROR - Error rolling back actor tool_actor: Actor tool_actor: Invalid state transition: FAILED -> FAILED (state: FAILED)
2025-05-11 16:44:59,297 - vibe_check_actor_system - INFO - Cleaning up resources for actor tool_actor
2025-05-11 16:44:59,297 - vibe_check_actor_system - INFO - Cleared mailbox for actor tool_actor
2025-05-11 16:44:59,297 - vibe_check_actor_registry - WARNING - Actor tool_actor not registered, cannot unregister
2025-05-11 16:44:59,297 - vibe_check_actor_system - INFO - Unregistered actor tool_actor from registry during cleanup
2025-05-11 16:44:59,297 - vibe_check_actor_initialization - DEBUG - Actor tool_actor is already in state stopped, no change needed
2025-05-11 16:44:59,297 - vibe_check_actor_system - INFO - Set actor tool_actor state to STOPPED in initialization manager
2025-05-11 16:44:59,297 - vibe_check_actor_initializer - DEBUG - Actor tool_actor is already in state stopped, no change needed
2025-05-11 16:44:59,297 - vibe_check_actor_initializer - INFO - Actor tool_actor state changed: stopped -> stopped
2025-05-11 16:44:59,297 - vibe_check_actor_system - INFO - Set actor tool_actor state to STOPPED in legacy initializer
2025-05-11 16:44:59,297 - vibe_check_actor_system - INFO - Cleaned up resources for actor tool_actor
2025-05-11 16:44:59,297 - vibe_check_actor_initializer - ERROR - [ACTOR INIT] Failed to initialize actor tool_actor: Actor tool_actor: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds
2025-05-11 16:44:59,297 - vibe_check_actor_initializer - ERROR - [ACTOR INIT] Traceback for actor tool_actor:
Traceback (most recent call last):
  File "/Users/<USER>/Lokalne_Kody/PAT_project_analysis/vibe_check/core/actor_system/actor_initializer.py", line 936, in initialize_actor
    await asyncio.wait_for(actor.initialize(), timeout)
  File "/Users/<USER>/.pyenv/versions/3.13.3/lib/python3.13/asyncio/tasks.py", line 507, in wait_for
    return await fut
           ^^^^^^^^^
  File "/Users/<USER>/Lokalne_Kody/PAT_project_analysis/vibe_check/core/actor_system/actor.py", line 1109, in initialize
    raise timeout_error
vibe_check.core.actor_system.exceptions.ActorTimeoutError: Actor tool_actor: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds

2025-05-11 16:44:59,298 - vibe_check_actor_initializer - DEBUG - [ACTOR INIT] Setting actor tool_actor state to FAILED
2025-05-11 16:44:59,298 - vibe_check_actor_initializer - ERROR - Invalid state transition for actor tool_actor: stopped -> failed
2025-05-11 16:44:59,298 - vibe_check_actor_initializer - DEBUG - Valid transitions from stopped are: ['initializing', 'created']
2025-05-11 16:44:59,298 - vibe_check_actor_initializer - WARNING - Forcing transition to failed state for actor tool_actor
2025-05-11 16:44:59,298 - vibe_check_actor_initializer - INFO - Actor tool_actor state changed: stopped -> failed
2025-05-11 16:44:59,298 - vibe_check_actor_initializer - ERROR - Actor tool_actor failed: Actor tool_actor failed during initialize phase (state: stopped): Actor tool_actor: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds
Original error: Actor tool_actor: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds
2025-05-11 16:44:59,298 - vibe_check_actor_lifecycle_manager - WARNING - Failed to initialize actor tool_actor (attempt 1/3): Actor tool_actor: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds
2025-05-11 16:44:59,298 - vibe_check_initialization_tracker - INFO - Actor tool_actor initialization_start
2025-05-11 16:44:59,298 - vibe_check_initialization_tracker - INFO - Actor tool_actor initialization_start
2025-05-11 16:44:59,298 - vibe_check_initialization_tracker - INFO - Actor tool_actor initialization_complete
2025-05-11 16:44:59,298 - vibe_check_initialization_tracker - INFO - Actor tool_actor initialization_complete
2025-05-11 16:44:59,298 - vibe_check_initialization_tracker - ERROR - Actor tool_actor failed: Actor tool_actor: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds
2025-05-11 16:44:59,399 - vibe_check_actor_lifecycle_manager - INFO - Initializing actor tool_actor (type: tool)
2025-05-11 16:44:59,399 - vibe_check_actor_initializer - INFO - [ACTOR INIT] Starting initialization of actor tool_actor
2025-05-11 16:44:59,399 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting registry for actor tool_actor
2025-05-11 16:44:59,399 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting actor tool_actor from registry
2025-05-11 16:44:59,399 - vibe_check_actor_initializer - WARNING - [ACTOR REGISTRY] Actor tool_actor not found in registry
2025-05-11 16:44:59,399 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Available actors in registry: ['file_actor_0', 'file_actor_1', 'file_actor_2', 'file_actor_3', 'file_actor_4', 'file_actor_5', 'file_actor_6', 'file_actor_7', 'file_actor_8', 'file_actor_9', 'file_actor_10', 'file_actor_11', 'file_actor_12', 'file_actor_13', 'project_actor']
2025-05-11 16:44:59,399 - vibe_check_actor_initializer - WARNING - [ACTOR INIT] Actor tool_actor not found in registry, retrying (1/3)
2025-05-11 16:44:59,501 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting registry for actor tool_actor
2025-05-11 16:44:59,501 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting actor tool_actor from registry
2025-05-11 16:44:59,501 - vibe_check_actor_initializer - WARNING - [ACTOR REGISTRY] Actor tool_actor not found in registry
2025-05-11 16:44:59,501 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Available actors in registry: ['file_actor_0', 'file_actor_1', 'file_actor_2', 'file_actor_3', 'file_actor_4', 'file_actor_5', 'file_actor_6', 'file_actor_7', 'file_actor_8', 'file_actor_9', 'file_actor_10', 'file_actor_11', 'file_actor_12', 'file_actor_13', 'project_actor']
2025-05-11 16:44:59,501 - vibe_check_actor_initializer - WARNING - [ACTOR INIT] Actor tool_actor not found in registry, retrying (2/3)
2025-05-11 16:44:59,702 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting registry for actor tool_actor
2025-05-11 16:44:59,703 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting actor tool_actor from registry
2025-05-11 16:44:59,703 - vibe_check_actor_initializer - WARNING - [ACTOR REGISTRY] Actor tool_actor not found in registry
2025-05-11 16:44:59,703 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Available actors in registry: ['file_actor_0', 'file_actor_1', 'file_actor_2', 'file_actor_3', 'file_actor_4', 'file_actor_5', 'file_actor_6', 'file_actor_7', 'file_actor_8', 'file_actor_9', 'file_actor_10', 'file_actor_11', 'file_actor_12', 'file_actor_13', 'project_actor']
2025-05-11 16:44:59,703 - vibe_check_actor_initializer - ERROR - [ACTOR INIT] Actor tool_actor not found in registry after 3 attempts
2025-05-11 16:44:59,703 - vibe_check_actor_lifecycle_manager - WARNING - Failed to initialize actor tool_actor (attempt 2/3): Actor tool_actor not found in registry after 3 attempts
2025-05-11 16:44:59,903 - vibe_check_actor_lifecycle_manager - INFO - Initializing actor tool_actor (type: tool)
2025-05-11 16:44:59,904 - vibe_check_actor_initializer - INFO - [ACTOR INIT] Starting initialization of actor tool_actor
2025-05-11 16:44:59,905 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting registry for actor tool_actor
2025-05-11 16:44:59,905 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting actor tool_actor from registry
2025-05-11 16:44:59,905 - vibe_check_actor_initializer - WARNING - [ACTOR REGISTRY] Actor tool_actor not found in registry
2025-05-11 16:44:59,905 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Available actors in registry: ['file_actor_0', 'file_actor_1', 'file_actor_2', 'file_actor_3', 'file_actor_4', 'file_actor_5', 'file_actor_6', 'file_actor_7', 'file_actor_8', 'file_actor_9', 'file_actor_10', 'file_actor_11', 'file_actor_12', 'file_actor_13', 'project_actor']
2025-05-11 16:44:59,905 - vibe_check_actor_initializer - WARNING - [ACTOR INIT] Actor tool_actor not found in registry, retrying (1/3)
2025-05-11 16:45:00,007 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting registry for actor tool_actor
2025-05-11 16:45:00,007 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting actor tool_actor from registry
2025-05-11 16:45:00,007 - vibe_check_actor_initializer - WARNING - [ACTOR REGISTRY] Actor tool_actor not found in registry
2025-05-11 16:45:00,007 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Available actors in registry: ['file_actor_0', 'file_actor_1', 'file_actor_2', 'file_actor_3', 'file_actor_4', 'file_actor_5', 'file_actor_6', 'file_actor_7', 'file_actor_8', 'file_actor_9', 'file_actor_10', 'file_actor_11', 'file_actor_12', 'file_actor_13', 'project_actor']
2025-05-11 16:45:00,008 - vibe_check_actor_initializer - WARNING - [ACTOR INIT] Actor tool_actor not found in registry, retrying (2/3)
2025-05-11 16:45:00,209 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting registry for actor tool_actor
2025-05-11 16:45:00,209 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting actor tool_actor from registry
2025-05-11 16:45:00,209 - vibe_check_actor_initializer - WARNING - [ACTOR REGISTRY] Actor tool_actor not found in registry
2025-05-11 16:45:00,209 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Available actors in registry: ['file_actor_0', 'file_actor_1', 'file_actor_2', 'file_actor_3', 'file_actor_4', 'file_actor_5', 'file_actor_6', 'file_actor_7', 'file_actor_8', 'file_actor_9', 'file_actor_10', 'file_actor_11', 'file_actor_12', 'file_actor_13', 'project_actor']
2025-05-11 16:45:00,209 - vibe_check_actor_initializer - ERROR - [ACTOR INIT] Actor tool_actor not found in registry after 3 attempts
2025-05-11 16:45:00,209 - vibe_check_actor_lifecycle_manager - ERROR - Failed to initialize actor tool_actor after 3 attempts: Actor tool_actor not found in registry after 3 attempts
2025-05-11 16:45:00,210 - vibe_check_actor_lifecycle_manager - ERROR - Traceback (most recent call last):
  File "/Users/<USER>/Lokalne_Kody/PAT_project_analysis/vibe_check/core/orchestration/actor_lifecycle_manager.py", line 174, in _initialize_all_actors
    await initializer.initialize_actor(actor_id)
  File "/Users/<USER>/Lokalne_Kody/PAT_project_analysis/vibe_check/core/actor_system/actor_initializer.py", line 883, in initialize_actor
    raise ValueError(error_msg)
ValueError: Actor tool_actor not found in registry after 3 attempts

2025-05-11 16:45:00,210 - vibe_check_actor_lifecycle_manager - INFO - Completed initialization of 1 actors of type tool
2025-05-11 16:45:00,210 - vibe_check_actor_lifecycle_manager - INFO - Initializing 14 actors of type file
2025-05-11 16:45:00,210 - vibe_check_actor_lifecycle_manager - INFO - Initializing actor file_actor_0 (type: file)
2025-05-11 16:45:00,210 - vibe_check_actor_initializer - INFO - [ACTOR INIT] Starting initialization of actor file_actor_0
2025-05-11 16:45:00,210 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting registry for actor file_actor_0
2025-05-11 16:45:00,210 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting actor file_actor_0 from registry
2025-05-11 16:45:00,210 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Successfully retrieved actor file_actor_0 from registry
2025-05-11 16:45:00,210 - vibe_check_actor_initializer - INFO - [ACTOR INIT] Actor file_actor_0 is of type file
2025-05-11 16:45:00,210 - vibe_check_actor_initializer - DEBUG - [ACTOR INIT] Setting actor file_actor_0 state to INITIALIZING
2025-05-11 16:45:00,210 - vibe_check_actor_initializer - INFO - Actor file_actor_0 state changed: created -> initializing
2025-05-11 16:45:00,210 - vibe_check_actor_initializer - DEBUG - [ACTOR INIT] Calling initialize() method on actor file_actor_0
2025-05-11 16:45:00,210 - vibe_check_actor_system - INFO - Actor file_actor_0 is already registered with initialization manager
2025-05-11 16:45:00,210 - vibe_check_actor_system - INFO - Actor file_actor_0 is already registered with legacy initializer
2025-05-11 16:45:00,210 - vibe_check_actor_initialization - INFO - Actor file_actor_0 state changed: created -> initializing
2025-05-11 16:45:00,210 - vibe_check_actor_initializer - DEBUG - Actor file_actor_0 is already in state initializing, no change needed
2025-05-11 16:45:00,210 - vibe_check_initialization_tracker - INFO - Actor file_actor_0 initialization_start
2025-05-11 16:45:00,210 - vibe_check_actor_system - DEBUG - Actor file_actor_0 has no _initialize method
2025-05-11 16:45:00,210 - vibe_check_actor_initialization - INFO - Actor file_actor_0 state changed: initializing -> initialized
2025-05-11 16:45:00,210 - vibe_check_actor_initializer - INFO - Actor file_actor_0 state changed: initializing -> initialized
2025-05-11 16:45:00,210 - vibe_check_initialization_tracker - INFO - Actor file_actor_0 initialization_complete
2025-05-11 16:45:00,210 - vibe_check_actor_system - INFO - Actor file_actor_0 initialized successfully
2025-05-11 16:45:00,210 - vibe_check_actor_system - INFO - Actor file_actor_0 is already registered with initialization manager
2025-05-11 16:45:00,210 - vibe_check_actor_system - INFO - Actor file_actor_0 is already registered with legacy initializer
2025-05-11 16:45:00,211 - vibe_check_actor_initializer - INFO - Actor file_actor_0 state changed: REGISTERED -> INITIALIZING
2025-05-11 16:45:00,211 - vibe_check_actor_system.file_actor_0 - INFO - State transition: REGISTERED -> INITIALIZING
2025-05-11 16:45:00,211 - vibe_check_actor_system.file_actor_0 - DEBUG - State transition details: {'phase': 'initialize'}
2025-05-11 16:45:00,211 - vibe_check_actor_system.file_actor_0 - DEBUG - Operation 'state_transition_REGISTERED_to_INITIALIZING' took 0.000092 seconds
2025-05-11 16:45:00,211 - vibe_check_actor_system.initializer - DEBUG - Component state: {'actor_id': 'file_actor_0', 'state': 'INITIALIZING', 'previous_state': 'REGISTERED', 'timestamp': 1746974700.211054, 'details': {'phase': 'initialize'}}
2025-05-11 16:45:00,211 - vibe_check_actor_registry - WARNING - Actor file_actor_0 already registered, updating
2025-05-11 16:45:00,211 - vibe_check_actor_registry - INFO - Registered actor file_actor_0 of type file
2025-05-11 16:45:00,211 - vibe_check_actor_system - DEBUG - Actor file_actor_0 has no _initialize method
2025-05-11 16:45:00,211 - vibe_check_actor_initializer - INFO - Actor file_actor_0 state changed: INITIALIZING -> INITIALIZED
2025-05-11 16:45:00,211 - vibe_check_actor_system.file_actor_0 - INFO - State transition: INITIALIZING -> INITIALIZED
2025-05-11 16:45:00,211 - vibe_check_actor_system.file_actor_0 - DEBUG - State transition details: {'phase': 'initialize'}
2025-05-11 16:45:00,211 - vibe_check_actor_system.file_actor_0 - DEBUG - Operation 'state_transition_INITIALIZING_to_INITIALIZED' took 0.000069 seconds
2025-05-11 16:45:00,211 - vibe_check_actor_system.initializer - DEBUG - Component state: {'actor_id': 'file_actor_0', 'state': 'INITIALIZED', 'previous_state': 'INITIALIZING', 'timestamp': 1746974700.211322, 'details': {'phase': 'initialize'}}
2025-05-11 16:45:00,211 - vibe_check_synchronization - DEBUG - Actor file_actor_0 reached synchronization point initialization_complete
2025-05-11 16:45:00,211 - vibe_check_actor_system - INFO - Actor file_actor_0 initialized successfully
2025-05-11 16:45:00,211 - vibe_check_actor_system.actor.file_actor_0 - ERROR - Error during state transition to FAILED: Actor file_actor_0: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds
2025-05-11 16:45:00,211 - vibe_check_actor_system.actor.file_actor_0 - ERROR - Traceback:
NoneType: None

2025-05-11 16:45:00,211 - vibe_check_actor_initializer - INFO - Actor file_actor_0 state changed: INITIALIZED -> FAILED
2025-05-11 16:45:00,211 - vibe_check_actor_system.file_actor_0 - INFO - State transition: INITIALIZED -> FAILED
2025-05-11 16:45:00,211 - vibe_check_actor_system.file_actor_0 - DEBUG - State transition details: {'phase': 'initialize'}
2025-05-11 16:45:00,211 - vibe_check_actor_system.file_actor_0 - DEBUG - Operation 'state_transition_INITIALIZED_to_FAILED' took 0.000196 seconds
2025-05-11 16:45:00,211 - vibe_check_actor_system.initializer - DEBUG - Component state: {'actor_id': 'file_actor_0', 'state': 'FAILED', 'previous_state': 'INITIALIZED', 'timestamp': 1746974700.211532, 'details': {'phase': 'initialize'}}
2025-05-11 16:45:00,211 - vibe_check_actor_initializer - DEBUG - Cleaned up resources for actor file_actor_0
2025-05-11 16:45:00,211 - vibe_check_actor_system - INFO - Cleaning up resources for actor file_actor_0
2025-05-11 16:45:00,211 - vibe_check_actor_system - INFO - Cleared mailbox for actor file_actor_0
2025-05-11 16:45:00,211 - vibe_check_actor_registry - INFO - Unregistered actor file_actor_0
2025-05-11 16:45:00,211 - vibe_check_actor_system - INFO - Unregistered actor file_actor_0 from registry during cleanup
2025-05-11 16:45:00,211 - vibe_check_actor_initialization - INFO - Actor file_actor_0 state changed: initialized -> stopped
2025-05-11 16:45:00,211 - vibe_check_actor_system - INFO - Set actor file_actor_0 state to STOPPED in initialization manager
2025-05-11 16:45:00,211 - vibe_check_actor_initializer - ERROR - Invalid state transition for actor file_actor_0: initialized -> stopped
2025-05-11 16:45:00,211 - vibe_check_actor_initializer - DEBUG - Valid transitions from initialized are: ['stopping', 'starting', 'rollback', 'failed']
2025-05-11 16:45:00,211 - vibe_check_actor_initializer - WARNING - Forcing transition to stopped state for actor file_actor_0
2025-05-11 16:45:00,211 - vibe_check_actor_initializer - INFO - Actor file_actor_0 state changed: initialized -> stopped
2025-05-11 16:45:00,212 - vibe_check_actor_system - INFO - Set actor file_actor_0 state to STOPPED in legacy initializer
2025-05-11 16:45:00,212 - vibe_check_actor_system - INFO - Cleaned up resources for actor file_actor_0
2025-05-11 16:45:00,212 - vibe_check_actor_system - ERROR - Error in initialization process for actor file_actor_0: Actor file_actor_0: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds
2025-05-11 16:45:00,212 - vibe_check_actor_initializer - ERROR - Actor file_actor_0: Invalid state transition: FAILED -> FAILED
2025-05-11 16:45:00,212 - vibe_check_actor_system - ERROR - Error rolling back actor file_actor_0: Actor file_actor_0: Invalid state transition: FAILED -> FAILED (state: FAILED)
2025-05-11 16:45:00,212 - vibe_check_actor_system - INFO - Cleaning up resources for actor file_actor_0
2025-05-11 16:45:00,212 - vibe_check_actor_system - INFO - Cleared mailbox for actor file_actor_0
2025-05-11 16:45:00,212 - vibe_check_actor_registry - WARNING - Actor file_actor_0 not registered, cannot unregister
2025-05-11 16:45:00,212 - vibe_check_actor_system - INFO - Unregistered actor file_actor_0 from registry during cleanup
2025-05-11 16:45:00,212 - vibe_check_actor_initialization - DEBUG - Actor file_actor_0 is already in state stopped, no change needed
2025-05-11 16:45:00,212 - vibe_check_actor_system - INFO - Set actor file_actor_0 state to STOPPED in initialization manager
2025-05-11 16:45:00,212 - vibe_check_actor_initializer - DEBUG - Actor file_actor_0 is already in state stopped, no change needed
2025-05-11 16:45:00,212 - vibe_check_actor_initializer - INFO - Actor file_actor_0 state changed: stopped -> stopped
2025-05-11 16:45:00,212 - vibe_check_actor_system - INFO - Set actor file_actor_0 state to STOPPED in legacy initializer
2025-05-11 16:45:00,212 - vibe_check_actor_system - INFO - Cleaned up resources for actor file_actor_0
2025-05-11 16:45:00,212 - vibe_check_actor_initializer - ERROR - [ACTOR INIT] Failed to initialize actor file_actor_0: Actor file_actor_0: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds
2025-05-11 16:45:00,212 - vibe_check_actor_initializer - ERROR - [ACTOR INIT] Traceback for actor file_actor_0:
Traceback (most recent call last):
  File "/Users/<USER>/Lokalne_Kody/PAT_project_analysis/vibe_check/core/actor_system/actor_initializer.py", line 936, in initialize_actor
    await asyncio.wait_for(actor.initialize(), timeout)
  File "/Users/<USER>/.pyenv/versions/3.13.3/lib/python3.13/asyncio/tasks.py", line 507, in wait_for
    return await fut
           ^^^^^^^^^
  File "/Users/<USER>/Lokalne_Kody/PAT_project_analysis/vibe_check/core/actor_system/actor.py", line 1109, in initialize
    raise timeout_error
vibe_check.core.actor_system.exceptions.ActorTimeoutError: Actor file_actor_0: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds

2025-05-11 16:45:00,212 - vibe_check_actor_initializer - DEBUG - [ACTOR INIT] Setting actor file_actor_0 state to FAILED
2025-05-11 16:45:00,212 - vibe_check_actor_initializer - ERROR - Invalid state transition for actor file_actor_0: stopped -> failed
2025-05-11 16:45:00,212 - vibe_check_actor_initializer - DEBUG - Valid transitions from stopped are: ['initializing', 'created']
2025-05-11 16:45:00,212 - vibe_check_actor_initializer - WARNING - Forcing transition to failed state for actor file_actor_0
2025-05-11 16:45:00,212 - vibe_check_actor_initializer - INFO - Actor file_actor_0 state changed: stopped -> failed
2025-05-11 16:45:00,213 - vibe_check_actor_initializer - ERROR - Actor file_actor_0 failed: Actor file_actor_0 failed during initialize phase (state: stopped): Actor file_actor_0: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds
Original error: Actor file_actor_0: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds
2025-05-11 16:45:00,213 - vibe_check_actor_lifecycle_manager - WARNING - Failed to initialize actor file_actor_0 (attempt 1/3): Actor file_actor_0: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds
2025-05-11 16:45:00,213 - vibe_check_initialization_tracker - INFO - Actor file_actor_0 initialization_start
2025-05-11 16:45:00,213 - vibe_check_initialization_tracker - INFO - Actor file_actor_0 initialization_start
2025-05-11 16:45:00,213 - vibe_check_initialization_tracker - INFO - Actor file_actor_0 initialization_complete
2025-05-11 16:45:00,213 - vibe_check_initialization_tracker - INFO - Actor file_actor_0 initialization_complete
2025-05-11 16:45:00,213 - vibe_check_initialization_tracker - ERROR - Actor file_actor_0 failed: Actor file_actor_0: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds
2025-05-11 16:45:00,314 - vibe_check_actor_lifecycle_manager - INFO - Initializing actor file_actor_0 (type: file)
2025-05-11 16:45:00,315 - vibe_check_actor_initializer - INFO - [ACTOR INIT] Starting initialization of actor file_actor_0
2025-05-11 16:45:00,315 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting registry for actor file_actor_0
2025-05-11 16:45:00,315 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting actor file_actor_0 from registry
2025-05-11 16:45:00,315 - vibe_check_actor_initializer - WARNING - [ACTOR REGISTRY] Actor file_actor_0 not found in registry
2025-05-11 16:45:00,315 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Available actors in registry: ['file_actor_1', 'file_actor_2', 'file_actor_3', 'file_actor_4', 'file_actor_5', 'file_actor_6', 'file_actor_7', 'file_actor_8', 'file_actor_9', 'file_actor_10', 'file_actor_11', 'file_actor_12', 'file_actor_13', 'project_actor']
2025-05-11 16:45:00,315 - vibe_check_actor_initializer - WARNING - [ACTOR INIT] Actor file_actor_0 not found in registry, retrying (1/3)
2025-05-11 16:45:00,416 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting registry for actor file_actor_0
2025-05-11 16:45:00,416 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting actor file_actor_0 from registry
2025-05-11 16:45:00,416 - vibe_check_actor_initializer - WARNING - [ACTOR REGISTRY] Actor file_actor_0 not found in registry
2025-05-11 16:45:00,416 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Available actors in registry: ['file_actor_1', 'file_actor_2', 'file_actor_3', 'file_actor_4', 'file_actor_5', 'file_actor_6', 'file_actor_7', 'file_actor_8', 'file_actor_9', 'file_actor_10', 'file_actor_11', 'file_actor_12', 'file_actor_13', 'project_actor']
2025-05-11 16:45:00,416 - vibe_check_actor_initializer - WARNING - [ACTOR INIT] Actor file_actor_0 not found in registry, retrying (2/3)
2025-05-11 16:45:00,617 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting registry for actor file_actor_0
2025-05-11 16:45:00,617 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting actor file_actor_0 from registry
2025-05-11 16:45:00,617 - vibe_check_actor_initializer - WARNING - [ACTOR REGISTRY] Actor file_actor_0 not found in registry
2025-05-11 16:45:00,618 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Available actors in registry: ['file_actor_1', 'file_actor_2', 'file_actor_3', 'file_actor_4', 'file_actor_5', 'file_actor_6', 'file_actor_7', 'file_actor_8', 'file_actor_9', 'file_actor_10', 'file_actor_11', 'file_actor_12', 'file_actor_13', 'project_actor']
2025-05-11 16:45:00,618 - vibe_check_actor_initializer - ERROR - [ACTOR INIT] Actor file_actor_0 not found in registry after 3 attempts
2025-05-11 16:45:00,618 - vibe_check_actor_lifecycle_manager - WARNING - Failed to initialize actor file_actor_0 (attempt 2/3): Actor file_actor_0 not found in registry after 3 attempts
2025-05-11 16:45:00,819 - vibe_check_actor_lifecycle_manager - INFO - Initializing actor file_actor_0 (type: file)
2025-05-11 16:45:00,819 - vibe_check_actor_initializer - INFO - [ACTOR INIT] Starting initialization of actor file_actor_0
2025-05-11 16:45:00,819 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting registry for actor file_actor_0
2025-05-11 16:45:00,819 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting actor file_actor_0 from registry
2025-05-11 16:45:00,819 - vibe_check_actor_initializer - WARNING - [ACTOR REGISTRY] Actor file_actor_0 not found in registry
2025-05-11 16:45:00,819 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Available actors in registry: ['file_actor_1', 'file_actor_2', 'file_actor_3', 'file_actor_4', 'file_actor_5', 'file_actor_6', 'file_actor_7', 'file_actor_8', 'file_actor_9', 'file_actor_10', 'file_actor_11', 'file_actor_12', 'file_actor_13', 'project_actor']
2025-05-11 16:45:00,819 - vibe_check_actor_initializer - WARNING - [ACTOR INIT] Actor file_actor_0 not found in registry, retrying (1/3)
2025-05-11 16:45:00,921 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting registry for actor file_actor_0
2025-05-11 16:45:00,922 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting actor file_actor_0 from registry
2025-05-11 16:45:00,922 - vibe_check_actor_initializer - WARNING - [ACTOR REGISTRY] Actor file_actor_0 not found in registry
2025-05-11 16:45:00,922 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Available actors in registry: ['file_actor_1', 'file_actor_2', 'file_actor_3', 'file_actor_4', 'file_actor_5', 'file_actor_6', 'file_actor_7', 'file_actor_8', 'file_actor_9', 'file_actor_10', 'file_actor_11', 'file_actor_12', 'file_actor_13', 'project_actor']
2025-05-11 16:45:00,922 - vibe_check_actor_initializer - WARNING - [ACTOR INIT] Actor file_actor_0 not found in registry, retrying (2/3)
2025-05-11 16:45:01,124 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting registry for actor file_actor_0
2025-05-11 16:45:01,124 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting actor file_actor_0 from registry
2025-05-11 16:45:01,124 - vibe_check_actor_initializer - WARNING - [ACTOR REGISTRY] Actor file_actor_0 not found in registry
2025-05-11 16:45:01,124 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Available actors in registry: ['file_actor_1', 'file_actor_2', 'file_actor_3', 'file_actor_4', 'file_actor_5', 'file_actor_6', 'file_actor_7', 'file_actor_8', 'file_actor_9', 'file_actor_10', 'file_actor_11', 'file_actor_12', 'file_actor_13', 'project_actor']
2025-05-11 16:45:01,124 - vibe_check_actor_initializer - ERROR - [ACTOR INIT] Actor file_actor_0 not found in registry after 3 attempts
2025-05-11 16:45:01,124 - vibe_check_actor_lifecycle_manager - ERROR - Failed to initialize actor file_actor_0 after 3 attempts: Actor file_actor_0 not found in registry after 3 attempts
2025-05-11 16:45:01,124 - vibe_check_actor_lifecycle_manager - ERROR - Traceback (most recent call last):
  File "/Users/<USER>/Lokalne_Kody/PAT_project_analysis/vibe_check/core/orchestration/actor_lifecycle_manager.py", line 174, in _initialize_all_actors
    await initializer.initialize_actor(actor_id)
  File "/Users/<USER>/Lokalne_Kody/PAT_project_analysis/vibe_check/core/actor_system/actor_initializer.py", line 883, in initialize_actor
    raise ValueError(error_msg)
ValueError: Actor file_actor_0 not found in registry after 3 attempts

2025-05-11 16:45:01,124 - vibe_check_actor_lifecycle_manager - INFO - Initializing actor file_actor_1 (type: file)
2025-05-11 16:45:01,124 - vibe_check_actor_initializer - INFO - [ACTOR INIT] Starting initialization of actor file_actor_1
2025-05-11 16:45:01,124 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting registry for actor file_actor_1
2025-05-11 16:45:01,124 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting actor file_actor_1 from registry
2025-05-11 16:45:01,124 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Successfully retrieved actor file_actor_1 from registry
2025-05-11 16:45:01,124 - vibe_check_actor_initializer - INFO - [ACTOR INIT] Actor file_actor_1 is of type file
2025-05-11 16:45:01,124 - vibe_check_actor_initializer - DEBUG - [ACTOR INIT] Setting actor file_actor_1 state to INITIALIZING
2025-05-11 16:45:01,124 - vibe_check_actor_initializer - INFO - Actor file_actor_1 state changed: created -> initializing
2025-05-11 16:45:01,124 - vibe_check_actor_initializer - DEBUG - [ACTOR INIT] Calling initialize() method on actor file_actor_1
2025-05-11 16:45:01,125 - vibe_check_actor_system - INFO - Actor file_actor_1 is already registered with initialization manager
2025-05-11 16:45:01,125 - vibe_check_actor_system - INFO - Actor file_actor_1 is already registered with legacy initializer
2025-05-11 16:45:01,125 - vibe_check_actor_initialization - INFO - Actor file_actor_1 state changed: created -> initializing
2025-05-11 16:45:01,125 - vibe_check_actor_initializer - DEBUG - Actor file_actor_1 is already in state initializing, no change needed
2025-05-11 16:45:01,125 - vibe_check_initialization_tracker - INFO - Actor file_actor_1 initialization_start
2025-05-11 16:45:01,125 - vibe_check_actor_system - DEBUG - Actor file_actor_1 has no _initialize method
2025-05-11 16:45:01,125 - vibe_check_actor_initialization - INFO - Actor file_actor_1 state changed: initializing -> initialized
2025-05-11 16:45:01,125 - vibe_check_actor_initializer - INFO - Actor file_actor_1 state changed: initializing -> initialized
2025-05-11 16:45:01,125 - vibe_check_initialization_tracker - INFO - Actor file_actor_1 initialization_complete
2025-05-11 16:45:01,125 - vibe_check_actor_system - INFO - Actor file_actor_1 initialized successfully
2025-05-11 16:45:01,125 - vibe_check_actor_system - INFO - Actor file_actor_1 is already registered with initialization manager
2025-05-11 16:45:01,125 - vibe_check_actor_system - INFO - Actor file_actor_1 is already registered with legacy initializer
2025-05-11 16:45:01,125 - vibe_check_actor_initializer - INFO - Actor file_actor_1 state changed: REGISTERED -> INITIALIZING
2025-05-11 16:45:01,125 - vibe_check_actor_system.file_actor_1 - INFO - State transition: REGISTERED -> INITIALIZING
2025-05-11 16:45:01,125 - vibe_check_actor_system.file_actor_1 - DEBUG - State transition details: {'phase': 'initialize'}
2025-05-11 16:45:01,125 - vibe_check_actor_system.file_actor_1 - DEBUG - Operation 'state_transition_REGISTERED_to_INITIALIZING' took 0.000071 seconds
2025-05-11 16:45:01,125 - vibe_check_actor_system.initializer - DEBUG - Component state: {'actor_id': 'file_actor_1', 'state': 'INITIALIZING', 'previous_state': 'REGISTERED', 'timestamp': 1746974701.125477, 'details': {'phase': 'initialize'}}
2025-05-11 16:45:01,125 - vibe_check_actor_registry - WARNING - Actor file_actor_1 already registered, updating
2025-05-11 16:45:01,125 - vibe_check_actor_registry - INFO - Registered actor file_actor_1 of type file
2025-05-11 16:45:01,125 - vibe_check_actor_system - DEBUG - Actor file_actor_1 has no _initialize method
2025-05-11 16:45:01,125 - vibe_check_actor_initializer - INFO - Actor file_actor_1 state changed: INITIALIZING -> INITIALIZED
2025-05-11 16:45:01,125 - vibe_check_actor_system.file_actor_1 - INFO - State transition: INITIALIZING -> INITIALIZED
2025-05-11 16:45:01,125 - vibe_check_actor_system.file_actor_1 - DEBUG - State transition details: {'phase': 'initialize'}
2025-05-11 16:45:01,125 - vibe_check_actor_system.file_actor_1 - DEBUG - Operation 'state_transition_INITIALIZING_to_INITIALIZED' took 0.000052 seconds
2025-05-11 16:45:01,125 - vibe_check_actor_system.initializer - DEBUG - Component state: {'actor_id': 'file_actor_1', 'state': 'INITIALIZED', 'previous_state': 'INITIALIZING', 'timestamp': 1746974701.125689, 'details': {'phase': 'initialize'}}
2025-05-11 16:45:01,125 - vibe_check_synchronization - DEBUG - Actor file_actor_1 reached synchronization point initialization_complete
2025-05-11 16:45:01,125 - vibe_check_actor_system - INFO - Actor file_actor_1 initialized successfully
2025-05-11 16:45:01,125 - vibe_check_actor_system.actor.file_actor_1 - ERROR - Error during state transition to FAILED: Actor file_actor_1: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds
2025-05-11 16:45:01,125 - vibe_check_actor_system.actor.file_actor_1 - ERROR - Traceback:
NoneType: None

2025-05-11 16:45:01,125 - vibe_check_actor_initializer - INFO - Actor file_actor_1 state changed: INITIALIZED -> FAILED
2025-05-11 16:45:01,125 - vibe_check_actor_system.file_actor_1 - INFO - State transition: INITIALIZED -> FAILED
2025-05-11 16:45:01,125 - vibe_check_actor_system.file_actor_1 - DEBUG - State transition details: {'phase': 'initialize'}
2025-05-11 16:45:01,126 - vibe_check_actor_system.file_actor_1 - DEBUG - Operation 'state_transition_INITIALIZED_to_FAILED' took 0.000165 seconds
2025-05-11 16:45:01,126 - vibe_check_actor_system.initializer - DEBUG - Component state: {'actor_id': 'file_actor_1', 'state': 'FAILED', 'previous_state': 'INITIALIZED', 'timestamp': 1746974701.125846, 'details': {'phase': 'initialize'}}
2025-05-11 16:45:01,126 - vibe_check_actor_initializer - DEBUG - Cleaned up resources for actor file_actor_1
2025-05-11 16:45:01,126 - vibe_check_actor_system - INFO - Cleaning up resources for actor file_actor_1
2025-05-11 16:45:01,126 - vibe_check_actor_system - INFO - Cleared mailbox for actor file_actor_1
2025-05-11 16:45:01,126 - vibe_check_actor_registry - INFO - Unregistered actor file_actor_1
2025-05-11 16:45:01,126 - vibe_check_actor_system - INFO - Unregistered actor file_actor_1 from registry during cleanup
2025-05-11 16:45:01,126 - vibe_check_actor_initialization - INFO - Actor file_actor_1 state changed: initialized -> stopped
2025-05-11 16:45:01,126 - vibe_check_actor_system - INFO - Set actor file_actor_1 state to STOPPED in initialization manager
2025-05-11 16:45:01,126 - vibe_check_actor_initializer - ERROR - Invalid state transition for actor file_actor_1: initialized -> stopped
2025-05-11 16:45:01,126 - vibe_check_actor_initializer - DEBUG - Valid transitions from initialized are: ['stopping', 'starting', 'rollback', 'failed']
2025-05-11 16:45:01,126 - vibe_check_actor_initializer - WARNING - Forcing transition to stopped state for actor file_actor_1
2025-05-11 16:45:01,126 - vibe_check_actor_initializer - INFO - Actor file_actor_1 state changed: initialized -> stopped
2025-05-11 16:45:01,126 - vibe_check_actor_system - INFO - Set actor file_actor_1 state to STOPPED in legacy initializer
2025-05-11 16:45:01,126 - vibe_check_actor_system - INFO - Cleaned up resources for actor file_actor_1
2025-05-11 16:45:01,126 - vibe_check_actor_system - ERROR - Error in initialization process for actor file_actor_1: Actor file_actor_1: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds
2025-05-11 16:45:01,126 - vibe_check_actor_initializer - ERROR - Actor file_actor_1: Invalid state transition: FAILED -> FAILED
2025-05-11 16:45:01,126 - vibe_check_actor_system - ERROR - Error rolling back actor file_actor_1: Actor file_actor_1: Invalid state transition: FAILED -> FAILED (state: FAILED)
2025-05-11 16:45:01,126 - vibe_check_actor_system - INFO - Cleaning up resources for actor file_actor_1
2025-05-11 16:45:01,126 - vibe_check_actor_system - INFO - Cleared mailbox for actor file_actor_1
2025-05-11 16:45:01,126 - vibe_check_actor_registry - WARNING - Actor file_actor_1 not registered, cannot unregister
2025-05-11 16:45:01,126 - vibe_check_actor_system - INFO - Unregistered actor file_actor_1 from registry during cleanup
2025-05-11 16:45:01,126 - vibe_check_actor_initialization - DEBUG - Actor file_actor_1 is already in state stopped, no change needed
2025-05-11 16:45:01,126 - vibe_check_actor_system - INFO - Set actor file_actor_1 state to STOPPED in initialization manager
2025-05-11 16:45:01,126 - vibe_check_actor_initializer - DEBUG - Actor file_actor_1 is already in state stopped, no change needed
2025-05-11 16:45:01,126 - vibe_check_actor_initializer - INFO - Actor file_actor_1 state changed: stopped -> stopped
2025-05-11 16:45:01,126 - vibe_check_actor_system - INFO - Set actor file_actor_1 state to STOPPED in legacy initializer
2025-05-11 16:45:01,126 - vibe_check_actor_system - INFO - Cleaned up resources for actor file_actor_1
2025-05-11 16:45:01,126 - vibe_check_actor_initializer - ERROR - [ACTOR INIT] Failed to initialize actor file_actor_1: Actor file_actor_1: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds
2025-05-11 16:45:01,126 - vibe_check_actor_initializer - ERROR - [ACTOR INIT] Traceback for actor file_actor_1:
Traceback (most recent call last):
  File "/Users/<USER>/Lokalne_Kody/PAT_project_analysis/vibe_check/core/actor_system/actor_initializer.py", line 936, in initialize_actor
    await asyncio.wait_for(actor.initialize(), timeout)
  File "/Users/<USER>/.pyenv/versions/3.13.3/lib/python3.13/asyncio/tasks.py", line 507, in wait_for
    return await fut
           ^^^^^^^^^
  File "/Users/<USER>/Lokalne_Kody/PAT_project_analysis/vibe_check/core/actor_system/actor.py", line 1109, in initialize
    raise timeout_error
vibe_check.core.actor_system.exceptions.ActorTimeoutError: Actor file_actor_1: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds

2025-05-11 16:45:01,127 - vibe_check_actor_initializer - DEBUG - [ACTOR INIT] Setting actor file_actor_1 state to FAILED
2025-05-11 16:45:01,127 - vibe_check_actor_initializer - ERROR - Invalid state transition for actor file_actor_1: stopped -> failed
2025-05-11 16:45:01,127 - vibe_check_actor_initializer - DEBUG - Valid transitions from stopped are: ['initializing', 'created']
2025-05-11 16:45:01,127 - vibe_check_actor_initializer - WARNING - Forcing transition to failed state for actor file_actor_1
2025-05-11 16:45:01,127 - vibe_check_actor_initializer - INFO - Actor file_actor_1 state changed: stopped -> failed
2025-05-11 16:45:01,127 - vibe_check_actor_initializer - ERROR - Actor file_actor_1 failed: Actor file_actor_1 failed during initialize phase (state: stopped): Actor file_actor_1: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds
Original error: Actor file_actor_1: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds
2025-05-11 16:45:01,127 - vibe_check_actor_lifecycle_manager - WARNING - Failed to initialize actor file_actor_1 (attempt 1/3): Actor file_actor_1: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds
2025-05-11 16:45:01,127 - vibe_check_initialization_tracker - INFO - Actor file_actor_1 initialization_start
2025-05-11 16:45:01,127 - vibe_check_initialization_tracker - INFO - Actor file_actor_1 initialization_start
2025-05-11 16:45:01,127 - vibe_check_initialization_tracker - INFO - Actor file_actor_1 initialization_complete
2025-05-11 16:45:01,127 - vibe_check_initialization_tracker - INFO - Actor file_actor_1 initialization_complete
2025-05-11 16:45:01,127 - vibe_check_initialization_tracker - ERROR - Actor file_actor_1 failed: Actor file_actor_1: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds
2025-05-11 16:45:01,228 - vibe_check_actor_lifecycle_manager - INFO - Initializing actor file_actor_1 (type: file)
2025-05-11 16:45:01,228 - vibe_check_actor_initializer - INFO - [ACTOR INIT] Starting initialization of actor file_actor_1
2025-05-11 16:45:01,229 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting registry for actor file_actor_1
2025-05-11 16:45:01,229 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting actor file_actor_1 from registry
2025-05-11 16:45:01,229 - vibe_check_actor_initializer - WARNING - [ACTOR REGISTRY] Actor file_actor_1 not found in registry
2025-05-11 16:45:01,229 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Available actors in registry: ['file_actor_2', 'file_actor_3', 'file_actor_4', 'file_actor_5', 'file_actor_6', 'file_actor_7', 'file_actor_8', 'file_actor_9', 'file_actor_10', 'file_actor_11', 'file_actor_12', 'file_actor_13', 'project_actor']
2025-05-11 16:45:01,229 - vibe_check_actor_initializer - WARNING - [ACTOR INIT] Actor file_actor_1 not found in registry, retrying (1/3)
2025-05-11 16:45:01,330 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting registry for actor file_actor_1
2025-05-11 16:45:01,330 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting actor file_actor_1 from registry
2025-05-11 16:45:01,330 - vibe_check_actor_initializer - WARNING - [ACTOR REGISTRY] Actor file_actor_1 not found in registry
2025-05-11 16:45:01,331 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Available actors in registry: ['file_actor_2', 'file_actor_3', 'file_actor_4', 'file_actor_5', 'file_actor_6', 'file_actor_7', 'file_actor_8', 'file_actor_9', 'file_actor_10', 'file_actor_11', 'file_actor_12', 'file_actor_13', 'project_actor']
2025-05-11 16:45:01,331 - vibe_check_actor_initializer - WARNING - [ACTOR INIT] Actor file_actor_1 not found in registry, retrying (2/3)
2025-05-11 16:45:01,532 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting registry for actor file_actor_1
2025-05-11 16:45:01,532 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting actor file_actor_1 from registry
2025-05-11 16:45:01,532 - vibe_check_actor_initializer - WARNING - [ACTOR REGISTRY] Actor file_actor_1 not found in registry
2025-05-11 16:45:01,532 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Available actors in registry: ['file_actor_2', 'file_actor_3', 'file_actor_4', 'file_actor_5', 'file_actor_6', 'file_actor_7', 'file_actor_8', 'file_actor_9', 'file_actor_10', 'file_actor_11', 'file_actor_12', 'file_actor_13', 'project_actor']
2025-05-11 16:45:01,532 - vibe_check_actor_initializer - ERROR - [ACTOR INIT] Actor file_actor_1 not found in registry after 3 attempts
2025-05-11 16:45:01,532 - vibe_check_actor_lifecycle_manager - WARNING - Failed to initialize actor file_actor_1 (attempt 2/3): Actor file_actor_1 not found in registry after 3 attempts
2025-05-11 16:45:01,733 - vibe_check_actor_lifecycle_manager - INFO - Initializing actor file_actor_1 (type: file)
2025-05-11 16:45:01,733 - vibe_check_actor_initializer - INFO - [ACTOR INIT] Starting initialization of actor file_actor_1
2025-05-11 16:45:01,733 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting registry for actor file_actor_1
2025-05-11 16:45:01,733 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting actor file_actor_1 from registry
2025-05-11 16:45:01,733 - vibe_check_actor_initializer - WARNING - [ACTOR REGISTRY] Actor file_actor_1 not found in registry
2025-05-11 16:45:01,733 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Available actors in registry: ['file_actor_2', 'file_actor_3', 'file_actor_4', 'file_actor_5', 'file_actor_6', 'file_actor_7', 'file_actor_8', 'file_actor_9', 'file_actor_10', 'file_actor_11', 'file_actor_12', 'file_actor_13', 'project_actor']
2025-05-11 16:45:01,733 - vibe_check_actor_initializer - WARNING - [ACTOR INIT] Actor file_actor_1 not found in registry, retrying (1/3)
2025-05-11 16:45:01,834 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting registry for actor file_actor_1
2025-05-11 16:45:01,834 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting actor file_actor_1 from registry
2025-05-11 16:45:01,834 - vibe_check_actor_initializer - WARNING - [ACTOR REGISTRY] Actor file_actor_1 not found in registry
2025-05-11 16:45:01,834 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Available actors in registry: ['file_actor_2', 'file_actor_3', 'file_actor_4', 'file_actor_5', 'file_actor_6', 'file_actor_7', 'file_actor_8', 'file_actor_9', 'file_actor_10', 'file_actor_11', 'file_actor_12', 'file_actor_13', 'project_actor']
2025-05-11 16:45:01,834 - vibe_check_actor_initializer - WARNING - [ACTOR INIT] Actor file_actor_1 not found in registry, retrying (2/3)
2025-05-11 16:45:02,036 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting registry for actor file_actor_1
2025-05-11 16:45:02,036 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting actor file_actor_1 from registry
2025-05-11 16:45:02,036 - vibe_check_actor_initializer - WARNING - [ACTOR REGISTRY] Actor file_actor_1 not found in registry
2025-05-11 16:45:02,036 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Available actors in registry: ['file_actor_2', 'file_actor_3', 'file_actor_4', 'file_actor_5', 'file_actor_6', 'file_actor_7', 'file_actor_8', 'file_actor_9', 'file_actor_10', 'file_actor_11', 'file_actor_12', 'file_actor_13', 'project_actor']
2025-05-11 16:45:02,036 - vibe_check_actor_initializer - ERROR - [ACTOR INIT] Actor file_actor_1 not found in registry after 3 attempts
2025-05-11 16:45:02,036 - vibe_check_actor_lifecycle_manager - ERROR - Failed to initialize actor file_actor_1 after 3 attempts: Actor file_actor_1 not found in registry after 3 attempts
2025-05-11 16:45:02,037 - vibe_check_actor_lifecycle_manager - ERROR - Traceback (most recent call last):
  File "/Users/<USER>/Lokalne_Kody/PAT_project_analysis/vibe_check/core/orchestration/actor_lifecycle_manager.py", line 174, in _initialize_all_actors
    await initializer.initialize_actor(actor_id)
  File "/Users/<USER>/Lokalne_Kody/PAT_project_analysis/vibe_check/core/actor_system/actor_initializer.py", line 883, in initialize_actor
    raise ValueError(error_msg)
ValueError: Actor file_actor_1 not found in registry after 3 attempts

2025-05-11 16:45:02,037 - vibe_check_actor_lifecycle_manager - INFO - Initializing actor file_actor_2 (type: file)
2025-05-11 16:45:02,037 - vibe_check_actor_initializer - INFO - [ACTOR INIT] Starting initialization of actor file_actor_2
2025-05-11 16:45:02,037 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting registry for actor file_actor_2
2025-05-11 16:45:02,037 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting actor file_actor_2 from registry
2025-05-11 16:45:02,037 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Successfully retrieved actor file_actor_2 from registry
2025-05-11 16:45:02,037 - vibe_check_actor_initializer - INFO - [ACTOR INIT] Actor file_actor_2 is of type file
2025-05-11 16:45:02,037 - vibe_check_actor_initializer - DEBUG - [ACTOR INIT] Setting actor file_actor_2 state to INITIALIZING
2025-05-11 16:45:02,037 - vibe_check_actor_initializer - INFO - Actor file_actor_2 state changed: created -> initializing
2025-05-11 16:45:02,038 - vibe_check_actor_initializer - DEBUG - [ACTOR INIT] Calling initialize() method on actor file_actor_2
2025-05-11 16:45:02,038 - vibe_check_actor_system - INFO - Actor file_actor_2 is already registered with initialization manager
2025-05-11 16:45:02,038 - vibe_check_actor_system - INFO - Actor file_actor_2 is already registered with legacy initializer
2025-05-11 16:45:02,038 - vibe_check_actor_initialization - INFO - Actor file_actor_2 state changed: created -> initializing
2025-05-11 16:45:02,038 - vibe_check_actor_initializer - DEBUG - Actor file_actor_2 is already in state initializing, no change needed
2025-05-11 16:45:02,038 - vibe_check_initialization_tracker - INFO - Actor file_actor_2 initialization_start
2025-05-11 16:45:02,039 - vibe_check_actor_system - DEBUG - Actor file_actor_2 has no _initialize method
2025-05-11 16:45:02,039 - vibe_check_actor_initialization - INFO - Actor file_actor_2 state changed: initializing -> initialized
2025-05-11 16:45:02,039 - vibe_check_actor_initializer - INFO - Actor file_actor_2 state changed: initializing -> initialized
2025-05-11 16:45:02,039 - vibe_check_initialization_tracker - INFO - Actor file_actor_2 initialization_complete
2025-05-11 16:45:02,039 - vibe_check_actor_system - INFO - Actor file_actor_2 initialized successfully
2025-05-11 16:45:02,039 - vibe_check_actor_system - INFO - Actor file_actor_2 is already registered with initialization manager
2025-05-11 16:45:02,039 - vibe_check_actor_system - INFO - Actor file_actor_2 is already registered with legacy initializer
2025-05-11 16:45:02,039 - vibe_check_actor_initializer - INFO - Actor file_actor_2 state changed: REGISTERED -> INITIALIZING
2025-05-11 16:45:02,039 - vibe_check_actor_system.file_actor_2 - INFO - State transition: REGISTERED -> INITIALIZING
2025-05-11 16:45:02,039 - vibe_check_actor_system.file_actor_2 - DEBUG - State transition details: {'phase': 'initialize'}
2025-05-11 16:45:02,039 - vibe_check_actor_system.file_actor_2 - DEBUG - Operation 'state_transition_REGISTERED_to_INITIALIZING' took 0.000130 seconds
2025-05-11 16:45:02,039 - vibe_check_actor_system.initializer - DEBUG - Component state: {'actor_id': 'file_actor_2', 'state': 'INITIALIZING', 'previous_state': 'REGISTERED', 'timestamp': 1746974702.039665, 'details': {'phase': 'initialize'}}
2025-05-11 16:45:02,040 - vibe_check_actor_registry - WARNING - Actor file_actor_2 already registered, updating
2025-05-11 16:45:02,040 - vibe_check_actor_registry - INFO - Registered actor file_actor_2 of type file
2025-05-11 16:45:02,041 - vibe_check_actor_system - DEBUG - Actor file_actor_2 has no _initialize method
2025-05-11 16:45:02,041 - vibe_check_actor_initializer - INFO - Actor file_actor_2 state changed: INITIALIZING -> INITIALIZED
2025-05-11 16:45:02,041 - vibe_check_actor_system.file_actor_2 - INFO - State transition: INITIALIZING -> INITIALIZED
2025-05-11 16:45:02,041 - vibe_check_actor_system.file_actor_2 - DEBUG - State transition details: {'phase': 'initialize'}
2025-05-11 16:45:02,041 - vibe_check_actor_system.file_actor_2 - DEBUG - Operation 'state_transition_INITIALIZING_to_INITIALIZED' took 0.000208 seconds
2025-05-11 16:45:02,041 - vibe_check_actor_system.initializer - DEBUG - Component state: {'actor_id': 'file_actor_2', 'state': 'INITIALIZED', 'previous_state': 'INITIALIZING', 'timestamp': 1746974702.041304, 'details': {'phase': 'initialize'}}
2025-05-11 16:45:02,041 - vibe_check_synchronization - DEBUG - Actor file_actor_2 reached synchronization point initialization_complete
2025-05-11 16:45:02,041 - vibe_check_actor_system - INFO - Actor file_actor_2 initialized successfully
2025-05-11 16:45:02,041 - vibe_check_actor_system.actor.file_actor_2 - ERROR - Error during state transition to FAILED: Actor file_actor_2: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds
2025-05-11 16:45:02,042 - vibe_check_actor_system.actor.file_actor_2 - ERROR - Traceback:
NoneType: None

2025-05-11 16:45:02,042 - vibe_check_actor_initializer - INFO - Actor file_actor_2 state changed: INITIALIZED -> FAILED
2025-05-11 16:45:02,042 - vibe_check_actor_system.file_actor_2 - INFO - State transition: INITIALIZED -> FAILED
2025-05-11 16:45:02,042 - vibe_check_actor_system.file_actor_2 - DEBUG - State transition details: {'phase': 'initialize'}
2025-05-11 16:45:02,042 - vibe_check_actor_system.file_actor_2 - DEBUG - Operation 'state_transition_INITIALIZED_to_FAILED' took 0.000544 seconds
2025-05-11 16:45:02,042 - vibe_check_actor_system.initializer - DEBUG - Component state: {'actor_id': 'file_actor_2', 'state': 'FAILED', 'previous_state': 'INITIALIZED', 'timestamp': 1746974702.041722, 'details': {'phase': 'initialize'}}
2025-05-11 16:45:02,042 - vibe_check_actor_initializer - DEBUG - Cleaned up resources for actor file_actor_2
2025-05-11 16:45:02,042 - vibe_check_actor_system - INFO - Cleaning up resources for actor file_actor_2
2025-05-11 16:45:02,042 - vibe_check_actor_system - INFO - Cleared mailbox for actor file_actor_2
2025-05-11 16:45:02,042 - vibe_check_actor_registry - INFO - Unregistered actor file_actor_2
2025-05-11 16:45:02,042 - vibe_check_actor_system - INFO - Unregistered actor file_actor_2 from registry during cleanup
2025-05-11 16:45:02,042 - vibe_check_actor_initialization - INFO - Actor file_actor_2 state changed: initialized -> stopped
2025-05-11 16:45:02,042 - vibe_check_actor_system - INFO - Set actor file_actor_2 state to STOPPED in initialization manager
2025-05-11 16:45:02,042 - vibe_check_actor_initializer - ERROR - Invalid state transition for actor file_actor_2: initialized -> stopped
2025-05-11 16:45:02,042 - vibe_check_actor_initializer - DEBUG - Valid transitions from initialized are: ['stopping', 'starting', 'rollback', 'failed']
2025-05-11 16:45:02,042 - vibe_check_actor_initializer - WARNING - Forcing transition to stopped state for actor file_actor_2
2025-05-11 16:45:02,042 - vibe_check_actor_initializer - INFO - Actor file_actor_2 state changed: initialized -> stopped
2025-05-11 16:45:02,042 - vibe_check_actor_system - INFO - Set actor file_actor_2 state to STOPPED in legacy initializer
2025-05-11 16:45:02,043 - vibe_check_actor_system - INFO - Cleaned up resources for actor file_actor_2
2025-05-11 16:45:02,043 - vibe_check_actor_system - ERROR - Error in initialization process for actor file_actor_2: Actor file_actor_2: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds
2025-05-11 16:45:02,043 - vibe_check_actor_initializer - ERROR - Actor file_actor_2: Invalid state transition: FAILED -> FAILED
2025-05-11 16:45:02,043 - vibe_check_actor_system - ERROR - Error rolling back actor file_actor_2: Actor file_actor_2: Invalid state transition: FAILED -> FAILED (state: FAILED)
2025-05-11 16:45:02,043 - vibe_check_actor_system - INFO - Cleaning up resources for actor file_actor_2
2025-05-11 16:45:02,043 - vibe_check_actor_system - INFO - Cleared mailbox for actor file_actor_2
2025-05-11 16:45:02,043 - vibe_check_actor_registry - WARNING - Actor file_actor_2 not registered, cannot unregister
2025-05-11 16:45:02,043 - vibe_check_actor_system - INFO - Unregistered actor file_actor_2 from registry during cleanup
2025-05-11 16:45:02,043 - vibe_check_actor_initialization - DEBUG - Actor file_actor_2 is already in state stopped, no change needed
2025-05-11 16:45:02,043 - vibe_check_actor_system - INFO - Set actor file_actor_2 state to STOPPED in initialization manager
2025-05-11 16:45:02,043 - vibe_check_actor_initializer - DEBUG - Actor file_actor_2 is already in state stopped, no change needed
2025-05-11 16:45:02,043 - vibe_check_actor_initializer - INFO - Actor file_actor_2 state changed: stopped -> stopped
2025-05-11 16:45:02,043 - vibe_check_actor_system - INFO - Set actor file_actor_2 state to STOPPED in legacy initializer
2025-05-11 16:45:02,043 - vibe_check_actor_system - INFO - Cleaned up resources for actor file_actor_2
2025-05-11 16:45:02,043 - vibe_check_actor_initializer - ERROR - [ACTOR INIT] Failed to initialize actor file_actor_2: Actor file_actor_2: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds
2025-05-11 16:45:02,044 - vibe_check_actor_initializer - ERROR - [ACTOR INIT] Traceback for actor file_actor_2:
Traceback (most recent call last):
  File "/Users/<USER>/Lokalne_Kody/PAT_project_analysis/vibe_check/core/actor_system/actor_initializer.py", line 936, in initialize_actor
    await asyncio.wait_for(actor.initialize(), timeout)
  File "/Users/<USER>/.pyenv/versions/3.13.3/lib/python3.13/asyncio/tasks.py", line 507, in wait_for
    return await fut
           ^^^^^^^^^
  File "/Users/<USER>/Lokalne_Kody/PAT_project_analysis/vibe_check/core/actor_system/actor.py", line 1109, in initialize
    raise timeout_error
vibe_check.core.actor_system.exceptions.ActorTimeoutError: Actor file_actor_2: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds

2025-05-11 16:45:02,044 - vibe_check_actor_initializer - DEBUG - [ACTOR INIT] Setting actor file_actor_2 state to FAILED
2025-05-11 16:45:02,044 - vibe_check_actor_initializer - ERROR - Invalid state transition for actor file_actor_2: stopped -> failed
2025-05-11 16:45:02,044 - vibe_check_actor_initializer - DEBUG - Valid transitions from stopped are: ['initializing', 'created']
2025-05-11 16:45:02,044 - vibe_check_actor_initializer - WARNING - Forcing transition to failed state for actor file_actor_2
2025-05-11 16:45:02,044 - vibe_check_actor_initializer - INFO - Actor file_actor_2 state changed: stopped -> failed
2025-05-11 16:45:02,045 - vibe_check_actor_initializer - ERROR - Actor file_actor_2 failed: Actor file_actor_2 failed during initialize phase (state: stopped): Actor file_actor_2: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds
Original error: Actor file_actor_2: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds
2025-05-11 16:45:02,045 - vibe_check_actor_lifecycle_manager - WARNING - Failed to initialize actor file_actor_2 (attempt 1/3): Actor file_actor_2: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds
2025-05-11 16:45:02,045 - vibe_check_initialization_tracker - INFO - Actor file_actor_2 initialization_start
2025-05-11 16:45:02,045 - vibe_check_initialization_tracker - INFO - Actor file_actor_2 initialization_start
2025-05-11 16:45:02,045 - vibe_check_initialization_tracker - INFO - Actor file_actor_2 initialization_complete
2025-05-11 16:45:02,045 - vibe_check_initialization_tracker - INFO - Actor file_actor_2 initialization_complete
2025-05-11 16:45:02,045 - vibe_check_initialization_tracker - ERROR - Actor file_actor_2 failed: Actor file_actor_2: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds
2025-05-11 16:45:02,146 - vibe_check_actor_lifecycle_manager - INFO - Initializing actor file_actor_2 (type: file)
2025-05-11 16:45:02,147 - vibe_check_actor_initializer - INFO - [ACTOR INIT] Starting initialization of actor file_actor_2
2025-05-11 16:45:02,147 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting registry for actor file_actor_2
2025-05-11 16:45:02,147 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting actor file_actor_2 from registry
2025-05-11 16:45:02,148 - vibe_check_actor_initializer - WARNING - [ACTOR REGISTRY] Actor file_actor_2 not found in registry
2025-05-11 16:45:02,148 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Available actors in registry: ['file_actor_3', 'file_actor_4', 'file_actor_5', 'file_actor_6', 'file_actor_7', 'file_actor_8', 'file_actor_9', 'file_actor_10', 'file_actor_11', 'file_actor_12', 'file_actor_13', 'project_actor']
2025-05-11 16:45:02,148 - vibe_check_actor_initializer - WARNING - [ACTOR INIT] Actor file_actor_2 not found in registry, retrying (1/3)
2025-05-11 16:45:02,249 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting registry for actor file_actor_2
2025-05-11 16:45:02,249 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting actor file_actor_2 from registry
2025-05-11 16:45:02,249 - vibe_check_actor_initializer - WARNING - [ACTOR REGISTRY] Actor file_actor_2 not found in registry
2025-05-11 16:45:02,249 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Available actors in registry: ['file_actor_3', 'file_actor_4', 'file_actor_5', 'file_actor_6', 'file_actor_7', 'file_actor_8', 'file_actor_9', 'file_actor_10', 'file_actor_11', 'file_actor_12', 'file_actor_13', 'project_actor']
2025-05-11 16:45:02,249 - vibe_check_actor_initializer - WARNING - [ACTOR INIT] Actor file_actor_2 not found in registry, retrying (2/3)
2025-05-11 16:45:02,450 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting registry for actor file_actor_2
2025-05-11 16:45:02,450 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting actor file_actor_2 from registry
2025-05-11 16:45:02,450 - vibe_check_actor_initializer - WARNING - [ACTOR REGISTRY] Actor file_actor_2 not found in registry
2025-05-11 16:45:02,450 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Available actors in registry: ['file_actor_3', 'file_actor_4', 'file_actor_5', 'file_actor_6', 'file_actor_7', 'file_actor_8', 'file_actor_9', 'file_actor_10', 'file_actor_11', 'file_actor_12', 'file_actor_13', 'project_actor']
2025-05-11 16:45:02,450 - vibe_check_actor_initializer - ERROR - [ACTOR INIT] Actor file_actor_2 not found in registry after 3 attempts
2025-05-11 16:45:02,450 - vibe_check_actor_lifecycle_manager - WARNING - Failed to initialize actor file_actor_2 (attempt 2/3): Actor file_actor_2 not found in registry after 3 attempts
2025-05-11 16:45:02,651 - vibe_check_actor_lifecycle_manager - INFO - Initializing actor file_actor_2 (type: file)
2025-05-11 16:45:02,651 - vibe_check_actor_initializer - INFO - [ACTOR INIT] Starting initialization of actor file_actor_2
2025-05-11 16:45:02,651 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting registry for actor file_actor_2
2025-05-11 16:45:02,651 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting actor file_actor_2 from registry
2025-05-11 16:45:02,651 - vibe_check_actor_initializer - WARNING - [ACTOR REGISTRY] Actor file_actor_2 not found in registry
2025-05-11 16:45:02,651 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Available actors in registry: ['file_actor_3', 'file_actor_4', 'file_actor_5', 'file_actor_6', 'file_actor_7', 'file_actor_8', 'file_actor_9', 'file_actor_10', 'file_actor_11', 'file_actor_12', 'file_actor_13', 'project_actor']
2025-05-11 16:45:02,651 - vibe_check_actor_initializer - WARNING - [ACTOR INIT] Actor file_actor_2 not found in registry, retrying (1/3)
2025-05-11 16:45:02,753 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting registry for actor file_actor_2
2025-05-11 16:45:02,753 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting actor file_actor_2 from registry
2025-05-11 16:45:02,753 - vibe_check_actor_initializer - WARNING - [ACTOR REGISTRY] Actor file_actor_2 not found in registry
2025-05-11 16:45:02,753 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Available actors in registry: ['file_actor_3', 'file_actor_4', 'file_actor_5', 'file_actor_6', 'file_actor_7', 'file_actor_8', 'file_actor_9', 'file_actor_10', 'file_actor_11', 'file_actor_12', 'file_actor_13', 'project_actor']
2025-05-11 16:45:02,753 - vibe_check_actor_initializer - WARNING - [ACTOR INIT] Actor file_actor_2 not found in registry, retrying (2/3)
2025-05-11 16:45:02,954 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting registry for actor file_actor_2
2025-05-11 16:45:02,954 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting actor file_actor_2 from registry
2025-05-11 16:45:02,954 - vibe_check_actor_initializer - WARNING - [ACTOR REGISTRY] Actor file_actor_2 not found in registry
2025-05-11 16:45:02,954 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Available actors in registry: ['file_actor_3', 'file_actor_4', 'file_actor_5', 'file_actor_6', 'file_actor_7', 'file_actor_8', 'file_actor_9', 'file_actor_10', 'file_actor_11', 'file_actor_12', 'file_actor_13', 'project_actor']
2025-05-11 16:45:02,954 - vibe_check_actor_initializer - ERROR - [ACTOR INIT] Actor file_actor_2 not found in registry after 3 attempts
2025-05-11 16:45:02,954 - vibe_check_actor_lifecycle_manager - ERROR - Failed to initialize actor file_actor_2 after 3 attempts: Actor file_actor_2 not found in registry after 3 attempts
2025-05-11 16:45:02,955 - vibe_check_actor_lifecycle_manager - ERROR - Traceback (most recent call last):
  File "/Users/<USER>/Lokalne_Kody/PAT_project_analysis/vibe_check/core/orchestration/actor_lifecycle_manager.py", line 174, in _initialize_all_actors
    await initializer.initialize_actor(actor_id)
  File "/Users/<USER>/Lokalne_Kody/PAT_project_analysis/vibe_check/core/actor_system/actor_initializer.py", line 883, in initialize_actor
    raise ValueError(error_msg)
ValueError: Actor file_actor_2 not found in registry after 3 attempts

2025-05-11 16:45:02,955 - vibe_check_actor_lifecycle_manager - INFO - Initializing actor file_actor_3 (type: file)
2025-05-11 16:45:02,955 - vibe_check_actor_initializer - INFO - [ACTOR INIT] Starting initialization of actor file_actor_3
2025-05-11 16:45:02,955 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting registry for actor file_actor_3
2025-05-11 16:45:02,955 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting actor file_actor_3 from registry
2025-05-11 16:45:02,955 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Successfully retrieved actor file_actor_3 from registry
2025-05-11 16:45:02,955 - vibe_check_actor_initializer - INFO - [ACTOR INIT] Actor file_actor_3 is of type file
2025-05-11 16:45:02,955 - vibe_check_actor_initializer - DEBUG - [ACTOR INIT] Setting actor file_actor_3 state to INITIALIZING
2025-05-11 16:45:02,955 - vibe_check_actor_initializer - INFO - Actor file_actor_3 state changed: created -> initializing
2025-05-11 16:45:02,955 - vibe_check_actor_initializer - DEBUG - [ACTOR INIT] Calling initialize() method on actor file_actor_3
2025-05-11 16:45:02,955 - vibe_check_actor_system - INFO - Actor file_actor_3 is already registered with initialization manager
2025-05-11 16:45:02,955 - vibe_check_actor_system - INFO - Actor file_actor_3 is already registered with legacy initializer
2025-05-11 16:45:02,955 - vibe_check_actor_initialization - INFO - Actor file_actor_3 state changed: created -> initializing
2025-05-11 16:45:02,955 - vibe_check_actor_initializer - DEBUG - Actor file_actor_3 is already in state initializing, no change needed
2025-05-11 16:45:02,955 - vibe_check_initialization_tracker - INFO - Actor file_actor_3 initialization_start
2025-05-11 16:45:02,955 - vibe_check_actor_system - DEBUG - Actor file_actor_3 has no _initialize method
2025-05-11 16:45:02,955 - vibe_check_actor_initialization - INFO - Actor file_actor_3 state changed: initializing -> initialized
2025-05-11 16:45:02,955 - vibe_check_actor_initializer - INFO - Actor file_actor_3 state changed: initializing -> initialized
2025-05-11 16:45:02,955 - vibe_check_initialization_tracker - INFO - Actor file_actor_3 initialization_complete
2025-05-11 16:45:02,955 - vibe_check_actor_system - INFO - Actor file_actor_3 initialized successfully
2025-05-11 16:45:02,955 - vibe_check_actor_system - INFO - Actor file_actor_3 is already registered with initialization manager
2025-05-11 16:45:02,955 - vibe_check_actor_system - INFO - Actor file_actor_3 is already registered with legacy initializer
2025-05-11 16:45:02,955 - vibe_check_actor_initializer - INFO - Actor file_actor_3 state changed: REGISTERED -> INITIALIZING
2025-05-11 16:45:02,955 - vibe_check_actor_system.file_actor_3 - INFO - State transition: REGISTERED -> INITIALIZING
2025-05-11 16:45:02,955 - vibe_check_actor_system.file_actor_3 - DEBUG - State transition details: {'phase': 'initialize'}
2025-05-11 16:45:02,955 - vibe_check_actor_system.file_actor_3 - DEBUG - Operation 'state_transition_REGISTERED_to_INITIALIZING' took 0.000061 seconds
2025-05-11 16:45:02,955 - vibe_check_actor_system.initializer - DEBUG - Component state: {'actor_id': 'file_actor_3', 'state': 'INITIALIZING', 'previous_state': 'REGISTERED', 'timestamp': 1746974702.955741, 'details': {'phase': 'initialize'}}
2025-05-11 16:45:02,955 - vibe_check_actor_registry - WARNING - Actor file_actor_3 already registered, updating
2025-05-11 16:45:02,955 - vibe_check_actor_registry - INFO - Registered actor file_actor_3 of type file
2025-05-11 16:45:02,955 - vibe_check_actor_system - DEBUG - Actor file_actor_3 has no _initialize method
2025-05-11 16:45:02,955 - vibe_check_actor_initializer - INFO - Actor file_actor_3 state changed: INITIALIZING -> INITIALIZED
2025-05-11 16:45:02,955 - vibe_check_actor_system.file_actor_3 - INFO - State transition: INITIALIZING -> INITIALIZED
2025-05-11 16:45:02,955 - vibe_check_actor_system.file_actor_3 - DEBUG - State transition details: {'phase': 'initialize'}
2025-05-11 16:45:02,955 - vibe_check_actor_system.file_actor_3 - DEBUG - Operation 'state_transition_INITIALIZING_to_INITIALIZED' took 0.000044 seconds
2025-05-11 16:45:02,955 - vibe_check_actor_system.initializer - DEBUG - Component state: {'actor_id': 'file_actor_3', 'state': 'INITIALIZED', 'previous_state': 'INITIALIZING', 'timestamp': 1746974702.95591, 'details': {'phase': 'initialize'}}
2025-05-11 16:45:02,955 - vibe_check_synchronization - DEBUG - Actor file_actor_3 reached synchronization point initialization_complete
2025-05-11 16:45:02,956 - vibe_check_actor_system - INFO - Actor file_actor_3 initialized successfully
2025-05-11 16:45:02,956 - vibe_check_actor_system.actor.file_actor_3 - ERROR - Error during state transition to FAILED: Actor file_actor_3: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds
2025-05-11 16:45:02,956 - vibe_check_actor_system.actor.file_actor_3 - ERROR - Traceback:
NoneType: None

2025-05-11 16:45:02,956 - vibe_check_actor_initializer - INFO - Actor file_actor_3 state changed: INITIALIZED -> FAILED
2025-05-11 16:45:02,956 - vibe_check_actor_system.file_actor_3 - INFO - State transition: INITIALIZED -> FAILED
2025-05-11 16:45:02,956 - vibe_check_actor_system.file_actor_3 - DEBUG - State transition details: {'phase': 'initialize'}
2025-05-11 16:45:02,956 - vibe_check_actor_system.file_actor_3 - DEBUG - Operation 'state_transition_INITIALIZED_to_FAILED' took 0.000126 seconds
2025-05-11 16:45:02,956 - vibe_check_actor_system.initializer - DEBUG - Component state: {'actor_id': 'file_actor_3', 'state': 'FAILED', 'previous_state': 'INITIALIZED', 'timestamp': 1746974702.9560409, 'details': {'phase': 'initialize'}}
2025-05-11 16:45:02,956 - vibe_check_actor_initializer - DEBUG - Cleaned up resources for actor file_actor_3
2025-05-11 16:45:02,956 - vibe_check_actor_system - INFO - Cleaning up resources for actor file_actor_3
2025-05-11 16:45:02,956 - vibe_check_actor_system - INFO - Cleared mailbox for actor file_actor_3
2025-05-11 16:45:02,956 - vibe_check_actor_registry - INFO - Unregistered actor file_actor_3
2025-05-11 16:45:02,956 - vibe_check_actor_system - INFO - Unregistered actor file_actor_3 from registry during cleanup
2025-05-11 16:45:02,956 - vibe_check_actor_initialization - INFO - Actor file_actor_3 state changed: initialized -> stopped
2025-05-11 16:45:02,956 - vibe_check_actor_system - INFO - Set actor file_actor_3 state to STOPPED in initialization manager
2025-05-11 16:45:02,956 - vibe_check_actor_initializer - ERROR - Invalid state transition for actor file_actor_3: initialized -> stopped
2025-05-11 16:45:02,956 - vibe_check_actor_initializer - DEBUG - Valid transitions from initialized are: ['stopping', 'starting', 'rollback', 'failed']
2025-05-11 16:45:02,956 - vibe_check_actor_initializer - WARNING - Forcing transition to stopped state for actor file_actor_3
2025-05-11 16:45:02,956 - vibe_check_actor_initializer - INFO - Actor file_actor_3 state changed: initialized -> stopped
2025-05-11 16:45:02,956 - vibe_check_actor_system - INFO - Set actor file_actor_3 state to STOPPED in legacy initializer
2025-05-11 16:45:02,956 - vibe_check_actor_system - INFO - Cleaned up resources for actor file_actor_3
2025-05-11 16:45:02,956 - vibe_check_actor_system - ERROR - Error in initialization process for actor file_actor_3: Actor file_actor_3: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds
2025-05-11 16:45:02,956 - vibe_check_actor_initializer - ERROR - Actor file_actor_3: Invalid state transition: FAILED -> FAILED
2025-05-11 16:45:02,956 - vibe_check_actor_system - ERROR - Error rolling back actor file_actor_3: Actor file_actor_3: Invalid state transition: FAILED -> FAILED (state: FAILED)
2025-05-11 16:45:02,956 - vibe_check_actor_system - INFO - Cleaning up resources for actor file_actor_3
2025-05-11 16:45:02,956 - vibe_check_actor_system - INFO - Cleared mailbox for actor file_actor_3
2025-05-11 16:45:02,956 - vibe_check_actor_registry - WARNING - Actor file_actor_3 not registered, cannot unregister
2025-05-11 16:45:02,956 - vibe_check_actor_system - INFO - Unregistered actor file_actor_3 from registry during cleanup
2025-05-11 16:45:02,956 - vibe_check_actor_initialization - DEBUG - Actor file_actor_3 is already in state stopped, no change needed
2025-05-11 16:45:02,956 - vibe_check_actor_system - INFO - Set actor file_actor_3 state to STOPPED in initialization manager
2025-05-11 16:45:02,956 - vibe_check_actor_initializer - DEBUG - Actor file_actor_3 is already in state stopped, no change needed
2025-05-11 16:45:02,956 - vibe_check_actor_initializer - INFO - Actor file_actor_3 state changed: stopped -> stopped
2025-05-11 16:45:02,956 - vibe_check_actor_system - INFO - Set actor file_actor_3 state to STOPPED in legacy initializer
2025-05-11 16:45:02,956 - vibe_check_actor_system - INFO - Cleaned up resources for actor file_actor_3
2025-05-11 16:45:02,956 - vibe_check_actor_initializer - ERROR - [ACTOR INIT] Failed to initialize actor file_actor_3: Actor file_actor_3: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds
2025-05-11 16:45:02,957 - vibe_check_actor_initializer - ERROR - [ACTOR INIT] Traceback for actor file_actor_3:
Traceback (most recent call last):
  File "/Users/<USER>/Lokalne_Kody/PAT_project_analysis/vibe_check/core/actor_system/actor_initializer.py", line 936, in initialize_actor
    await asyncio.wait_for(actor.initialize(), timeout)
  File "/Users/<USER>/.pyenv/versions/3.13.3/lib/python3.13/asyncio/tasks.py", line 507, in wait_for
    return await fut
           ^^^^^^^^^
  File "/Users/<USER>/Lokalne_Kody/PAT_project_analysis/vibe_check/core/actor_system/actor.py", line 1109, in initialize
    raise timeout_error
vibe_check.core.actor_system.exceptions.ActorTimeoutError: Actor file_actor_3: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds

2025-05-11 16:45:02,957 - vibe_check_actor_initializer - DEBUG - [ACTOR INIT] Setting actor file_actor_3 state to FAILED
2025-05-11 16:45:02,957 - vibe_check_actor_initializer - ERROR - Invalid state transition for actor file_actor_3: stopped -> failed
2025-05-11 16:45:02,957 - vibe_check_actor_initializer - DEBUG - Valid transitions from stopped are: ['initializing', 'created']
2025-05-11 16:45:02,957 - vibe_check_actor_initializer - WARNING - Forcing transition to failed state for actor file_actor_3
2025-05-11 16:45:02,957 - vibe_check_actor_initializer - INFO - Actor file_actor_3 state changed: stopped -> failed
2025-05-11 16:45:02,957 - vibe_check_actor_initializer - ERROR - Actor file_actor_3 failed: Actor file_actor_3 failed during initialize phase (state: stopped): Actor file_actor_3: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds
Original error: Actor file_actor_3: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds
2025-05-11 16:45:02,957 - vibe_check_actor_lifecycle_manager - WARNING - Failed to initialize actor file_actor_3 (attempt 1/3): Actor file_actor_3: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds
2025-05-11 16:45:02,957 - vibe_check_initialization_tracker - INFO - Actor file_actor_3 initialization_start
2025-05-11 16:45:02,957 - vibe_check_initialization_tracker - INFO - Actor file_actor_3 initialization_start
2025-05-11 16:45:02,957 - vibe_check_initialization_tracker - INFO - Actor file_actor_3 initialization_complete
2025-05-11 16:45:02,957 - vibe_check_initialization_tracker - INFO - Actor file_actor_3 initialization_complete
2025-05-11 16:45:02,957 - vibe_check_initialization_tracker - ERROR - Actor file_actor_3 failed: Actor file_actor_3: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds
2025-05-11 16:45:03,058 - vibe_check_actor_lifecycle_manager - INFO - Initializing actor file_actor_3 (type: file)
2025-05-11 16:45:03,058 - vibe_check_actor_initializer - INFO - [ACTOR INIT] Starting initialization of actor file_actor_3
2025-05-11 16:45:03,058 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting registry for actor file_actor_3
2025-05-11 16:45:03,058 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting actor file_actor_3 from registry
2025-05-11 16:45:03,058 - vibe_check_actor_initializer - WARNING - [ACTOR REGISTRY] Actor file_actor_3 not found in registry
2025-05-11 16:45:03,058 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Available actors in registry: ['file_actor_4', 'file_actor_5', 'file_actor_6', 'file_actor_7', 'file_actor_8', 'file_actor_9', 'file_actor_10', 'file_actor_11', 'file_actor_12', 'file_actor_13', 'project_actor']
2025-05-11 16:45:03,058 - vibe_check_actor_initializer - WARNING - [ACTOR INIT] Actor file_actor_3 not found in registry, retrying (1/3)
2025-05-11 16:45:03,160 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting registry for actor file_actor_3
2025-05-11 16:45:03,160 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting actor file_actor_3 from registry
2025-05-11 16:45:03,160 - vibe_check_actor_initializer - WARNING - [ACTOR REGISTRY] Actor file_actor_3 not found in registry
2025-05-11 16:45:03,160 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Available actors in registry: ['file_actor_4', 'file_actor_5', 'file_actor_6', 'file_actor_7', 'file_actor_8', 'file_actor_9', 'file_actor_10', 'file_actor_11', 'file_actor_12', 'file_actor_13', 'project_actor']
2025-05-11 16:45:03,160 - vibe_check_actor_initializer - WARNING - [ACTOR INIT] Actor file_actor_3 not found in registry, retrying (2/3)
2025-05-11 16:45:03,361 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting registry for actor file_actor_3
2025-05-11 16:45:03,361 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting actor file_actor_3 from registry
2025-05-11 16:45:03,361 - vibe_check_actor_initializer - WARNING - [ACTOR REGISTRY] Actor file_actor_3 not found in registry
2025-05-11 16:45:03,361 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Available actors in registry: ['file_actor_4', 'file_actor_5', 'file_actor_6', 'file_actor_7', 'file_actor_8', 'file_actor_9', 'file_actor_10', 'file_actor_11', 'file_actor_12', 'file_actor_13', 'project_actor']
2025-05-11 16:45:03,361 - vibe_check_actor_initializer - ERROR - [ACTOR INIT] Actor file_actor_3 not found in registry after 3 attempts
2025-05-11 16:45:03,361 - vibe_check_actor_lifecycle_manager - WARNING - Failed to initialize actor file_actor_3 (attempt 2/3): Actor file_actor_3 not found in registry after 3 attempts
2025-05-11 16:45:03,562 - vibe_check_actor_lifecycle_manager - INFO - Initializing actor file_actor_3 (type: file)
2025-05-11 16:45:03,562 - vibe_check_actor_initializer - INFO - [ACTOR INIT] Starting initialization of actor file_actor_3
2025-05-11 16:45:03,563 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting registry for actor file_actor_3
2025-05-11 16:45:03,563 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting actor file_actor_3 from registry
2025-05-11 16:45:03,563 - vibe_check_actor_initializer - WARNING - [ACTOR REGISTRY] Actor file_actor_3 not found in registry
2025-05-11 16:45:03,563 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Available actors in registry: ['file_actor_4', 'file_actor_5', 'file_actor_6', 'file_actor_7', 'file_actor_8', 'file_actor_9', 'file_actor_10', 'file_actor_11', 'file_actor_12', 'file_actor_13', 'project_actor']
2025-05-11 16:45:03,563 - vibe_check_actor_initializer - WARNING - [ACTOR INIT] Actor file_actor_3 not found in registry, retrying (1/3)
2025-05-11 16:45:03,664 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting registry for actor file_actor_3
2025-05-11 16:45:03,664 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting actor file_actor_3 from registry
2025-05-11 16:45:03,664 - vibe_check_actor_initializer - WARNING - [ACTOR REGISTRY] Actor file_actor_3 not found in registry
2025-05-11 16:45:03,664 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Available actors in registry: ['file_actor_4', 'file_actor_5', 'file_actor_6', 'file_actor_7', 'file_actor_8', 'file_actor_9', 'file_actor_10', 'file_actor_11', 'file_actor_12', 'file_actor_13', 'project_actor']
2025-05-11 16:45:03,664 - vibe_check_actor_initializer - WARNING - [ACTOR INIT] Actor file_actor_3 not found in registry, retrying (2/3)
2025-05-11 16:45:03,865 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting registry for actor file_actor_3
2025-05-11 16:45:03,865 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting actor file_actor_3 from registry
2025-05-11 16:45:03,866 - vibe_check_actor_initializer - WARNING - [ACTOR REGISTRY] Actor file_actor_3 not found in registry
2025-05-11 16:45:03,866 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Available actors in registry: ['file_actor_4', 'file_actor_5', 'file_actor_6', 'file_actor_7', 'file_actor_8', 'file_actor_9', 'file_actor_10', 'file_actor_11', 'file_actor_12', 'file_actor_13', 'project_actor']
2025-05-11 16:45:03,866 - vibe_check_actor_initializer - ERROR - [ACTOR INIT] Actor file_actor_3 not found in registry after 3 attempts
2025-05-11 16:45:03,866 - vibe_check_actor_lifecycle_manager - ERROR - Failed to initialize actor file_actor_3 after 3 attempts: Actor file_actor_3 not found in registry after 3 attempts
2025-05-11 16:45:03,866 - vibe_check_actor_lifecycle_manager - ERROR - Traceback (most recent call last):
  File "/Users/<USER>/Lokalne_Kody/PAT_project_analysis/vibe_check/core/orchestration/actor_lifecycle_manager.py", line 174, in _initialize_all_actors
    await initializer.initialize_actor(actor_id)
  File "/Users/<USER>/Lokalne_Kody/PAT_project_analysis/vibe_check/core/actor_system/actor_initializer.py", line 883, in initialize_actor
    raise ValueError(error_msg)
ValueError: Actor file_actor_3 not found in registry after 3 attempts

2025-05-11 16:45:03,866 - vibe_check_actor_lifecycle_manager - INFO - Initializing actor file_actor_4 (type: file)
2025-05-11 16:45:03,866 - vibe_check_actor_initializer - INFO - [ACTOR INIT] Starting initialization of actor file_actor_4
2025-05-11 16:45:03,866 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting registry for actor file_actor_4
2025-05-11 16:45:03,866 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting actor file_actor_4 from registry
2025-05-11 16:45:03,866 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Successfully retrieved actor file_actor_4 from registry
2025-05-11 16:45:03,866 - vibe_check_actor_initializer - INFO - [ACTOR INIT] Actor file_actor_4 is of type file
2025-05-11 16:45:03,866 - vibe_check_actor_initializer - DEBUG - [ACTOR INIT] Setting actor file_actor_4 state to INITIALIZING
2025-05-11 16:45:03,866 - vibe_check_actor_initializer - INFO - Actor file_actor_4 state changed: created -> initializing
2025-05-11 16:45:03,866 - vibe_check_actor_initializer - DEBUG - [ACTOR INIT] Calling initialize() method on actor file_actor_4
2025-05-11 16:45:03,866 - vibe_check_actor_system - INFO - Actor file_actor_4 is already registered with initialization manager
2025-05-11 16:45:03,866 - vibe_check_actor_system - INFO - Actor file_actor_4 is already registered with legacy initializer
2025-05-11 16:45:03,866 - vibe_check_actor_initialization - INFO - Actor file_actor_4 state changed: created -> initializing
2025-05-11 16:45:03,866 - vibe_check_actor_initializer - DEBUG - Actor file_actor_4 is already in state initializing, no change needed
2025-05-11 16:45:03,866 - vibe_check_initialization_tracker - INFO - Actor file_actor_4 initialization_start
2025-05-11 16:45:03,866 - vibe_check_actor_system - DEBUG - Actor file_actor_4 has no _initialize method
2025-05-11 16:45:03,866 - vibe_check_actor_initialization - INFO - Actor file_actor_4 state changed: initializing -> initialized
2025-05-11 16:45:03,866 - vibe_check_actor_initializer - INFO - Actor file_actor_4 state changed: initializing -> initialized
2025-05-11 16:45:03,866 - vibe_check_initialization_tracker - INFO - Actor file_actor_4 initialization_complete
2025-05-11 16:45:03,866 - vibe_check_actor_system - INFO - Actor file_actor_4 initialized successfully
2025-05-11 16:45:03,866 - vibe_check_actor_system - INFO - Actor file_actor_4 is already registered with initialization manager
2025-05-11 16:45:03,866 - vibe_check_actor_system - INFO - Actor file_actor_4 is already registered with legacy initializer
2025-05-11 16:45:03,866 - vibe_check_actor_initializer - INFO - Actor file_actor_4 state changed: REGISTERED -> INITIALIZING
2025-05-11 16:45:03,866 - vibe_check_actor_system.file_actor_4 - INFO - State transition: REGISTERED -> INITIALIZING
2025-05-11 16:45:03,866 - vibe_check_actor_system.file_actor_4 - DEBUG - State transition details: {'phase': 'initialize'}
2025-05-11 16:45:03,866 - vibe_check_actor_system.file_actor_4 - DEBUG - Operation 'state_transition_REGISTERED_to_INITIALIZING' took 0.000056 seconds
2025-05-11 16:45:03,866 - vibe_check_actor_system.initializer - DEBUG - Component state: {'actor_id': 'file_actor_4', 'state': 'INITIALIZING', 'previous_state': 'REGISTERED', 'timestamp': 1746974703.8668668, 'details': {'phase': 'initialize'}}
2025-05-11 16:45:03,866 - vibe_check_actor_registry - WARNING - Actor file_actor_4 already registered, updating
2025-05-11 16:45:03,866 - vibe_check_actor_registry - INFO - Registered actor file_actor_4 of type file
2025-05-11 16:45:03,867 - vibe_check_actor_system - DEBUG - Actor file_actor_4 has no _initialize method
2025-05-11 16:45:03,867 - vibe_check_actor_initializer - INFO - Actor file_actor_4 state changed: INITIALIZING -> INITIALIZED
2025-05-11 16:45:03,867 - vibe_check_actor_system.file_actor_4 - INFO - State transition: INITIALIZING -> INITIALIZED
2025-05-11 16:45:03,867 - vibe_check_actor_system.file_actor_4 - DEBUG - State transition details: {'phase': 'initialize'}
2025-05-11 16:45:03,867 - vibe_check_actor_system.file_actor_4 - DEBUG - Operation 'state_transition_INITIALIZING_to_INITIALIZED' took 0.000035 seconds
2025-05-11 16:45:03,867 - vibe_check_actor_system.initializer - DEBUG - Component state: {'actor_id': 'file_actor_4', 'state': 'INITIALIZED', 'previous_state': 'INITIALIZING', 'timestamp': 1746974703.867025, 'details': {'phase': 'initialize'}}
2025-05-11 16:45:03,867 - vibe_check_synchronization - DEBUG - Actor file_actor_4 reached synchronization point initialization_complete
2025-05-11 16:45:03,867 - vibe_check_actor_system - INFO - Actor file_actor_4 initialized successfully
2025-05-11 16:45:03,867 - vibe_check_actor_system.actor.file_actor_4 - ERROR - Error during state transition to FAILED: Actor file_actor_4: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds
2025-05-11 16:45:03,867 - vibe_check_actor_system.actor.file_actor_4 - ERROR - Traceback:
NoneType: None

2025-05-11 16:45:03,867 - vibe_check_actor_initializer - INFO - Actor file_actor_4 state changed: INITIALIZED -> FAILED
2025-05-11 16:45:03,867 - vibe_check_actor_system.file_actor_4 - INFO - State transition: INITIALIZED -> FAILED
2025-05-11 16:45:03,867 - vibe_check_actor_system.file_actor_4 - DEBUG - State transition details: {'phase': 'initialize'}
2025-05-11 16:45:03,867 - vibe_check_actor_system.file_actor_4 - DEBUG - Operation 'state_transition_INITIALIZED_to_FAILED' took 0.000125 seconds
2025-05-11 16:45:03,867 - vibe_check_actor_system.initializer - DEBUG - Component state: {'actor_id': 'file_actor_4', 'state': 'FAILED', 'previous_state': 'INITIALIZED', 'timestamp': 1746974703.867136, 'details': {'phase': 'initialize'}}
2025-05-11 16:45:03,867 - vibe_check_actor_initializer - DEBUG - Cleaned up resources for actor file_actor_4
2025-05-11 16:45:03,867 - vibe_check_actor_system - INFO - Cleaning up resources for actor file_actor_4
2025-05-11 16:45:03,867 - vibe_check_actor_system - INFO - Cleared mailbox for actor file_actor_4
2025-05-11 16:45:03,867 - vibe_check_actor_registry - INFO - Unregistered actor file_actor_4
2025-05-11 16:45:03,867 - vibe_check_actor_system - INFO - Unregistered actor file_actor_4 from registry during cleanup
2025-05-11 16:45:03,867 - vibe_check_actor_initialization - INFO - Actor file_actor_4 state changed: initialized -> stopped
2025-05-11 16:45:03,867 - vibe_check_actor_system - INFO - Set actor file_actor_4 state to STOPPED in initialization manager
2025-05-11 16:45:03,867 - vibe_check_actor_initializer - ERROR - Invalid state transition for actor file_actor_4: initialized -> stopped
2025-05-11 16:45:03,867 - vibe_check_actor_initializer - DEBUG - Valid transitions from initialized are: ['stopping', 'starting', 'rollback', 'failed']
2025-05-11 16:45:03,867 - vibe_check_actor_initializer - WARNING - Forcing transition to stopped state for actor file_actor_4
2025-05-11 16:45:03,867 - vibe_check_actor_initializer - INFO - Actor file_actor_4 state changed: initialized -> stopped
2025-05-11 16:45:03,867 - vibe_check_actor_system - INFO - Set actor file_actor_4 state to STOPPED in legacy initializer
2025-05-11 16:45:03,867 - vibe_check_actor_system - INFO - Cleaned up resources for actor file_actor_4
2025-05-11 16:45:03,867 - vibe_check_actor_system - ERROR - Error in initialization process for actor file_actor_4: Actor file_actor_4: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds
2025-05-11 16:45:03,867 - vibe_check_actor_initializer - ERROR - Actor file_actor_4: Invalid state transition: FAILED -> FAILED
2025-05-11 16:45:03,867 - vibe_check_actor_system - ERROR - Error rolling back actor file_actor_4: Actor file_actor_4: Invalid state transition: FAILED -> FAILED (state: FAILED)
2025-05-11 16:45:03,867 - vibe_check_actor_system - INFO - Cleaning up resources for actor file_actor_4
2025-05-11 16:45:03,867 - vibe_check_actor_system - INFO - Cleared mailbox for actor file_actor_4
2025-05-11 16:45:03,867 - vibe_check_actor_registry - WARNING - Actor file_actor_4 not registered, cannot unregister
2025-05-11 16:45:03,867 - vibe_check_actor_system - INFO - Unregistered actor file_actor_4 from registry during cleanup
2025-05-11 16:45:03,867 - vibe_check_actor_initialization - DEBUG - Actor file_actor_4 is already in state stopped, no change needed
2025-05-11 16:45:03,867 - vibe_check_actor_system - INFO - Set actor file_actor_4 state to STOPPED in initialization manager
2025-05-11 16:45:03,867 - vibe_check_actor_initializer - DEBUG - Actor file_actor_4 is already in state stopped, no change needed
2025-05-11 16:45:03,867 - vibe_check_actor_initializer - INFO - Actor file_actor_4 state changed: stopped -> stopped
2025-05-11 16:45:03,867 - vibe_check_actor_system - INFO - Set actor file_actor_4 state to STOPPED in legacy initializer
2025-05-11 16:45:03,867 - vibe_check_actor_system - INFO - Cleaned up resources for actor file_actor_4
2025-05-11 16:45:03,867 - vibe_check_actor_initializer - ERROR - [ACTOR INIT] Failed to initialize actor file_actor_4: Actor file_actor_4: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds
2025-05-11 16:45:03,867 - vibe_check_actor_initializer - ERROR - [ACTOR INIT] Traceback for actor file_actor_4:
Traceback (most recent call last):
  File "/Users/<USER>/Lokalne_Kody/PAT_project_analysis/vibe_check/core/actor_system/actor_initializer.py", line 936, in initialize_actor
    await asyncio.wait_for(actor.initialize(), timeout)
  File "/Users/<USER>/.pyenv/versions/3.13.3/lib/python3.13/asyncio/tasks.py", line 507, in wait_for
    return await fut
           ^^^^^^^^^
  File "/Users/<USER>/Lokalne_Kody/PAT_project_analysis/vibe_check/core/actor_system/actor.py", line 1109, in initialize
    raise timeout_error
vibe_check.core.actor_system.exceptions.ActorTimeoutError: Actor file_actor_4: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds

2025-05-11 16:45:03,867 - vibe_check_actor_initializer - DEBUG - [ACTOR INIT] Setting actor file_actor_4 state to FAILED
2025-05-11 16:45:03,867 - vibe_check_actor_initializer - ERROR - Invalid state transition for actor file_actor_4: stopped -> failed
2025-05-11 16:45:03,867 - vibe_check_actor_initializer - DEBUG - Valid transitions from stopped are: ['initializing', 'created']
2025-05-11 16:45:03,868 - vibe_check_actor_initializer - WARNING - Forcing transition to failed state for actor file_actor_4
2025-05-11 16:45:03,868 - vibe_check_actor_initializer - INFO - Actor file_actor_4 state changed: stopped -> failed
2025-05-11 16:45:03,868 - vibe_check_actor_initializer - ERROR - Actor file_actor_4 failed: Actor file_actor_4 failed during initialize phase (state: stopped): Actor file_actor_4: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds
Original error: Actor file_actor_4: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds
2025-05-11 16:45:03,868 - vibe_check_actor_lifecycle_manager - WARNING - Failed to initialize actor file_actor_4 (attempt 1/3): Actor file_actor_4: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds
2025-05-11 16:45:03,868 - vibe_check_initialization_tracker - INFO - Actor file_actor_4 initialization_start
2025-05-11 16:45:03,868 - vibe_check_initialization_tracker - INFO - Actor file_actor_4 initialization_start
2025-05-11 16:45:03,868 - vibe_check_initialization_tracker - INFO - Actor file_actor_4 initialization_complete
2025-05-11 16:45:03,868 - vibe_check_initialization_tracker - INFO - Actor file_actor_4 initialization_complete
2025-05-11 16:45:03,868 - vibe_check_initialization_tracker - ERROR - Actor file_actor_4 failed: Actor file_actor_4: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds
2025-05-11 16:45:03,969 - vibe_check_actor_lifecycle_manager - INFO - Initializing actor file_actor_4 (type: file)
2025-05-11 16:45:03,969 - vibe_check_actor_initializer - INFO - [ACTOR INIT] Starting initialization of actor file_actor_4
2025-05-11 16:45:03,969 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting registry for actor file_actor_4
2025-05-11 16:45:03,969 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting actor file_actor_4 from registry
2025-05-11 16:45:03,969 - vibe_check_actor_initializer - WARNING - [ACTOR REGISTRY] Actor file_actor_4 not found in registry
2025-05-11 16:45:03,969 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Available actors in registry: ['file_actor_5', 'file_actor_6', 'file_actor_7', 'file_actor_8', 'file_actor_9', 'file_actor_10', 'file_actor_11', 'file_actor_12', 'file_actor_13', 'project_actor']
2025-05-11 16:45:03,969 - vibe_check_actor_initializer - WARNING - [ACTOR INIT] Actor file_actor_4 not found in registry, retrying (1/3)
2025-05-11 16:45:04,070 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting registry for actor file_actor_4
2025-05-11 16:45:04,070 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting actor file_actor_4 from registry
2025-05-11 16:45:04,070 - vibe_check_actor_initializer - WARNING - [ACTOR REGISTRY] Actor file_actor_4 not found in registry
2025-05-11 16:45:04,070 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Available actors in registry: ['file_actor_5', 'file_actor_6', 'file_actor_7', 'file_actor_8', 'file_actor_9', 'file_actor_10', 'file_actor_11', 'file_actor_12', 'file_actor_13', 'project_actor']
2025-05-11 16:45:04,070 - vibe_check_actor_initializer - WARNING - [ACTOR INIT] Actor file_actor_4 not found in registry, retrying (2/3)
2025-05-11 16:45:04,271 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting registry for actor file_actor_4
2025-05-11 16:45:04,271 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting actor file_actor_4 from registry
2025-05-11 16:45:04,272 - vibe_check_actor_initializer - WARNING - [ACTOR REGISTRY] Actor file_actor_4 not found in registry
2025-05-11 16:45:04,272 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Available actors in registry: ['file_actor_5', 'file_actor_6', 'file_actor_7', 'file_actor_8', 'file_actor_9', 'file_actor_10', 'file_actor_11', 'file_actor_12', 'file_actor_13', 'project_actor']
2025-05-11 16:45:04,272 - vibe_check_actor_initializer - ERROR - [ACTOR INIT] Actor file_actor_4 not found in registry after 3 attempts
2025-05-11 16:45:04,272 - vibe_check_actor_lifecycle_manager - WARNING - Failed to initialize actor file_actor_4 (attempt 2/3): Actor file_actor_4 not found in registry after 3 attempts
2025-05-11 16:45:04,473 - vibe_check_actor_lifecycle_manager - INFO - Initializing actor file_actor_4 (type: file)
2025-05-11 16:45:04,473 - vibe_check_actor_initializer - INFO - [ACTOR INIT] Starting initialization of actor file_actor_4
2025-05-11 16:45:04,473 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting registry for actor file_actor_4
2025-05-11 16:45:04,473 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting actor file_actor_4 from registry
2025-05-11 16:45:04,473 - vibe_check_actor_initializer - WARNING - [ACTOR REGISTRY] Actor file_actor_4 not found in registry
2025-05-11 16:45:04,474 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Available actors in registry: ['file_actor_5', 'file_actor_6', 'file_actor_7', 'file_actor_8', 'file_actor_9', 'file_actor_10', 'file_actor_11', 'file_actor_12', 'file_actor_13', 'project_actor']
2025-05-11 16:45:04,474 - vibe_check_actor_initializer - WARNING - [ACTOR INIT] Actor file_actor_4 not found in registry, retrying (1/3)
2025-05-11 16:45:04,575 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting registry for actor file_actor_4
2025-05-11 16:45:04,575 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting actor file_actor_4 from registry
2025-05-11 16:45:04,575 - vibe_check_actor_initializer - WARNING - [ACTOR REGISTRY] Actor file_actor_4 not found in registry
2025-05-11 16:45:04,575 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Available actors in registry: ['file_actor_5', 'file_actor_6', 'file_actor_7', 'file_actor_8', 'file_actor_9', 'file_actor_10', 'file_actor_11', 'file_actor_12', 'file_actor_13', 'project_actor']
2025-05-11 16:45:04,575 - vibe_check_actor_initializer - WARNING - [ACTOR INIT] Actor file_actor_4 not found in registry, retrying (2/3)
2025-05-11 16:45:04,776 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting registry for actor file_actor_4
2025-05-11 16:45:04,776 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting actor file_actor_4 from registry
2025-05-11 16:45:04,776 - vibe_check_actor_initializer - WARNING - [ACTOR REGISTRY] Actor file_actor_4 not found in registry
2025-05-11 16:45:04,776 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Available actors in registry: ['file_actor_5', 'file_actor_6', 'file_actor_7', 'file_actor_8', 'file_actor_9', 'file_actor_10', 'file_actor_11', 'file_actor_12', 'file_actor_13', 'project_actor']
2025-05-11 16:45:04,776 - vibe_check_actor_initializer - ERROR - [ACTOR INIT] Actor file_actor_4 not found in registry after 3 attempts
2025-05-11 16:45:04,776 - vibe_check_actor_lifecycle_manager - ERROR - Failed to initialize actor file_actor_4 after 3 attempts: Actor file_actor_4 not found in registry after 3 attempts
2025-05-11 16:45:04,777 - vibe_check_actor_lifecycle_manager - ERROR - Traceback (most recent call last):
  File "/Users/<USER>/Lokalne_Kody/PAT_project_analysis/vibe_check/core/orchestration/actor_lifecycle_manager.py", line 174, in _initialize_all_actors
    await initializer.initialize_actor(actor_id)
  File "/Users/<USER>/Lokalne_Kody/PAT_project_analysis/vibe_check/core/actor_system/actor_initializer.py", line 883, in initialize_actor
    raise ValueError(error_msg)
ValueError: Actor file_actor_4 not found in registry after 3 attempts

2025-05-11 16:45:04,777 - vibe_check_actor_lifecycle_manager - INFO - Initializing actor file_actor_5 (type: file)
2025-05-11 16:45:04,777 - vibe_check_actor_initializer - INFO - [ACTOR INIT] Starting initialization of actor file_actor_5
2025-05-11 16:45:04,777 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting registry for actor file_actor_5
2025-05-11 16:45:04,777 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting actor file_actor_5 from registry
2025-05-11 16:45:04,777 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Successfully retrieved actor file_actor_5 from registry
2025-05-11 16:45:04,777 - vibe_check_actor_initializer - INFO - [ACTOR INIT] Actor file_actor_5 is of type file
2025-05-11 16:45:04,777 - vibe_check_actor_initializer - DEBUG - [ACTOR INIT] Setting actor file_actor_5 state to INITIALIZING
2025-05-11 16:45:04,777 - vibe_check_actor_initializer - INFO - Actor file_actor_5 state changed: created -> initializing
2025-05-11 16:45:04,777 - vibe_check_actor_initializer - DEBUG - [ACTOR INIT] Calling initialize() method on actor file_actor_5
2025-05-11 16:45:04,777 - vibe_check_actor_system - INFO - Actor file_actor_5 is already registered with initialization manager
2025-05-11 16:45:04,777 - vibe_check_actor_system - INFO - Actor file_actor_5 is already registered with legacy initializer
2025-05-11 16:45:04,777 - vibe_check_actor_initialization - INFO - Actor file_actor_5 state changed: created -> initializing
2025-05-11 16:45:04,777 - vibe_check_actor_initializer - DEBUG - Actor file_actor_5 is already in state initializing, no change needed
2025-05-11 16:45:04,777 - vibe_check_initialization_tracker - INFO - Actor file_actor_5 initialization_start
2025-05-11 16:45:04,777 - vibe_check_actor_system - DEBUG - Actor file_actor_5 has no _initialize method
2025-05-11 16:45:04,777 - vibe_check_actor_initialization - INFO - Actor file_actor_5 state changed: initializing -> initialized
2025-05-11 16:45:04,777 - vibe_check_actor_initializer - INFO - Actor file_actor_5 state changed: initializing -> initialized
2025-05-11 16:45:04,777 - vibe_check_initialization_tracker - INFO - Actor file_actor_5 initialization_complete
2025-05-11 16:45:04,777 - vibe_check_actor_system - INFO - Actor file_actor_5 initialized successfully
2025-05-11 16:45:04,777 - vibe_check_actor_system - INFO - Actor file_actor_5 is already registered with initialization manager
2025-05-11 16:45:04,777 - vibe_check_actor_system - INFO - Actor file_actor_5 is already registered with legacy initializer
2025-05-11 16:45:04,777 - vibe_check_actor_initializer - INFO - Actor file_actor_5 state changed: REGISTERED -> INITIALIZING
2025-05-11 16:45:04,777 - vibe_check_actor_system.file_actor_5 - INFO - State transition: REGISTERED -> INITIALIZING
2025-05-11 16:45:04,777 - vibe_check_actor_system.file_actor_5 - DEBUG - State transition details: {'phase': 'initialize'}
2025-05-11 16:45:04,777 - vibe_check_actor_system.file_actor_5 - DEBUG - Operation 'state_transition_REGISTERED_to_INITIALIZING' took 0.000043 seconds
2025-05-11 16:45:04,777 - vibe_check_actor_system.initializer - DEBUG - Component state: {'actor_id': 'file_actor_5', 'state': 'INITIALIZING', 'previous_state': 'REGISTERED', 'timestamp': 1746974704.7774668, 'details': {'phase': 'initialize'}}
2025-05-11 16:45:04,777 - vibe_check_actor_registry - WARNING - Actor file_actor_5 already registered, updating
2025-05-11 16:45:04,777 - vibe_check_actor_registry - INFO - Registered actor file_actor_5 of type file
2025-05-11 16:45:04,777 - vibe_check_actor_system - DEBUG - Actor file_actor_5 has no _initialize method
2025-05-11 16:45:04,777 - vibe_check_actor_initializer - INFO - Actor file_actor_5 state changed: INITIALIZING -> INITIALIZED
2025-05-11 16:45:04,777 - vibe_check_actor_system.file_actor_5 - INFO - State transition: INITIALIZING -> INITIALIZED
2025-05-11 16:45:04,777 - vibe_check_actor_system.file_actor_5 - DEBUG - State transition details: {'phase': 'initialize'}
2025-05-11 16:45:04,777 - vibe_check_actor_system.file_actor_5 - DEBUG - Operation 'state_transition_INITIALIZING_to_INITIALIZED' took 0.000032 seconds
2025-05-11 16:45:04,777 - vibe_check_actor_system.initializer - DEBUG - Component state: {'actor_id': 'file_actor_5', 'state': 'INITIALIZED', 'previous_state': 'INITIALIZING', 'timestamp': 1746974704.7776058, 'details': {'phase': 'initialize'}}
2025-05-11 16:45:04,777 - vibe_check_synchronization - DEBUG - Actor file_actor_5 reached synchronization point initialization_complete
2025-05-11 16:45:04,777 - vibe_check_actor_system - INFO - Actor file_actor_5 initialized successfully
2025-05-11 16:45:04,777 - vibe_check_actor_system.actor.file_actor_5 - ERROR - Error during state transition to FAILED: Actor file_actor_5: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds
2025-05-11 16:45:04,777 - vibe_check_actor_system.actor.file_actor_5 - ERROR - Traceback:
NoneType: None

2025-05-11 16:45:04,777 - vibe_check_actor_initializer - INFO - Actor file_actor_5 state changed: INITIALIZED -> FAILED
2025-05-11 16:45:04,777 - vibe_check_actor_system.file_actor_5 - INFO - State transition: INITIALIZED -> FAILED
2025-05-11 16:45:04,777 - vibe_check_actor_system.file_actor_5 - DEBUG - State transition details: {'phase': 'initialize'}
2025-05-11 16:45:04,777 - vibe_check_actor_system.file_actor_5 - DEBUG - Operation 'state_transition_INITIALIZED_to_FAILED' took 0.000100 seconds
2025-05-11 16:45:04,777 - vibe_check_actor_system.initializer - DEBUG - Component state: {'actor_id': 'file_actor_5', 'state': 'FAILED', 'previous_state': 'INITIALIZED', 'timestamp': 1746974704.777712, 'details': {'phase': 'initialize'}}
2025-05-11 16:45:04,777 - vibe_check_actor_initializer - DEBUG - Cleaned up resources for actor file_actor_5
2025-05-11 16:45:04,777 - vibe_check_actor_system - INFO - Cleaning up resources for actor file_actor_5
2025-05-11 16:45:04,777 - vibe_check_actor_system - INFO - Cleared mailbox for actor file_actor_5
2025-05-11 16:45:04,777 - vibe_check_actor_registry - INFO - Unregistered actor file_actor_5
2025-05-11 16:45:04,777 - vibe_check_actor_system - INFO - Unregistered actor file_actor_5 from registry during cleanup
2025-05-11 16:45:04,777 - vibe_check_actor_initialization - INFO - Actor file_actor_5 state changed: initialized -> stopped
2025-05-11 16:45:04,777 - vibe_check_actor_system - INFO - Set actor file_actor_5 state to STOPPED in initialization manager
2025-05-11 16:45:04,777 - vibe_check_actor_initializer - ERROR - Invalid state transition for actor file_actor_5: initialized -> stopped
2025-05-11 16:45:04,777 - vibe_check_actor_initializer - DEBUG - Valid transitions from initialized are: ['stopping', 'starting', 'rollback', 'failed']
2025-05-11 16:45:04,777 - vibe_check_actor_initializer - WARNING - Forcing transition to stopped state for actor file_actor_5
2025-05-11 16:45:04,777 - vibe_check_actor_initializer - INFO - Actor file_actor_5 state changed: initialized -> stopped
2025-05-11 16:45:04,777 - vibe_check_actor_system - INFO - Set actor file_actor_5 state to STOPPED in legacy initializer
2025-05-11 16:45:04,777 - vibe_check_actor_system - INFO - Cleaned up resources for actor file_actor_5
2025-05-11 16:45:04,778 - vibe_check_actor_system - ERROR - Error in initialization process for actor file_actor_5: Actor file_actor_5: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds
2025-05-11 16:45:04,778 - vibe_check_actor_initializer - ERROR - Actor file_actor_5: Invalid state transition: FAILED -> FAILED
2025-05-11 16:45:04,778 - vibe_check_actor_system - ERROR - Error rolling back actor file_actor_5: Actor file_actor_5: Invalid state transition: FAILED -> FAILED (state: FAILED)
2025-05-11 16:45:04,778 - vibe_check_actor_system - INFO - Cleaning up resources for actor file_actor_5
2025-05-11 16:45:04,778 - vibe_check_actor_system - INFO - Cleared mailbox for actor file_actor_5
2025-05-11 16:45:04,778 - vibe_check_actor_registry - WARNING - Actor file_actor_5 not registered, cannot unregister
2025-05-11 16:45:04,778 - vibe_check_actor_system - INFO - Unregistered actor file_actor_5 from registry during cleanup
2025-05-11 16:45:04,778 - vibe_check_actor_initialization - DEBUG - Actor file_actor_5 is already in state stopped, no change needed
2025-05-11 16:45:04,778 - vibe_check_actor_system - INFO - Set actor file_actor_5 state to STOPPED in initialization manager
2025-05-11 16:45:04,778 - vibe_check_actor_initializer - DEBUG - Actor file_actor_5 is already in state stopped, no change needed
2025-05-11 16:45:04,778 - vibe_check_actor_initializer - INFO - Actor file_actor_5 state changed: stopped -> stopped
2025-05-11 16:45:04,778 - vibe_check_actor_system - INFO - Set actor file_actor_5 state to STOPPED in legacy initializer
2025-05-11 16:45:04,778 - vibe_check_actor_system - INFO - Cleaned up resources for actor file_actor_5
2025-05-11 16:45:04,778 - vibe_check_actor_initializer - ERROR - [ACTOR INIT] Failed to initialize actor file_actor_5: Actor file_actor_5: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds
2025-05-11 16:45:04,778 - vibe_check_actor_initializer - ERROR - [ACTOR INIT] Traceback for actor file_actor_5:
Traceback (most recent call last):
  File "/Users/<USER>/Lokalne_Kody/PAT_project_analysis/vibe_check/core/actor_system/actor_initializer.py", line 936, in initialize_actor
    await asyncio.wait_for(actor.initialize(), timeout)
  File "/Users/<USER>/.pyenv/versions/3.13.3/lib/python3.13/asyncio/tasks.py", line 507, in wait_for
    return await fut
           ^^^^^^^^^
  File "/Users/<USER>/Lokalne_Kody/PAT_project_analysis/vibe_check/core/actor_system/actor.py", line 1109, in initialize
    raise timeout_error
vibe_check.core.actor_system.exceptions.ActorTimeoutError: Actor file_actor_5: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds

2025-05-11 16:45:04,778 - vibe_check_actor_initializer - DEBUG - [ACTOR INIT] Setting actor file_actor_5 state to FAILED
2025-05-11 16:45:04,778 - vibe_check_actor_initializer - ERROR - Invalid state transition for actor file_actor_5: stopped -> failed
2025-05-11 16:45:04,778 - vibe_check_actor_initializer - DEBUG - Valid transitions from stopped are: ['initializing', 'created']
2025-05-11 16:45:04,778 - vibe_check_actor_initializer - WARNING - Forcing transition to failed state for actor file_actor_5
2025-05-11 16:45:04,778 - vibe_check_actor_initializer - INFO - Actor file_actor_5 state changed: stopped -> failed
2025-05-11 16:45:04,778 - vibe_check_actor_initializer - ERROR - Actor file_actor_5 failed: Actor file_actor_5 failed during initialize phase (state: stopped): Actor file_actor_5: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds
Original error: Actor file_actor_5: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds
2025-05-11 16:45:04,778 - vibe_check_actor_lifecycle_manager - WARNING - Failed to initialize actor file_actor_5 (attempt 1/3): Actor file_actor_5: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds
2025-05-11 16:45:04,778 - vibe_check_initialization_tracker - INFO - Actor file_actor_5 initialization_start
2025-05-11 16:45:04,778 - vibe_check_initialization_tracker - INFO - Actor file_actor_5 initialization_start
2025-05-11 16:45:04,779 - vibe_check_initialization_tracker - INFO - Actor file_actor_5 initialization_complete
2025-05-11 16:45:04,779 - vibe_check_initialization_tracker - INFO - Actor file_actor_5 initialization_complete
2025-05-11 16:45:04,779 - vibe_check_initialization_tracker - ERROR - Actor file_actor_5 failed: Actor file_actor_5: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds
2025-05-11 16:45:04,879 - vibe_check_actor_lifecycle_manager - INFO - Initializing actor file_actor_5 (type: file)
2025-05-11 16:45:04,879 - vibe_check_actor_initializer - INFO - [ACTOR INIT] Starting initialization of actor file_actor_5
2025-05-11 16:45:04,879 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting registry for actor file_actor_5
2025-05-11 16:45:04,879 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting actor file_actor_5 from registry
2025-05-11 16:45:04,879 - vibe_check_actor_initializer - WARNING - [ACTOR REGISTRY] Actor file_actor_5 not found in registry
2025-05-11 16:45:04,879 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Available actors in registry: ['file_actor_6', 'file_actor_7', 'file_actor_8', 'file_actor_9', 'file_actor_10', 'file_actor_11', 'file_actor_12', 'file_actor_13', 'project_actor']
2025-05-11 16:45:04,879 - vibe_check_actor_initializer - WARNING - [ACTOR INIT] Actor file_actor_5 not found in registry, retrying (1/3)
2025-05-11 16:45:04,980 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting registry for actor file_actor_5
2025-05-11 16:45:04,981 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting actor file_actor_5 from registry
2025-05-11 16:45:04,981 - vibe_check_actor_initializer - WARNING - [ACTOR REGISTRY] Actor file_actor_5 not found in registry
2025-05-11 16:45:04,981 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Available actors in registry: ['file_actor_6', 'file_actor_7', 'file_actor_8', 'file_actor_9', 'file_actor_10', 'file_actor_11', 'file_actor_12', 'file_actor_13', 'project_actor']
2025-05-11 16:45:04,981 - vibe_check_actor_initializer - WARNING - [ACTOR INIT] Actor file_actor_5 not found in registry, retrying (2/3)
2025-05-11 16:45:05,182 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting registry for actor file_actor_5
2025-05-11 16:45:05,182 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting actor file_actor_5 from registry
2025-05-11 16:45:05,182 - vibe_check_actor_initializer - WARNING - [ACTOR REGISTRY] Actor file_actor_5 not found in registry
2025-05-11 16:45:05,182 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Available actors in registry: ['file_actor_6', 'file_actor_7', 'file_actor_8', 'file_actor_9', 'file_actor_10', 'file_actor_11', 'file_actor_12', 'file_actor_13', 'project_actor']
2025-05-11 16:45:05,182 - vibe_check_actor_initializer - ERROR - [ACTOR INIT] Actor file_actor_5 not found in registry after 3 attempts
2025-05-11 16:45:05,182 - vibe_check_actor_lifecycle_manager - WARNING - Failed to initialize actor file_actor_5 (attempt 2/3): Actor file_actor_5 not found in registry after 3 attempts
2025-05-11 16:45:05,384 - vibe_check_actor_lifecycle_manager - INFO - Initializing actor file_actor_5 (type: file)
2025-05-11 16:45:05,384 - vibe_check_actor_initializer - INFO - [ACTOR INIT] Starting initialization of actor file_actor_5
2025-05-11 16:45:05,384 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting registry for actor file_actor_5
2025-05-11 16:45:05,384 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting actor file_actor_5 from registry
2025-05-11 16:45:05,384 - vibe_check_actor_initializer - WARNING - [ACTOR REGISTRY] Actor file_actor_5 not found in registry
2025-05-11 16:45:05,384 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Available actors in registry: ['file_actor_6', 'file_actor_7', 'file_actor_8', 'file_actor_9', 'file_actor_10', 'file_actor_11', 'file_actor_12', 'file_actor_13', 'project_actor']
2025-05-11 16:45:05,384 - vibe_check_actor_initializer - WARNING - [ACTOR INIT] Actor file_actor_5 not found in registry, retrying (1/3)
2025-05-11 16:45:05,486 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting registry for actor file_actor_5
2025-05-11 16:45:05,486 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting actor file_actor_5 from registry
2025-05-11 16:45:05,486 - vibe_check_actor_initializer - WARNING - [ACTOR REGISTRY] Actor file_actor_5 not found in registry
2025-05-11 16:45:05,486 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Available actors in registry: ['file_actor_6', 'file_actor_7', 'file_actor_8', 'file_actor_9', 'file_actor_10', 'file_actor_11', 'file_actor_12', 'file_actor_13', 'project_actor']
2025-05-11 16:45:05,486 - vibe_check_actor_initializer - WARNING - [ACTOR INIT] Actor file_actor_5 not found in registry, retrying (2/3)
2025-05-11 16:45:05,687 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting registry for actor file_actor_5
2025-05-11 16:45:05,688 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting actor file_actor_5 from registry
2025-05-11 16:45:05,688 - vibe_check_actor_initializer - WARNING - [ACTOR REGISTRY] Actor file_actor_5 not found in registry
2025-05-11 16:45:05,688 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Available actors in registry: ['file_actor_6', 'file_actor_7', 'file_actor_8', 'file_actor_9', 'file_actor_10', 'file_actor_11', 'file_actor_12', 'file_actor_13', 'project_actor']
2025-05-11 16:45:05,688 - vibe_check_actor_initializer - ERROR - [ACTOR INIT] Actor file_actor_5 not found in registry after 3 attempts
2025-05-11 16:45:05,688 - vibe_check_actor_lifecycle_manager - ERROR - Failed to initialize actor file_actor_5 after 3 attempts: Actor file_actor_5 not found in registry after 3 attempts
2025-05-11 16:45:05,688 - vibe_check_actor_lifecycle_manager - ERROR - Traceback (most recent call last):
  File "/Users/<USER>/Lokalne_Kody/PAT_project_analysis/vibe_check/core/orchestration/actor_lifecycle_manager.py", line 174, in _initialize_all_actors
    await initializer.initialize_actor(actor_id)
  File "/Users/<USER>/Lokalne_Kody/PAT_project_analysis/vibe_check/core/actor_system/actor_initializer.py", line 883, in initialize_actor
    raise ValueError(error_msg)
ValueError: Actor file_actor_5 not found in registry after 3 attempts

2025-05-11 16:45:05,688 - vibe_check_actor_lifecycle_manager - INFO - Initializing actor file_actor_6 (type: file)
2025-05-11 16:45:05,688 - vibe_check_actor_initializer - INFO - [ACTOR INIT] Starting initialization of actor file_actor_6
2025-05-11 16:45:05,688 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting registry for actor file_actor_6
2025-05-11 16:45:05,688 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting actor file_actor_6 from registry
2025-05-11 16:45:05,688 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Successfully retrieved actor file_actor_6 from registry
2025-05-11 16:45:05,688 - vibe_check_actor_initializer - INFO - [ACTOR INIT] Actor file_actor_6 is of type file
2025-05-11 16:45:05,688 - vibe_check_actor_initializer - DEBUG - [ACTOR INIT] Setting actor file_actor_6 state to INITIALIZING
2025-05-11 16:45:05,688 - vibe_check_actor_initializer - INFO - Actor file_actor_6 state changed: created -> initializing
2025-05-11 16:45:05,688 - vibe_check_actor_initializer - DEBUG - [ACTOR INIT] Calling initialize() method on actor file_actor_6
2025-05-11 16:45:05,688 - vibe_check_actor_system - INFO - Actor file_actor_6 is already registered with initialization manager
2025-05-11 16:45:05,688 - vibe_check_actor_system - INFO - Actor file_actor_6 is already registered with legacy initializer
2025-05-11 16:45:05,688 - vibe_check_actor_initialization - INFO - Actor file_actor_6 state changed: created -> initializing
2025-05-11 16:45:05,688 - vibe_check_actor_initializer - DEBUG - Actor file_actor_6 is already in state initializing, no change needed
2025-05-11 16:45:05,688 - vibe_check_initialization_tracker - INFO - Actor file_actor_6 initialization_start
2025-05-11 16:45:05,689 - vibe_check_actor_system - DEBUG - Actor file_actor_6 has no _initialize method
2025-05-11 16:45:05,689 - vibe_check_actor_initialization - INFO - Actor file_actor_6 state changed: initializing -> initialized
2025-05-11 16:45:05,689 - vibe_check_actor_initializer - INFO - Actor file_actor_6 state changed: initializing -> initialized
2025-05-11 16:45:05,689 - vibe_check_initialization_tracker - INFO - Actor file_actor_6 initialization_complete
2025-05-11 16:45:05,689 - vibe_check_actor_system - INFO - Actor file_actor_6 initialized successfully
2025-05-11 16:45:05,689 - vibe_check_actor_system - INFO - Actor file_actor_6 is already registered with initialization manager
2025-05-11 16:45:05,689 - vibe_check_actor_system - INFO - Actor file_actor_6 is already registered with legacy initializer
2025-05-11 16:45:05,689 - vibe_check_actor_initializer - INFO - Actor file_actor_6 state changed: REGISTERED -> INITIALIZING
2025-05-11 16:45:05,689 - vibe_check_actor_system.file_actor_6 - INFO - State transition: REGISTERED -> INITIALIZING
2025-05-11 16:45:05,689 - vibe_check_actor_system.file_actor_6 - DEBUG - State transition details: {'phase': 'initialize'}
2025-05-11 16:45:05,689 - vibe_check_actor_system.file_actor_6 - DEBUG - Operation 'state_transition_REGISTERED_to_INITIALIZING' took 0.000066 seconds
2025-05-11 16:45:05,689 - vibe_check_actor_system.initializer - DEBUG - Component state: {'actor_id': 'file_actor_6', 'state': 'INITIALIZING', 'previous_state': 'REGISTERED', 'timestamp': 1746974705.689156, 'details': {'phase': 'initialize'}}
2025-05-11 16:45:05,689 - vibe_check_actor_registry - WARNING - Actor file_actor_6 already registered, updating
2025-05-11 16:45:05,689 - vibe_check_actor_registry - INFO - Registered actor file_actor_6 of type file
2025-05-11 16:45:05,689 - vibe_check_actor_system - DEBUG - Actor file_actor_6 has no _initialize method
2025-05-11 16:45:05,689 - vibe_check_actor_initializer - INFO - Actor file_actor_6 state changed: INITIALIZING -> INITIALIZED
2025-05-11 16:45:05,689 - vibe_check_actor_system.file_actor_6 - INFO - State transition: INITIALIZING -> INITIALIZED
2025-05-11 16:45:05,689 - vibe_check_actor_system.file_actor_6 - DEBUG - State transition details: {'phase': 'initialize'}
2025-05-11 16:45:05,689 - vibe_check_actor_system.file_actor_6 - DEBUG - Operation 'state_transition_INITIALIZING_to_INITIALIZED' took 0.000041 seconds
2025-05-11 16:45:05,689 - vibe_check_actor_system.initializer - DEBUG - Component state: {'actor_id': 'file_actor_6', 'state': 'INITIALIZED', 'previous_state': 'INITIALIZING', 'timestamp': 1746974705.689342, 'details': {'phase': 'initialize'}}
2025-05-11 16:45:05,689 - vibe_check_synchronization - DEBUG - Actor file_actor_6 reached synchronization point initialization_complete
2025-05-11 16:45:05,689 - vibe_check_actor_system - INFO - Actor file_actor_6 initialized successfully
2025-05-11 16:45:05,689 - vibe_check_actor_system.actor.file_actor_6 - ERROR - Error during state transition to FAILED: Actor file_actor_6: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds
2025-05-11 16:45:05,689 - vibe_check_actor_system.actor.file_actor_6 - ERROR - Traceback:
NoneType: None

2025-05-11 16:45:05,689 - vibe_check_actor_initializer - INFO - Actor file_actor_6 state changed: INITIALIZED -> FAILED
2025-05-11 16:45:05,689 - vibe_check_actor_system.file_actor_6 - INFO - State transition: INITIALIZED -> FAILED
2025-05-11 16:45:05,689 - vibe_check_actor_system.file_actor_6 - DEBUG - State transition details: {'phase': 'initialize'}
2025-05-11 16:45:05,689 - vibe_check_actor_system.file_actor_6 - DEBUG - Operation 'state_transition_INITIALIZED_to_FAILED' took 0.000133 seconds
2025-05-11 16:45:05,689 - vibe_check_actor_system.initializer - DEBUG - Component state: {'actor_id': 'file_actor_6', 'state': 'FAILED', 'previous_state': 'INITIALIZED', 'timestamp': 1746974705.689468, 'details': {'phase': 'initialize'}}
2025-05-11 16:45:05,689 - vibe_check_actor_initializer - DEBUG - Cleaned up resources for actor file_actor_6
2025-05-11 16:45:05,689 - vibe_check_actor_system - INFO - Cleaning up resources for actor file_actor_6
2025-05-11 16:45:05,689 - vibe_check_actor_system - INFO - Cleared mailbox for actor file_actor_6
2025-05-11 16:45:05,689 - vibe_check_actor_registry - INFO - Unregistered actor file_actor_6
2025-05-11 16:45:05,689 - vibe_check_actor_system - INFO - Unregistered actor file_actor_6 from registry during cleanup
2025-05-11 16:45:05,689 - vibe_check_actor_initialization - INFO - Actor file_actor_6 state changed: initialized -> stopped
2025-05-11 16:45:05,689 - vibe_check_actor_system - INFO - Set actor file_actor_6 state to STOPPED in initialization manager
2025-05-11 16:45:05,689 - vibe_check_actor_initializer - ERROR - Invalid state transition for actor file_actor_6: initialized -> stopped
2025-05-11 16:45:05,689 - vibe_check_actor_initializer - DEBUG - Valid transitions from initialized are: ['stopping', 'starting', 'rollback', 'failed']
2025-05-11 16:45:05,689 - vibe_check_actor_initializer - WARNING - Forcing transition to stopped state for actor file_actor_6
2025-05-11 16:45:05,689 - vibe_check_actor_initializer - INFO - Actor file_actor_6 state changed: initialized -> stopped
2025-05-11 16:45:05,689 - vibe_check_actor_system - INFO - Set actor file_actor_6 state to STOPPED in legacy initializer
2025-05-11 16:45:05,689 - vibe_check_actor_system - INFO - Cleaned up resources for actor file_actor_6
2025-05-11 16:45:05,689 - vibe_check_actor_system - ERROR - Error in initialization process for actor file_actor_6: Actor file_actor_6: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds
2025-05-11 16:45:05,689 - vibe_check_actor_initializer - ERROR - Actor file_actor_6: Invalid state transition: FAILED -> FAILED
2025-05-11 16:45:05,689 - vibe_check_actor_system - ERROR - Error rolling back actor file_actor_6: Actor file_actor_6: Invalid state transition: FAILED -> FAILED (state: FAILED)
2025-05-11 16:45:05,689 - vibe_check_actor_system - INFO - Cleaning up resources for actor file_actor_6
2025-05-11 16:45:05,689 - vibe_check_actor_system - INFO - Cleared mailbox for actor file_actor_6
2025-05-11 16:45:05,689 - vibe_check_actor_registry - WARNING - Actor file_actor_6 not registered, cannot unregister
2025-05-11 16:45:05,689 - vibe_check_actor_system - INFO - Unregistered actor file_actor_6 from registry during cleanup
2025-05-11 16:45:05,689 - vibe_check_actor_initialization - DEBUG - Actor file_actor_6 is already in state stopped, no change needed
2025-05-11 16:45:05,689 - vibe_check_actor_system - INFO - Set actor file_actor_6 state to STOPPED in initialization manager
2025-05-11 16:45:05,689 - vibe_check_actor_initializer - DEBUG - Actor file_actor_6 is already in state stopped, no change needed
2025-05-11 16:45:05,689 - vibe_check_actor_initializer - INFO - Actor file_actor_6 state changed: stopped -> stopped
2025-05-11 16:45:05,689 - vibe_check_actor_system - INFO - Set actor file_actor_6 state to STOPPED in legacy initializer
2025-05-11 16:45:05,690 - vibe_check_actor_system - INFO - Cleaned up resources for actor file_actor_6
2025-05-11 16:45:05,690 - vibe_check_actor_initializer - ERROR - [ACTOR INIT] Failed to initialize actor file_actor_6: Actor file_actor_6: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds
2025-05-11 16:45:05,690 - vibe_check_actor_initializer - ERROR - [ACTOR INIT] Traceback for actor file_actor_6:
Traceback (most recent call last):
  File "/Users/<USER>/Lokalne_Kody/PAT_project_analysis/vibe_check/core/actor_system/actor_initializer.py", line 936, in initialize_actor
    await asyncio.wait_for(actor.initialize(), timeout)
  File "/Users/<USER>/.pyenv/versions/3.13.3/lib/python3.13/asyncio/tasks.py", line 507, in wait_for
    return await fut
           ^^^^^^^^^
  File "/Users/<USER>/Lokalne_Kody/PAT_project_analysis/vibe_check/core/actor_system/actor.py", line 1109, in initialize
    raise timeout_error
vibe_check.core.actor_system.exceptions.ActorTimeoutError: Actor file_actor_6: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds

2025-05-11 16:45:05,690 - vibe_check_actor_initializer - DEBUG - [ACTOR INIT] Setting actor file_actor_6 state to FAILED
2025-05-11 16:45:05,690 - vibe_check_actor_initializer - ERROR - Invalid state transition for actor file_actor_6: stopped -> failed
2025-05-11 16:45:05,690 - vibe_check_actor_initializer - DEBUG - Valid transitions from stopped are: ['initializing', 'created']
2025-05-11 16:45:05,690 - vibe_check_actor_initializer - WARNING - Forcing transition to failed state for actor file_actor_6
2025-05-11 16:45:05,690 - vibe_check_actor_initializer - INFO - Actor file_actor_6 state changed: stopped -> failed
2025-05-11 16:45:05,690 - vibe_check_actor_initializer - ERROR - Actor file_actor_6 failed: Actor file_actor_6 failed during initialize phase (state: stopped): Actor file_actor_6: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds
Original error: Actor file_actor_6: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds
2025-05-11 16:45:05,690 - vibe_check_actor_lifecycle_manager - WARNING - Failed to initialize actor file_actor_6 (attempt 1/3): Actor file_actor_6: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds
2025-05-11 16:45:05,690 - vibe_check_initialization_tracker - INFO - Actor file_actor_6 initialization_start
2025-05-11 16:45:05,691 - vibe_check_initialization_tracker - INFO - Actor file_actor_6 initialization_start
2025-05-11 16:45:05,691 - vibe_check_initialization_tracker - INFO - Actor file_actor_6 initialization_complete
2025-05-11 16:45:05,691 - vibe_check_initialization_tracker - INFO - Actor file_actor_6 initialization_complete
2025-05-11 16:45:05,691 - vibe_check_initialization_tracker - ERROR - Actor file_actor_6 failed: Actor file_actor_6: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds
2025-05-11 16:45:05,791 - vibe_check_actor_lifecycle_manager - INFO - Initializing actor file_actor_6 (type: file)
2025-05-11 16:45:05,792 - vibe_check_actor_initializer - INFO - [ACTOR INIT] Starting initialization of actor file_actor_6
2025-05-11 16:45:05,792 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting registry for actor file_actor_6
2025-05-11 16:45:05,792 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting actor file_actor_6 from registry
2025-05-11 16:45:05,792 - vibe_check_actor_initializer - WARNING - [ACTOR REGISTRY] Actor file_actor_6 not found in registry
2025-05-11 16:45:05,792 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Available actors in registry: ['file_actor_7', 'file_actor_8', 'file_actor_9', 'file_actor_10', 'file_actor_11', 'file_actor_12', 'file_actor_13', 'project_actor']
2025-05-11 16:45:05,792 - vibe_check_actor_initializer - WARNING - [ACTOR INIT] Actor file_actor_6 not found in registry, retrying (1/3)
2025-05-11 16:45:05,893 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting registry for actor file_actor_6
2025-05-11 16:45:05,893 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting actor file_actor_6 from registry
2025-05-11 16:45:05,894 - vibe_check_actor_initializer - WARNING - [ACTOR REGISTRY] Actor file_actor_6 not found in registry
2025-05-11 16:45:05,894 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Available actors in registry: ['file_actor_7', 'file_actor_8', 'file_actor_9', 'file_actor_10', 'file_actor_11', 'file_actor_12', 'file_actor_13', 'project_actor']
2025-05-11 16:45:05,894 - vibe_check_actor_initializer - WARNING - [ACTOR INIT] Actor file_actor_6 not found in registry, retrying (2/3)
2025-05-11 16:45:06,094 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting registry for actor file_actor_6
2025-05-11 16:45:06,095 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting actor file_actor_6 from registry
2025-05-11 16:45:06,095 - vibe_check_actor_initializer - WARNING - [ACTOR REGISTRY] Actor file_actor_6 not found in registry
2025-05-11 16:45:06,095 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Available actors in registry: ['file_actor_7', 'file_actor_8', 'file_actor_9', 'file_actor_10', 'file_actor_11', 'file_actor_12', 'file_actor_13', 'project_actor']
2025-05-11 16:45:06,095 - vibe_check_actor_initializer - ERROR - [ACTOR INIT] Actor file_actor_6 not found in registry after 3 attempts
2025-05-11 16:45:06,095 - vibe_check_actor_lifecycle_manager - WARNING - Failed to initialize actor file_actor_6 (attempt 2/3): Actor file_actor_6 not found in registry after 3 attempts
2025-05-11 16:45:06,296 - vibe_check_actor_lifecycle_manager - INFO - Initializing actor file_actor_6 (type: file)
2025-05-11 16:45:06,296 - vibe_check_actor_initializer - INFO - [ACTOR INIT] Starting initialization of actor file_actor_6
2025-05-11 16:45:06,296 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting registry for actor file_actor_6
2025-05-11 16:45:06,296 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting actor file_actor_6 from registry
2025-05-11 16:45:06,296 - vibe_check_actor_initializer - WARNING - [ACTOR REGISTRY] Actor file_actor_6 not found in registry
2025-05-11 16:45:06,296 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Available actors in registry: ['file_actor_7', 'file_actor_8', 'file_actor_9', 'file_actor_10', 'file_actor_11', 'file_actor_12', 'file_actor_13', 'project_actor']
2025-05-11 16:45:06,296 - vibe_check_actor_initializer - WARNING - [ACTOR INIT] Actor file_actor_6 not found in registry, retrying (1/3)
2025-05-11 16:45:06,398 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting registry for actor file_actor_6
2025-05-11 16:45:06,398 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting actor file_actor_6 from registry
2025-05-11 16:45:06,398 - vibe_check_actor_initializer - WARNING - [ACTOR REGISTRY] Actor file_actor_6 not found in registry
2025-05-11 16:45:06,398 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Available actors in registry: ['file_actor_7', 'file_actor_8', 'file_actor_9', 'file_actor_10', 'file_actor_11', 'file_actor_12', 'file_actor_13', 'project_actor']
2025-05-11 16:45:06,398 - vibe_check_actor_initializer - WARNING - [ACTOR INIT] Actor file_actor_6 not found in registry, retrying (2/3)
2025-05-11 16:45:06,599 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting registry for actor file_actor_6
2025-05-11 16:45:06,599 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting actor file_actor_6 from registry
2025-05-11 16:45:06,599 - vibe_check_actor_initializer - WARNING - [ACTOR REGISTRY] Actor file_actor_6 not found in registry
2025-05-11 16:45:06,599 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Available actors in registry: ['file_actor_7', 'file_actor_8', 'file_actor_9', 'file_actor_10', 'file_actor_11', 'file_actor_12', 'file_actor_13', 'project_actor']
2025-05-11 16:45:06,599 - vibe_check_actor_initializer - ERROR - [ACTOR INIT] Actor file_actor_6 not found in registry after 3 attempts
2025-05-11 16:45:06,599 - vibe_check_actor_lifecycle_manager - ERROR - Failed to initialize actor file_actor_6 after 3 attempts: Actor file_actor_6 not found in registry after 3 attempts
2025-05-11 16:45:06,600 - vibe_check_actor_lifecycle_manager - ERROR - Traceback (most recent call last):
  File "/Users/<USER>/Lokalne_Kody/PAT_project_analysis/vibe_check/core/orchestration/actor_lifecycle_manager.py", line 174, in _initialize_all_actors
    await initializer.initialize_actor(actor_id)
  File "/Users/<USER>/Lokalne_Kody/PAT_project_analysis/vibe_check/core/actor_system/actor_initializer.py", line 883, in initialize_actor
    raise ValueError(error_msg)
ValueError: Actor file_actor_6 not found in registry after 3 attempts

2025-05-11 16:45:06,600 - vibe_check_actor_lifecycle_manager - INFO - Initializing actor file_actor_7 (type: file)
2025-05-11 16:45:06,600 - vibe_check_actor_initializer - INFO - [ACTOR INIT] Starting initialization of actor file_actor_7
2025-05-11 16:45:06,600 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting registry for actor file_actor_7
2025-05-11 16:45:06,600 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting actor file_actor_7 from registry
2025-05-11 16:45:06,600 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Successfully retrieved actor file_actor_7 from registry
2025-05-11 16:45:06,600 - vibe_check_actor_initializer - INFO - [ACTOR INIT] Actor file_actor_7 is of type file
2025-05-11 16:45:06,600 - vibe_check_actor_initializer - DEBUG - [ACTOR INIT] Setting actor file_actor_7 state to INITIALIZING
2025-05-11 16:45:06,600 - vibe_check_actor_initializer - INFO - Actor file_actor_7 state changed: created -> initializing
2025-05-11 16:45:06,600 - vibe_check_actor_initializer - DEBUG - [ACTOR INIT] Calling initialize() method on actor file_actor_7
2025-05-11 16:45:06,600 - vibe_check_actor_system - INFO - Actor file_actor_7 is already registered with initialization manager
2025-05-11 16:45:06,600 - vibe_check_actor_system - INFO - Actor file_actor_7 is already registered with legacy initializer
2025-05-11 16:45:06,600 - vibe_check_actor_initialization - INFO - Actor file_actor_7 state changed: created -> initializing
2025-05-11 16:45:06,600 - vibe_check_actor_initializer - DEBUG - Actor file_actor_7 is already in state initializing, no change needed
2025-05-11 16:45:06,600 - vibe_check_initialization_tracker - INFO - Actor file_actor_7 initialization_start
2025-05-11 16:45:06,600 - vibe_check_actor_system - DEBUG - Actor file_actor_7 has no _initialize method
2025-05-11 16:45:06,600 - vibe_check_actor_initialization - INFO - Actor file_actor_7 state changed: initializing -> initialized
2025-05-11 16:45:06,600 - vibe_check_actor_initializer - INFO - Actor file_actor_7 state changed: initializing -> initialized
2025-05-11 16:45:06,600 - vibe_check_initialization_tracker - INFO - Actor file_actor_7 initialization_complete
2025-05-11 16:45:06,600 - vibe_check_actor_system - INFO - Actor file_actor_7 initialized successfully
2025-05-11 16:45:06,600 - vibe_check_actor_system - INFO - Actor file_actor_7 is already registered with initialization manager
2025-05-11 16:45:06,600 - vibe_check_actor_system - INFO - Actor file_actor_7 is already registered with legacy initializer
2025-05-11 16:45:06,600 - vibe_check_actor_initializer - INFO - Actor file_actor_7 state changed: REGISTERED -> INITIALIZING
2025-05-11 16:45:06,600 - vibe_check_actor_system.file_actor_7 - INFO - State transition: REGISTERED -> INITIALIZING
2025-05-11 16:45:06,600 - vibe_check_actor_system.file_actor_7 - DEBUG - State transition details: {'phase': 'initialize'}
2025-05-11 16:45:06,600 - vibe_check_actor_system.file_actor_7 - DEBUG - Operation 'state_transition_REGISTERED_to_INITIALIZING' took 0.000052 seconds
2025-05-11 16:45:06,600 - vibe_check_actor_system.initializer - DEBUG - Component state: {'actor_id': 'file_actor_7', 'state': 'INITIALIZING', 'previous_state': 'REGISTERED', 'timestamp': 1746974706.600558, 'details': {'phase': 'initialize'}}
2025-05-11 16:45:06,600 - vibe_check_actor_registry - WARNING - Actor file_actor_7 already registered, updating
2025-05-11 16:45:06,600 - vibe_check_actor_registry - INFO - Registered actor file_actor_7 of type file
2025-05-11 16:45:06,600 - vibe_check_actor_system - DEBUG - Actor file_actor_7 has no _initialize method
2025-05-11 16:45:06,600 - vibe_check_actor_initializer - INFO - Actor file_actor_7 state changed: INITIALIZING -> INITIALIZED
2025-05-11 16:45:06,600 - vibe_check_actor_system.file_actor_7 - INFO - State transition: INITIALIZING -> INITIALIZED
2025-05-11 16:45:06,600 - vibe_check_actor_system.file_actor_7 - DEBUG - State transition details: {'phase': 'initialize'}
2025-05-11 16:45:06,600 - vibe_check_actor_system.file_actor_7 - DEBUG - Operation 'state_transition_INITIALIZING_to_INITIALIZED' took 0.000035 seconds
2025-05-11 16:45:06,600 - vibe_check_actor_system.initializer - DEBUG - Component state: {'actor_id': 'file_actor_7', 'state': 'INITIALIZED', 'previous_state': 'INITIALIZING', 'timestamp': 1746974706.600704, 'details': {'phase': 'initialize'}}
2025-05-11 16:45:06,600 - vibe_check_synchronization - DEBUG - Actor file_actor_7 reached synchronization point initialization_complete
2025-05-11 16:45:06,600 - vibe_check_actor_system - INFO - Actor file_actor_7 initialized successfully
2025-05-11 16:45:06,600 - vibe_check_actor_system.actor.file_actor_7 - ERROR - Error during state transition to FAILED: Actor file_actor_7: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds
2025-05-11 16:45:06,600 - vibe_check_actor_system.actor.file_actor_7 - ERROR - Traceback:
NoneType: None

2025-05-11 16:45:06,600 - vibe_check_actor_initializer - INFO - Actor file_actor_7 state changed: INITIALIZED -> FAILED
2025-05-11 16:45:06,600 - vibe_check_actor_system.file_actor_7 - INFO - State transition: INITIALIZED -> FAILED
2025-05-11 16:45:06,600 - vibe_check_actor_system.file_actor_7 - DEBUG - State transition details: {'phase': 'initialize'}
2025-05-11 16:45:06,600 - vibe_check_actor_system.file_actor_7 - DEBUG - Operation 'state_transition_INITIALIZED_to_FAILED' took 0.000107 seconds
2025-05-11 16:45:06,600 - vibe_check_actor_system.initializer - DEBUG - Component state: {'actor_id': 'file_actor_7', 'state': 'FAILED', 'previous_state': 'INITIALIZED', 'timestamp': 1746974706.600814, 'details': {'phase': 'initialize'}}
2025-05-11 16:45:06,600 - vibe_check_actor_initializer - DEBUG - Cleaned up resources for actor file_actor_7
2025-05-11 16:45:06,600 - vibe_check_actor_system - INFO - Cleaning up resources for actor file_actor_7
2025-05-11 16:45:06,600 - vibe_check_actor_system - INFO - Cleared mailbox for actor file_actor_7
2025-05-11 16:45:06,600 - vibe_check_actor_registry - INFO - Unregistered actor file_actor_7
2025-05-11 16:45:06,601 - vibe_check_actor_system - INFO - Unregistered actor file_actor_7 from registry during cleanup
2025-05-11 16:45:06,601 - vibe_check_actor_initialization - INFO - Actor file_actor_7 state changed: initialized -> stopped
2025-05-11 16:45:06,601 - vibe_check_actor_system - INFO - Set actor file_actor_7 state to STOPPED in initialization manager
2025-05-11 16:45:06,601 - vibe_check_actor_initializer - ERROR - Invalid state transition for actor file_actor_7: initialized -> stopped
2025-05-11 16:45:06,601 - vibe_check_actor_initializer - DEBUG - Valid transitions from initialized are: ['stopping', 'starting', 'rollback', 'failed']
2025-05-11 16:45:06,601 - vibe_check_actor_initializer - WARNING - Forcing transition to stopped state for actor file_actor_7
2025-05-11 16:45:06,601 - vibe_check_actor_initializer - INFO - Actor file_actor_7 state changed: initialized -> stopped
2025-05-11 16:45:06,601 - vibe_check_actor_system - INFO - Set actor file_actor_7 state to STOPPED in legacy initializer
2025-05-11 16:45:06,601 - vibe_check_actor_system - INFO - Cleaned up resources for actor file_actor_7
2025-05-11 16:45:06,601 - vibe_check_actor_system - ERROR - Error in initialization process for actor file_actor_7: Actor file_actor_7: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds
2025-05-11 16:45:06,601 - vibe_check_actor_initializer - ERROR - Actor file_actor_7: Invalid state transition: FAILED -> FAILED
2025-05-11 16:45:06,601 - vibe_check_actor_system - ERROR - Error rolling back actor file_actor_7: Actor file_actor_7: Invalid state transition: FAILED -> FAILED (state: FAILED)
2025-05-11 16:45:06,601 - vibe_check_actor_system - INFO - Cleaning up resources for actor file_actor_7
2025-05-11 16:45:06,601 - vibe_check_actor_system - INFO - Cleared mailbox for actor file_actor_7
2025-05-11 16:45:06,601 - vibe_check_actor_registry - WARNING - Actor file_actor_7 not registered, cannot unregister
2025-05-11 16:45:06,601 - vibe_check_actor_system - INFO - Unregistered actor file_actor_7 from registry during cleanup
2025-05-11 16:45:06,601 - vibe_check_actor_initialization - DEBUG - Actor file_actor_7 is already in state stopped, no change needed
2025-05-11 16:45:06,601 - vibe_check_actor_system - INFO - Set actor file_actor_7 state to STOPPED in initialization manager
2025-05-11 16:45:06,601 - vibe_check_actor_initializer - DEBUG - Actor file_actor_7 is already in state stopped, no change needed
2025-05-11 16:45:06,601 - vibe_check_actor_initializer - INFO - Actor file_actor_7 state changed: stopped -> stopped
2025-05-11 16:45:06,601 - vibe_check_actor_system - INFO - Set actor file_actor_7 state to STOPPED in legacy initializer
2025-05-11 16:45:06,601 - vibe_check_actor_system - INFO - Cleaned up resources for actor file_actor_7
2025-05-11 16:45:06,601 - vibe_check_actor_initializer - ERROR - [ACTOR INIT] Failed to initialize actor file_actor_7: Actor file_actor_7: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds
2025-05-11 16:45:06,601 - vibe_check_actor_initializer - ERROR - [ACTOR INIT] Traceback for actor file_actor_7:
Traceback (most recent call last):
  File "/Users/<USER>/Lokalne_Kody/PAT_project_analysis/vibe_check/core/actor_system/actor_initializer.py", line 936, in initialize_actor
    await asyncio.wait_for(actor.initialize(), timeout)
  File "/Users/<USER>/.pyenv/versions/3.13.3/lib/python3.13/asyncio/tasks.py", line 507, in wait_for
    return await fut
           ^^^^^^^^^
  File "/Users/<USER>/Lokalne_Kody/PAT_project_analysis/vibe_check/core/actor_system/actor.py", line 1109, in initialize
    raise timeout_error
vibe_check.core.actor_system.exceptions.ActorTimeoutError: Actor file_actor_7: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds

2025-05-11 16:45:06,601 - vibe_check_actor_initializer - DEBUG - [ACTOR INIT] Setting actor file_actor_7 state to FAILED
2025-05-11 16:45:06,601 - vibe_check_actor_initializer - ERROR - Invalid state transition for actor file_actor_7: stopped -> failed
2025-05-11 16:45:06,601 - vibe_check_actor_initializer - DEBUG - Valid transitions from stopped are: ['initializing', 'created']
2025-05-11 16:45:06,601 - vibe_check_actor_initializer - WARNING - Forcing transition to failed state for actor file_actor_7
2025-05-11 16:45:06,601 - vibe_check_actor_initializer - INFO - Actor file_actor_7 state changed: stopped -> failed
2025-05-11 16:45:06,601 - vibe_check_actor_initializer - ERROR - Actor file_actor_7 failed: Actor file_actor_7 failed during initialize phase (state: stopped): Actor file_actor_7: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds
Original error: Actor file_actor_7: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds
2025-05-11 16:45:06,601 - vibe_check_actor_lifecycle_manager - WARNING - Failed to initialize actor file_actor_7 (attempt 1/3): Actor file_actor_7: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds
2025-05-11 16:45:06,601 - vibe_check_initialization_tracker - INFO - Actor file_actor_7 initialization_start
2025-05-11 16:45:06,601 - vibe_check_initialization_tracker - INFO - Actor file_actor_7 initialization_start
2025-05-11 16:45:06,602 - vibe_check_initialization_tracker - INFO - Actor file_actor_7 initialization_complete
2025-05-11 16:45:06,602 - vibe_check_initialization_tracker - INFO - Actor file_actor_7 initialization_complete
2025-05-11 16:45:06,602 - vibe_check_initialization_tracker - ERROR - Actor file_actor_7 failed: Actor file_actor_7: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds
2025-05-11 16:45:06,702 - vibe_check_actor_lifecycle_manager - INFO - Initializing actor file_actor_7 (type: file)
2025-05-11 16:45:06,703 - vibe_check_actor_initializer - INFO - [ACTOR INIT] Starting initialization of actor file_actor_7
2025-05-11 16:45:06,703 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting registry for actor file_actor_7
2025-05-11 16:45:06,703 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting actor file_actor_7 from registry
2025-05-11 16:45:06,703 - vibe_check_actor_initializer - WARNING - [ACTOR REGISTRY] Actor file_actor_7 not found in registry
2025-05-11 16:45:06,703 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Available actors in registry: ['file_actor_8', 'file_actor_9', 'file_actor_10', 'file_actor_11', 'file_actor_12', 'file_actor_13', 'project_actor']
2025-05-11 16:45:06,703 - vibe_check_actor_initializer - WARNING - [ACTOR INIT] Actor file_actor_7 not found in registry, retrying (1/3)
2025-05-11 16:45:06,803 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting registry for actor file_actor_7
2025-05-11 16:45:06,803 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting actor file_actor_7 from registry
2025-05-11 16:45:06,803 - vibe_check_actor_initializer - WARNING - [ACTOR REGISTRY] Actor file_actor_7 not found in registry
2025-05-11 16:45:06,803 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Available actors in registry: ['file_actor_8', 'file_actor_9', 'file_actor_10', 'file_actor_11', 'file_actor_12', 'file_actor_13', 'project_actor']
2025-05-11 16:45:06,803 - vibe_check_actor_initializer - WARNING - [ACTOR INIT] Actor file_actor_7 not found in registry, retrying (2/3)
2025-05-11 16:45:07,004 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting registry for actor file_actor_7
2025-05-11 16:45:07,004 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting actor file_actor_7 from registry
2025-05-11 16:45:07,004 - vibe_check_actor_initializer - WARNING - [ACTOR REGISTRY] Actor file_actor_7 not found in registry
2025-05-11 16:45:07,004 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Available actors in registry: ['file_actor_8', 'file_actor_9', 'file_actor_10', 'file_actor_11', 'file_actor_12', 'file_actor_13', 'project_actor']
2025-05-11 16:45:07,004 - vibe_check_actor_initializer - ERROR - [ACTOR INIT] Actor file_actor_7 not found in registry after 3 attempts
2025-05-11 16:45:07,005 - vibe_check_actor_lifecycle_manager - WARNING - Failed to initialize actor file_actor_7 (attempt 2/3): Actor file_actor_7 not found in registry after 3 attempts
2025-05-11 16:45:07,206 - vibe_check_actor_lifecycle_manager - INFO - Initializing actor file_actor_7 (type: file)
2025-05-11 16:45:07,206 - vibe_check_actor_initializer - INFO - [ACTOR INIT] Starting initialization of actor file_actor_7
2025-05-11 16:45:07,206 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting registry for actor file_actor_7
2025-05-11 16:45:07,206 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting actor file_actor_7 from registry
2025-05-11 16:45:07,207 - vibe_check_actor_initializer - WARNING - [ACTOR REGISTRY] Actor file_actor_7 not found in registry
2025-05-11 16:45:07,207 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Available actors in registry: ['file_actor_8', 'file_actor_9', 'file_actor_10', 'file_actor_11', 'file_actor_12', 'file_actor_13', 'project_actor']
2025-05-11 16:45:07,207 - vibe_check_actor_initializer - WARNING - [ACTOR INIT] Actor file_actor_7 not found in registry, retrying (1/3)
2025-05-11 16:45:07,308 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting registry for actor file_actor_7
2025-05-11 16:45:07,308 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting actor file_actor_7 from registry
2025-05-11 16:45:07,308 - vibe_check_actor_initializer - WARNING - [ACTOR REGISTRY] Actor file_actor_7 not found in registry
2025-05-11 16:45:07,308 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Available actors in registry: ['file_actor_8', 'file_actor_9', 'file_actor_10', 'file_actor_11', 'file_actor_12', 'file_actor_13', 'project_actor']
2025-05-11 16:45:07,308 - vibe_check_actor_initializer - WARNING - [ACTOR INIT] Actor file_actor_7 not found in registry, retrying (2/3)
2025-05-11 16:45:07,509 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting registry for actor file_actor_7
2025-05-11 16:45:07,509 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting actor file_actor_7 from registry
2025-05-11 16:45:07,510 - vibe_check_actor_initializer - WARNING - [ACTOR REGISTRY] Actor file_actor_7 not found in registry
2025-05-11 16:45:07,510 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Available actors in registry: ['file_actor_8', 'file_actor_9', 'file_actor_10', 'file_actor_11', 'file_actor_12', 'file_actor_13', 'project_actor']
2025-05-11 16:45:07,510 - vibe_check_actor_initializer - ERROR - [ACTOR INIT] Actor file_actor_7 not found in registry after 3 attempts
2025-05-11 16:45:07,510 - vibe_check_actor_lifecycle_manager - ERROR - Failed to initialize actor file_actor_7 after 3 attempts: Actor file_actor_7 not found in registry after 3 attempts
2025-05-11 16:45:07,510 - vibe_check_actor_lifecycle_manager - ERROR - Traceback (most recent call last):
  File "/Users/<USER>/Lokalne_Kody/PAT_project_analysis/vibe_check/core/orchestration/actor_lifecycle_manager.py", line 174, in _initialize_all_actors
    await initializer.initialize_actor(actor_id)
  File "/Users/<USER>/Lokalne_Kody/PAT_project_analysis/vibe_check/core/actor_system/actor_initializer.py", line 883, in initialize_actor
    raise ValueError(error_msg)
ValueError: Actor file_actor_7 not found in registry after 3 attempts

2025-05-11 16:45:07,510 - vibe_check_actor_lifecycle_manager - INFO - Initializing actor file_actor_8 (type: file)
2025-05-11 16:45:07,510 - vibe_check_actor_initializer - INFO - [ACTOR INIT] Starting initialization of actor file_actor_8
2025-05-11 16:45:07,510 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting registry for actor file_actor_8
2025-05-11 16:45:07,510 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting actor file_actor_8 from registry
2025-05-11 16:45:07,510 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Successfully retrieved actor file_actor_8 from registry
2025-05-11 16:45:07,510 - vibe_check_actor_initializer - INFO - [ACTOR INIT] Actor file_actor_8 is of type file
2025-05-11 16:45:07,510 - vibe_check_actor_initializer - DEBUG - [ACTOR INIT] Setting actor file_actor_8 state to INITIALIZING
2025-05-11 16:45:07,510 - vibe_check_actor_initializer - INFO - Actor file_actor_8 state changed: created -> initializing
2025-05-11 16:45:07,510 - vibe_check_actor_initializer - DEBUG - [ACTOR INIT] Calling initialize() method on actor file_actor_8
2025-05-11 16:45:07,510 - vibe_check_actor_system - INFO - Actor file_actor_8 is already registered with initialization manager
2025-05-11 16:45:07,510 - vibe_check_actor_system - INFO - Actor file_actor_8 is already registered with legacy initializer
2025-05-11 16:45:07,510 - vibe_check_actor_initialization - INFO - Actor file_actor_8 state changed: created -> initializing
2025-05-11 16:45:07,510 - vibe_check_actor_initializer - DEBUG - Actor file_actor_8 is already in state initializing, no change needed
2025-05-11 16:45:07,510 - vibe_check_initialization_tracker - INFO - Actor file_actor_8 initialization_start
2025-05-11 16:45:07,511 - vibe_check_actor_system - DEBUG - Actor file_actor_8 has no _initialize method
2025-05-11 16:45:07,511 - vibe_check_actor_initialization - INFO - Actor file_actor_8 state changed: initializing -> initialized
2025-05-11 16:45:07,511 - vibe_check_actor_initializer - INFO - Actor file_actor_8 state changed: initializing -> initialized
2025-05-11 16:45:07,511 - vibe_check_initialization_tracker - INFO - Actor file_actor_8 initialization_complete
2025-05-11 16:45:07,511 - vibe_check_actor_system - INFO - Actor file_actor_8 initialized successfully
2025-05-11 16:45:07,511 - vibe_check_actor_system - INFO - Actor file_actor_8 is already registered with initialization manager
2025-05-11 16:45:07,511 - vibe_check_actor_system - INFO - Actor file_actor_8 is already registered with legacy initializer
2025-05-11 16:45:07,511 - vibe_check_actor_initializer - INFO - Actor file_actor_8 state changed: REGISTERED -> INITIALIZING
2025-05-11 16:45:07,511 - vibe_check_actor_system.file_actor_8 - INFO - State transition: REGISTERED -> INITIALIZING
2025-05-11 16:45:07,511 - vibe_check_actor_system.file_actor_8 - DEBUG - State transition details: {'phase': 'initialize'}
2025-05-11 16:45:07,511 - vibe_check_actor_system.file_actor_8 - DEBUG - Operation 'state_transition_REGISTERED_to_INITIALIZING' took 0.000062 seconds
2025-05-11 16:45:07,511 - vibe_check_actor_system.initializer - DEBUG - Component state: {'actor_id': 'file_actor_8', 'state': 'INITIALIZING', 'previous_state': 'REGISTERED', 'timestamp': 1746974707.511169, 'details': {'phase': 'initialize'}}
2025-05-11 16:45:07,511 - vibe_check_actor_registry - WARNING - Actor file_actor_8 already registered, updating
2025-05-11 16:45:07,511 - vibe_check_actor_registry - INFO - Registered actor file_actor_8 of type file
2025-05-11 16:45:07,511 - vibe_check_actor_system - DEBUG - Actor file_actor_8 has no _initialize method
2025-05-11 16:45:07,511 - vibe_check_actor_initializer - INFO - Actor file_actor_8 state changed: INITIALIZING -> INITIALIZED
2025-05-11 16:45:07,511 - vibe_check_actor_system.file_actor_8 - INFO - State transition: INITIALIZING -> INITIALIZED
2025-05-11 16:45:07,511 - vibe_check_actor_system.file_actor_8 - DEBUG - State transition details: {'phase': 'initialize'}
2025-05-11 16:45:07,511 - vibe_check_actor_system.file_actor_8 - DEBUG - Operation 'state_transition_INITIALIZING_to_INITIALIZED' took 0.000047 seconds
2025-05-11 16:45:07,511 - vibe_check_actor_system.initializer - DEBUG - Component state: {'actor_id': 'file_actor_8', 'state': 'INITIALIZED', 'previous_state': 'INITIALIZING', 'timestamp': 1746974707.511345, 'details': {'phase': 'initialize'}}
2025-05-11 16:45:07,511 - vibe_check_synchronization - DEBUG - Actor file_actor_8 reached synchronization point initialization_complete
2025-05-11 16:45:07,511 - vibe_check_actor_system - INFO - Actor file_actor_8 initialized successfully
2025-05-11 16:45:07,511 - vibe_check_actor_system.actor.file_actor_8 - ERROR - Error during state transition to FAILED: Actor file_actor_8: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds
2025-05-11 16:45:07,511 - vibe_check_actor_system.actor.file_actor_8 - ERROR - Traceback:
NoneType: None

2025-05-11 16:45:07,511 - vibe_check_actor_initializer - INFO - Actor file_actor_8 state changed: INITIALIZED -> FAILED
2025-05-11 16:45:07,511 - vibe_check_actor_system.file_actor_8 - INFO - State transition: INITIALIZED -> FAILED
2025-05-11 16:45:07,511 - vibe_check_actor_system.file_actor_8 - DEBUG - State transition details: {'phase': 'initialize'}
2025-05-11 16:45:07,511 - vibe_check_actor_system.file_actor_8 - DEBUG - Operation 'state_transition_INITIALIZED_to_FAILED' took 0.000164 seconds
2025-05-11 16:45:07,511 - vibe_check_actor_system.initializer - DEBUG - Component state: {'actor_id': 'file_actor_8', 'state': 'FAILED', 'previous_state': 'INITIALIZED', 'timestamp': 1746974707.511501, 'details': {'phase': 'initialize'}}
2025-05-11 16:45:07,511 - vibe_check_actor_initializer - DEBUG - Cleaned up resources for actor file_actor_8
2025-05-11 16:45:07,511 - vibe_check_actor_system - INFO - Cleaning up resources for actor file_actor_8
2025-05-11 16:45:07,511 - vibe_check_actor_system - INFO - Cleared mailbox for actor file_actor_8
2025-05-11 16:45:07,511 - vibe_check_actor_registry - INFO - Unregistered actor file_actor_8
2025-05-11 16:45:07,511 - vibe_check_actor_system - INFO - Unregistered actor file_actor_8 from registry during cleanup
2025-05-11 16:45:07,511 - vibe_check_actor_initialization - INFO - Actor file_actor_8 state changed: initialized -> stopped
2025-05-11 16:45:07,511 - vibe_check_actor_system - INFO - Set actor file_actor_8 state to STOPPED in initialization manager
2025-05-11 16:45:07,511 - vibe_check_actor_initializer - ERROR - Invalid state transition for actor file_actor_8: initialized -> stopped
2025-05-11 16:45:07,511 - vibe_check_actor_initializer - DEBUG - Valid transitions from initialized are: ['stopping', 'starting', 'rollback', 'failed']
2025-05-11 16:45:07,511 - vibe_check_actor_initializer - WARNING - Forcing transition to stopped state for actor file_actor_8
2025-05-11 16:45:07,511 - vibe_check_actor_initializer - INFO - Actor file_actor_8 state changed: initialized -> stopped
2025-05-11 16:45:07,511 - vibe_check_actor_system - INFO - Set actor file_actor_8 state to STOPPED in legacy initializer
2025-05-11 16:45:07,511 - vibe_check_actor_system - INFO - Cleaned up resources for actor file_actor_8
2025-05-11 16:45:07,511 - vibe_check_actor_system - ERROR - Error in initialization process for actor file_actor_8: Actor file_actor_8: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds
2025-05-11 16:45:07,511 - vibe_check_actor_initializer - ERROR - Actor file_actor_8: Invalid state transition: FAILED -> FAILED
2025-05-11 16:45:07,511 - vibe_check_actor_system - ERROR - Error rolling back actor file_actor_8: Actor file_actor_8: Invalid state transition: FAILED -> FAILED (state: FAILED)
2025-05-11 16:45:07,511 - vibe_check_actor_system - INFO - Cleaning up resources for actor file_actor_8
2025-05-11 16:45:07,511 - vibe_check_actor_system - INFO - Cleared mailbox for actor file_actor_8
2025-05-11 16:45:07,511 - vibe_check_actor_registry - WARNING - Actor file_actor_8 not registered, cannot unregister
2025-05-11 16:45:07,512 - vibe_check_actor_system - INFO - Unregistered actor file_actor_8 from registry during cleanup
2025-05-11 16:45:07,512 - vibe_check_actor_initialization - DEBUG - Actor file_actor_8 is already in state stopped, no change needed
2025-05-11 16:45:07,512 - vibe_check_actor_system - INFO - Set actor file_actor_8 state to STOPPED in initialization manager
2025-05-11 16:45:07,512 - vibe_check_actor_initializer - DEBUG - Actor file_actor_8 is already in state stopped, no change needed
2025-05-11 16:45:07,512 - vibe_check_actor_initializer - INFO - Actor file_actor_8 state changed: stopped -> stopped
2025-05-11 16:45:07,512 - vibe_check_actor_system - INFO - Set actor file_actor_8 state to STOPPED in legacy initializer
2025-05-11 16:45:07,512 - vibe_check_actor_system - INFO - Cleaned up resources for actor file_actor_8
2025-05-11 16:45:07,512 - vibe_check_actor_initializer - ERROR - [ACTOR INIT] Failed to initialize actor file_actor_8: Actor file_actor_8: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds
2025-05-11 16:45:07,512 - vibe_check_actor_initializer - ERROR - [ACTOR INIT] Traceback for actor file_actor_8:
Traceback (most recent call last):
  File "/Users/<USER>/Lokalne_Kody/PAT_project_analysis/vibe_check/core/actor_system/actor_initializer.py", line 936, in initialize_actor
    await asyncio.wait_for(actor.initialize(), timeout)
  File "/Users/<USER>/.pyenv/versions/3.13.3/lib/python3.13/asyncio/tasks.py", line 507, in wait_for
    return await fut
           ^^^^^^^^^
  File "/Users/<USER>/Lokalne_Kody/PAT_project_analysis/vibe_check/core/actor_system/actor.py", line 1109, in initialize
    raise timeout_error
vibe_check.core.actor_system.exceptions.ActorTimeoutError: Actor file_actor_8: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds

2025-05-11 16:45:07,512 - vibe_check_actor_initializer - DEBUG - [ACTOR INIT] Setting actor file_actor_8 state to FAILED
2025-05-11 16:45:07,512 - vibe_check_actor_initializer - ERROR - Invalid state transition for actor file_actor_8: stopped -> failed
2025-05-11 16:45:07,512 - vibe_check_actor_initializer - DEBUG - Valid transitions from stopped are: ['initializing', 'created']
2025-05-11 16:45:07,512 - vibe_check_actor_initializer - WARNING - Forcing transition to failed state for actor file_actor_8
2025-05-11 16:45:07,512 - vibe_check_actor_initializer - INFO - Actor file_actor_8 state changed: stopped -> failed
2025-05-11 16:45:07,512 - vibe_check_actor_initializer - ERROR - Actor file_actor_8 failed: Actor file_actor_8 failed during initialize phase (state: stopped): Actor file_actor_8: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds
Original error: Actor file_actor_8: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds
2025-05-11 16:45:07,512 - vibe_check_actor_lifecycle_manager - WARNING - Failed to initialize actor file_actor_8 (attempt 1/3): Actor file_actor_8: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds
2025-05-11 16:45:07,512 - vibe_check_initialization_tracker - INFO - Actor file_actor_8 initialization_start
2025-05-11 16:45:07,512 - vibe_check_initialization_tracker - INFO - Actor file_actor_8 initialization_start
2025-05-11 16:45:07,512 - vibe_check_initialization_tracker - INFO - Actor file_actor_8 initialization_complete
2025-05-11 16:45:07,512 - vibe_check_initialization_tracker - INFO - Actor file_actor_8 initialization_complete
2025-05-11 16:45:07,513 - vibe_check_initialization_tracker - ERROR - Actor file_actor_8 failed: Actor file_actor_8: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds
2025-05-11 16:45:07,613 - vibe_check_actor_lifecycle_manager - INFO - Initializing actor file_actor_8 (type: file)
2025-05-11 16:45:07,613 - vibe_check_actor_initializer - INFO - [ACTOR INIT] Starting initialization of actor file_actor_8
2025-05-11 16:45:07,613 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting registry for actor file_actor_8
2025-05-11 16:45:07,613 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting actor file_actor_8 from registry
2025-05-11 16:45:07,613 - vibe_check_actor_initializer - WARNING - [ACTOR REGISTRY] Actor file_actor_8 not found in registry
2025-05-11 16:45:07,613 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Available actors in registry: ['file_actor_9', 'file_actor_10', 'file_actor_11', 'file_actor_12', 'file_actor_13', 'project_actor']
2025-05-11 16:45:07,613 - vibe_check_actor_initializer - WARNING - [ACTOR INIT] Actor file_actor_8 not found in registry, retrying (1/3)
2025-05-11 16:45:07,714 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting registry for actor file_actor_8
2025-05-11 16:45:07,714 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting actor file_actor_8 from registry
2025-05-11 16:45:07,714 - vibe_check_actor_initializer - WARNING - [ACTOR REGISTRY] Actor file_actor_8 not found in registry
2025-05-11 16:45:07,714 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Available actors in registry: ['file_actor_9', 'file_actor_10', 'file_actor_11', 'file_actor_12', 'file_actor_13', 'project_actor']
2025-05-11 16:45:07,714 - vibe_check_actor_initializer - WARNING - [ACTOR INIT] Actor file_actor_8 not found in registry, retrying (2/3)
2025-05-11 16:45:07,916 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting registry for actor file_actor_8
2025-05-11 16:45:07,916 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting actor file_actor_8 from registry
2025-05-11 16:45:07,916 - vibe_check_actor_initializer - WARNING - [ACTOR REGISTRY] Actor file_actor_8 not found in registry
2025-05-11 16:45:07,916 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Available actors in registry: ['file_actor_9', 'file_actor_10', 'file_actor_11', 'file_actor_12', 'file_actor_13', 'project_actor']
2025-05-11 16:45:07,916 - vibe_check_actor_initializer - ERROR - [ACTOR INIT] Actor file_actor_8 not found in registry after 3 attempts
2025-05-11 16:45:07,916 - vibe_check_actor_lifecycle_manager - WARNING - Failed to initialize actor file_actor_8 (attempt 2/3): Actor file_actor_8 not found in registry after 3 attempts
2025-05-11 16:45:08,117 - vibe_check_actor_lifecycle_manager - INFO - Initializing actor file_actor_8 (type: file)
2025-05-11 16:45:08,117 - vibe_check_actor_initializer - INFO - [ACTOR INIT] Starting initialization of actor file_actor_8
2025-05-11 16:45:08,117 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting registry for actor file_actor_8
2025-05-11 16:45:08,117 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting actor file_actor_8 from registry
2025-05-11 16:45:08,117 - vibe_check_actor_initializer - WARNING - [ACTOR REGISTRY] Actor file_actor_8 not found in registry
2025-05-11 16:45:08,117 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Available actors in registry: ['file_actor_9', 'file_actor_10', 'file_actor_11', 'file_actor_12', 'file_actor_13', 'project_actor']
2025-05-11 16:45:08,117 - vibe_check_actor_initializer - WARNING - [ACTOR INIT] Actor file_actor_8 not found in registry, retrying (1/3)
2025-05-11 16:45:08,218 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting registry for actor file_actor_8
2025-05-11 16:45:08,218 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting actor file_actor_8 from registry
2025-05-11 16:45:08,218 - vibe_check_actor_initializer - WARNING - [ACTOR REGISTRY] Actor file_actor_8 not found in registry
2025-05-11 16:45:08,218 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Available actors in registry: ['file_actor_9', 'file_actor_10', 'file_actor_11', 'file_actor_12', 'file_actor_13', 'project_actor']
2025-05-11 16:45:08,218 - vibe_check_actor_initializer - WARNING - [ACTOR INIT] Actor file_actor_8 not found in registry, retrying (2/3)
2025-05-11 16:45:08,420 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting registry for actor file_actor_8
2025-05-11 16:45:08,420 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting actor file_actor_8 from registry
2025-05-11 16:45:08,420 - vibe_check_actor_initializer - WARNING - [ACTOR REGISTRY] Actor file_actor_8 not found in registry
2025-05-11 16:45:08,420 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Available actors in registry: ['file_actor_9', 'file_actor_10', 'file_actor_11', 'file_actor_12', 'file_actor_13', 'project_actor']
2025-05-11 16:45:08,420 - vibe_check_actor_initializer - ERROR - [ACTOR INIT] Actor file_actor_8 not found in registry after 3 attempts
2025-05-11 16:45:08,420 - vibe_check_actor_lifecycle_manager - ERROR - Failed to initialize actor file_actor_8 after 3 attempts: Actor file_actor_8 not found in registry after 3 attempts
2025-05-11 16:45:08,421 - vibe_check_actor_lifecycle_manager - ERROR - Traceback (most recent call last):
  File "/Users/<USER>/Lokalne_Kody/PAT_project_analysis/vibe_check/core/orchestration/actor_lifecycle_manager.py", line 174, in _initialize_all_actors
    await initializer.initialize_actor(actor_id)
  File "/Users/<USER>/Lokalne_Kody/PAT_project_analysis/vibe_check/core/actor_system/actor_initializer.py", line 883, in initialize_actor
    raise ValueError(error_msg)
ValueError: Actor file_actor_8 not found in registry after 3 attempts

2025-05-11 16:45:08,421 - vibe_check_actor_lifecycle_manager - INFO - Initializing actor file_actor_9 (type: file)
2025-05-11 16:45:08,421 - vibe_check_actor_initializer - INFO - [ACTOR INIT] Starting initialization of actor file_actor_9
2025-05-11 16:45:08,421 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting registry for actor file_actor_9
2025-05-11 16:45:08,421 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting actor file_actor_9 from registry
2025-05-11 16:45:08,421 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Successfully retrieved actor file_actor_9 from registry
2025-05-11 16:45:08,421 - vibe_check_actor_initializer - INFO - [ACTOR INIT] Actor file_actor_9 is of type file
2025-05-11 16:45:08,421 - vibe_check_actor_initializer - DEBUG - [ACTOR INIT] Setting actor file_actor_9 state to INITIALIZING
2025-05-11 16:45:08,421 - vibe_check_actor_initializer - INFO - Actor file_actor_9 state changed: created -> initializing
2025-05-11 16:45:08,421 - vibe_check_actor_initializer - DEBUG - [ACTOR INIT] Calling initialize() method on actor file_actor_9
2025-05-11 16:45:08,421 - vibe_check_actor_system - INFO - Actor file_actor_9 is already registered with initialization manager
2025-05-11 16:45:08,421 - vibe_check_actor_system - INFO - Actor file_actor_9 is already registered with legacy initializer
2025-05-11 16:45:08,421 - vibe_check_actor_initialization - INFO - Actor file_actor_9 state changed: created -> initializing
2025-05-11 16:45:08,421 - vibe_check_actor_initializer - DEBUG - Actor file_actor_9 is already in state initializing, no change needed
2025-05-11 16:45:08,421 - vibe_check_initialization_tracker - INFO - Actor file_actor_9 initialization_start
2025-05-11 16:45:08,421 - vibe_check_actor_system - DEBUG - Actor file_actor_9 has no _initialize method
2025-05-11 16:45:08,421 - vibe_check_actor_initialization - INFO - Actor file_actor_9 state changed: initializing -> initialized
2025-05-11 16:45:08,421 - vibe_check_actor_initializer - INFO - Actor file_actor_9 state changed: initializing -> initialized
2025-05-11 16:45:08,421 - vibe_check_initialization_tracker - INFO - Actor file_actor_9 initialization_complete
2025-05-11 16:45:08,421 - vibe_check_actor_system - INFO - Actor file_actor_9 initialized successfully
2025-05-11 16:45:08,421 - vibe_check_actor_system - INFO - Actor file_actor_9 is already registered with initialization manager
2025-05-11 16:45:08,421 - vibe_check_actor_system - INFO - Actor file_actor_9 is already registered with legacy initializer
2025-05-11 16:45:08,421 - vibe_check_actor_initializer - INFO - Actor file_actor_9 state changed: REGISTERED -> INITIALIZING
2025-05-11 16:45:08,421 - vibe_check_actor_system.file_actor_9 - INFO - State transition: REGISTERED -> INITIALIZING
2025-05-11 16:45:08,421 - vibe_check_actor_system.file_actor_9 - DEBUG - State transition details: {'phase': 'initialize'}
2025-05-11 16:45:08,421 - vibe_check_actor_system.file_actor_9 - DEBUG - Operation 'state_transition_REGISTERED_to_INITIALIZING' took 0.000050 seconds
2025-05-11 16:45:08,421 - vibe_check_actor_system.initializer - DEBUG - Component state: {'actor_id': 'file_actor_9', 'state': 'INITIALIZING', 'previous_state': 'REGISTERED', 'timestamp': 1746974708.4218318, 'details': {'phase': 'initialize'}}
2025-05-11 16:45:08,421 - vibe_check_actor_registry - WARNING - Actor file_actor_9 already registered, updating
2025-05-11 16:45:08,421 - vibe_check_actor_registry - INFO - Registered actor file_actor_9 of type file
2025-05-11 16:45:08,421 - vibe_check_actor_system - DEBUG - Actor file_actor_9 has no _initialize method
2025-05-11 16:45:08,422 - vibe_check_actor_initializer - INFO - Actor file_actor_9 state changed: INITIALIZING -> INITIALIZED
2025-05-11 16:45:08,422 - vibe_check_actor_system.file_actor_9 - INFO - State transition: INITIALIZING -> INITIALIZED
2025-05-11 16:45:08,422 - vibe_check_actor_system.file_actor_9 - DEBUG - State transition details: {'phase': 'initialize'}
2025-05-11 16:45:08,422 - vibe_check_actor_system.file_actor_9 - DEBUG - Operation 'state_transition_INITIALIZING_to_INITIALIZED' took 0.000045 seconds
2025-05-11 16:45:08,422 - vibe_check_actor_system.initializer - DEBUG - Component state: {'actor_id': 'file_actor_9', 'state': 'INITIALIZED', 'previous_state': 'INITIALIZING', 'timestamp': 1746974708.421996, 'details': {'phase': 'initialize'}}
2025-05-11 16:45:08,422 - vibe_check_synchronization - DEBUG - Actor file_actor_9 reached synchronization point initialization_complete
2025-05-11 16:45:08,422 - vibe_check_actor_system - INFO - Actor file_actor_9 initialized successfully
2025-05-11 16:45:08,422 - vibe_check_actor_system.actor.file_actor_9 - ERROR - Error during state transition to FAILED: Actor file_actor_9: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds
2025-05-11 16:45:08,422 - vibe_check_actor_system.actor.file_actor_9 - ERROR - Traceback:
NoneType: None

2025-05-11 16:45:08,422 - vibe_check_actor_initializer - INFO - Actor file_actor_9 state changed: INITIALIZED -> FAILED
2025-05-11 16:45:08,422 - vibe_check_actor_system.file_actor_9 - INFO - State transition: INITIALIZED -> FAILED
2025-05-11 16:45:08,422 - vibe_check_actor_system.file_actor_9 - DEBUG - State transition details: {'phase': 'initialize'}
2025-05-11 16:45:08,422 - vibe_check_actor_system.file_actor_9 - DEBUG - Operation 'state_transition_INITIALIZED_to_FAILED' took 0.000125 seconds
2025-05-11 16:45:08,422 - vibe_check_actor_system.initializer - DEBUG - Component state: {'actor_id': 'file_actor_9', 'state': 'FAILED', 'previous_state': 'INITIALIZED', 'timestamp': 1746974708.4221199, 'details': {'phase': 'initialize'}}
2025-05-11 16:45:08,422 - vibe_check_actor_initializer - DEBUG - Cleaned up resources for actor file_actor_9
2025-05-11 16:45:08,422 - vibe_check_actor_system - INFO - Cleaning up resources for actor file_actor_9
2025-05-11 16:45:08,422 - vibe_check_actor_system - INFO - Cleared mailbox for actor file_actor_9
2025-05-11 16:45:08,422 - vibe_check_actor_registry - INFO - Unregistered actor file_actor_9
2025-05-11 16:45:08,422 - vibe_check_actor_system - INFO - Unregistered actor file_actor_9 from registry during cleanup
2025-05-11 16:45:08,422 - vibe_check_actor_initialization - INFO - Actor file_actor_9 state changed: initialized -> stopped
2025-05-11 16:45:08,422 - vibe_check_actor_system - INFO - Set actor file_actor_9 state to STOPPED in initialization manager
2025-05-11 16:45:08,422 - vibe_check_actor_initializer - ERROR - Invalid state transition for actor file_actor_9: initialized -> stopped
2025-05-11 16:45:08,422 - vibe_check_actor_initializer - DEBUG - Valid transitions from initialized are: ['stopping', 'starting', 'rollback', 'failed']
2025-05-11 16:45:08,422 - vibe_check_actor_initializer - WARNING - Forcing transition to stopped state for actor file_actor_9
2025-05-11 16:45:08,422 - vibe_check_actor_initializer - INFO - Actor file_actor_9 state changed: initialized -> stopped
2025-05-11 16:45:08,422 - vibe_check_actor_system - INFO - Set actor file_actor_9 state to STOPPED in legacy initializer
2025-05-11 16:45:08,422 - vibe_check_actor_system - INFO - Cleaned up resources for actor file_actor_9
2025-05-11 16:45:08,422 - vibe_check_actor_system - ERROR - Error in initialization process for actor file_actor_9: Actor file_actor_9: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds
2025-05-11 16:45:08,422 - vibe_check_actor_initializer - ERROR - Actor file_actor_9: Invalid state transition: FAILED -> FAILED
2025-05-11 16:45:08,422 - vibe_check_actor_system - ERROR - Error rolling back actor file_actor_9: Actor file_actor_9: Invalid state transition: FAILED -> FAILED (state: FAILED)
2025-05-11 16:45:08,422 - vibe_check_actor_system - INFO - Cleaning up resources for actor file_actor_9
2025-05-11 16:45:08,422 - vibe_check_actor_system - INFO - Cleared mailbox for actor file_actor_9
2025-05-11 16:45:08,422 - vibe_check_actor_registry - WARNING - Actor file_actor_9 not registered, cannot unregister
2025-05-11 16:45:08,422 - vibe_check_actor_system - INFO - Unregistered actor file_actor_9 from registry during cleanup
2025-05-11 16:45:08,422 - vibe_check_actor_initialization - DEBUG - Actor file_actor_9 is already in state stopped, no change needed
2025-05-11 16:45:08,422 - vibe_check_actor_system - INFO - Set actor file_actor_9 state to STOPPED in initialization manager
2025-05-11 16:45:08,422 - vibe_check_actor_initializer - DEBUG - Actor file_actor_9 is already in state stopped, no change needed
2025-05-11 16:45:08,422 - vibe_check_actor_initializer - INFO - Actor file_actor_9 state changed: stopped -> stopped
2025-05-11 16:45:08,422 - vibe_check_actor_system - INFO - Set actor file_actor_9 state to STOPPED in legacy initializer
2025-05-11 16:45:08,422 - vibe_check_actor_system - INFO - Cleaned up resources for actor file_actor_9
2025-05-11 16:45:08,422 - vibe_check_actor_initializer - ERROR - [ACTOR INIT] Failed to initialize actor file_actor_9: Actor file_actor_9: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds
2025-05-11 16:45:08,423 - vibe_check_actor_initializer - ERROR - [ACTOR INIT] Traceback for actor file_actor_9:
Traceback (most recent call last):
  File "/Users/<USER>/Lokalne_Kody/PAT_project_analysis/vibe_check/core/actor_system/actor_initializer.py", line 936, in initialize_actor
    await asyncio.wait_for(actor.initialize(), timeout)
  File "/Users/<USER>/.pyenv/versions/3.13.3/lib/python3.13/asyncio/tasks.py", line 507, in wait_for
    return await fut
           ^^^^^^^^^
  File "/Users/<USER>/Lokalne_Kody/PAT_project_analysis/vibe_check/core/actor_system/actor.py", line 1109, in initialize
    raise timeout_error
vibe_check.core.actor_system.exceptions.ActorTimeoutError: Actor file_actor_9: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds

2025-05-11 16:45:08,423 - vibe_check_actor_initializer - DEBUG - [ACTOR INIT] Setting actor file_actor_9 state to FAILED
2025-05-11 16:45:08,423 - vibe_check_actor_initializer - ERROR - Invalid state transition for actor file_actor_9: stopped -> failed
2025-05-11 16:45:08,423 - vibe_check_actor_initializer - DEBUG - Valid transitions from stopped are: ['initializing', 'created']
2025-05-11 16:45:08,423 - vibe_check_actor_initializer - WARNING - Forcing transition to failed state for actor file_actor_9
2025-05-11 16:45:08,423 - vibe_check_actor_initializer - INFO - Actor file_actor_9 state changed: stopped -> failed
2025-05-11 16:45:08,423 - vibe_check_actor_initializer - ERROR - Actor file_actor_9 failed: Actor file_actor_9 failed during initialize phase (state: stopped): Actor file_actor_9: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds
Original error: Actor file_actor_9: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds
2025-05-11 16:45:08,423 - vibe_check_actor_lifecycle_manager - WARNING - Failed to initialize actor file_actor_9 (attempt 1/3): Actor file_actor_9: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds
2025-05-11 16:45:08,423 - vibe_check_initialization_tracker - INFO - Actor file_actor_9 initialization_start
2025-05-11 16:45:08,423 - vibe_check_initialization_tracker - INFO - Actor file_actor_9 initialization_start
2025-05-11 16:45:08,424 - vibe_check_initialization_tracker - INFO - Actor file_actor_9 initialization_complete
2025-05-11 16:45:08,424 - vibe_check_initialization_tracker - INFO - Actor file_actor_9 initialization_complete
2025-05-11 16:45:08,424 - vibe_check_initialization_tracker - ERROR - Actor file_actor_9 failed: Actor file_actor_9: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds
2025-05-11 16:45:08,524 - vibe_check_actor_lifecycle_manager - INFO - Initializing actor file_actor_9 (type: file)
2025-05-11 16:45:08,525 - vibe_check_actor_initializer - INFO - [ACTOR INIT] Starting initialization of actor file_actor_9
2025-05-11 16:45:08,525 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting registry for actor file_actor_9
2025-05-11 16:45:08,525 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting actor file_actor_9 from registry
2025-05-11 16:45:08,525 - vibe_check_actor_initializer - WARNING - [ACTOR REGISTRY] Actor file_actor_9 not found in registry
2025-05-11 16:45:08,525 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Available actors in registry: ['file_actor_10', 'file_actor_11', 'file_actor_12', 'file_actor_13', 'project_actor']
2025-05-11 16:45:08,525 - vibe_check_actor_initializer - WARNING - [ACTOR INIT] Actor file_actor_9 not found in registry, retrying (1/3)
2025-05-11 16:45:08,626 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting registry for actor file_actor_9
2025-05-11 16:45:08,626 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting actor file_actor_9 from registry
2025-05-11 16:45:08,626 - vibe_check_actor_initializer - WARNING - [ACTOR REGISTRY] Actor file_actor_9 not found in registry
2025-05-11 16:45:08,626 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Available actors in registry: ['file_actor_10', 'file_actor_11', 'file_actor_12', 'file_actor_13', 'project_actor']
2025-05-11 16:45:08,626 - vibe_check_actor_initializer - WARNING - [ACTOR INIT] Actor file_actor_9 not found in registry, retrying (2/3)
2025-05-11 16:45:08,827 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting registry for actor file_actor_9
2025-05-11 16:45:08,827 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting actor file_actor_9 from registry
2025-05-11 16:45:08,827 - vibe_check_actor_initializer - WARNING - [ACTOR REGISTRY] Actor file_actor_9 not found in registry
2025-05-11 16:45:08,827 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Available actors in registry: ['file_actor_10', 'file_actor_11', 'file_actor_12', 'file_actor_13', 'project_actor']
2025-05-11 16:45:08,827 - vibe_check_actor_initializer - ERROR - [ACTOR INIT] Actor file_actor_9 not found in registry after 3 attempts
2025-05-11 16:45:08,827 - vibe_check_actor_lifecycle_manager - WARNING - Failed to initialize actor file_actor_9 (attempt 2/3): Actor file_actor_9 not found in registry after 3 attempts
2025-05-11 16:45:09,029 - vibe_check_actor_lifecycle_manager - INFO - Initializing actor file_actor_9 (type: file)
2025-05-11 16:45:09,029 - vibe_check_actor_initializer - INFO - [ACTOR INIT] Starting initialization of actor file_actor_9
2025-05-11 16:45:09,029 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting registry for actor file_actor_9
2025-05-11 16:45:09,029 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting actor file_actor_9 from registry
2025-05-11 16:45:09,029 - vibe_check_actor_initializer - WARNING - [ACTOR REGISTRY] Actor file_actor_9 not found in registry
2025-05-11 16:45:09,029 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Available actors in registry: ['file_actor_10', 'file_actor_11', 'file_actor_12', 'file_actor_13', 'project_actor']
2025-05-11 16:45:09,029 - vibe_check_actor_initializer - WARNING - [ACTOR INIT] Actor file_actor_9 not found in registry, retrying (1/3)
2025-05-11 16:45:09,130 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting registry for actor file_actor_9
2025-05-11 16:45:09,130 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting actor file_actor_9 from registry
2025-05-11 16:45:09,130 - vibe_check_actor_initializer - WARNING - [ACTOR REGISTRY] Actor file_actor_9 not found in registry
2025-05-11 16:45:09,130 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Available actors in registry: ['file_actor_10', 'file_actor_11', 'file_actor_12', 'file_actor_13', 'project_actor']
2025-05-11 16:45:09,130 - vibe_check_actor_initializer - WARNING - [ACTOR INIT] Actor file_actor_9 not found in registry, retrying (2/3)
2025-05-11 16:45:09,331 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting registry for actor file_actor_9
2025-05-11 16:45:09,332 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting actor file_actor_9 from registry
2025-05-11 16:45:09,332 - vibe_check_actor_initializer - WARNING - [ACTOR REGISTRY] Actor file_actor_9 not found in registry
2025-05-11 16:45:09,332 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Available actors in registry: ['file_actor_10', 'file_actor_11', 'file_actor_12', 'file_actor_13', 'project_actor']
2025-05-11 16:45:09,332 - vibe_check_actor_initializer - ERROR - [ACTOR INIT] Actor file_actor_9 not found in registry after 3 attempts
2025-05-11 16:45:09,332 - vibe_check_actor_lifecycle_manager - ERROR - Failed to initialize actor file_actor_9 after 3 attempts: Actor file_actor_9 not found in registry after 3 attempts
2025-05-11 16:45:09,332 - vibe_check_actor_lifecycle_manager - ERROR - Traceback (most recent call last):
  File "/Users/<USER>/Lokalne_Kody/PAT_project_analysis/vibe_check/core/orchestration/actor_lifecycle_manager.py", line 174, in _initialize_all_actors
    await initializer.initialize_actor(actor_id)
  File "/Users/<USER>/Lokalne_Kody/PAT_project_analysis/vibe_check/core/actor_system/actor_initializer.py", line 883, in initialize_actor
    raise ValueError(error_msg)
ValueError: Actor file_actor_9 not found in registry after 3 attempts

2025-05-11 16:45:09,332 - vibe_check_actor_lifecycle_manager - INFO - Initializing actor file_actor_10 (type: file)
2025-05-11 16:45:09,332 - vibe_check_actor_initializer - INFO - [ACTOR INIT] Starting initialization of actor file_actor_10
2025-05-11 16:45:09,332 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting registry for actor file_actor_10
2025-05-11 16:45:09,332 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting actor file_actor_10 from registry
2025-05-11 16:45:09,332 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Successfully retrieved actor file_actor_10 from registry
2025-05-11 16:45:09,332 - vibe_check_actor_initializer - INFO - [ACTOR INIT] Actor file_actor_10 is of type file
2025-05-11 16:45:09,332 - vibe_check_actor_initializer - DEBUG - [ACTOR INIT] Setting actor file_actor_10 state to INITIALIZING
2025-05-11 16:45:09,332 - vibe_check_actor_initializer - INFO - Actor file_actor_10 state changed: created -> initializing
2025-05-11 16:45:09,332 - vibe_check_actor_initializer - DEBUG - [ACTOR INIT] Calling initialize() method on actor file_actor_10
2025-05-11 16:45:09,332 - vibe_check_actor_system - INFO - Actor file_actor_10 is already registered with initialization manager
2025-05-11 16:45:09,332 - vibe_check_actor_system - INFO - Actor file_actor_10 is already registered with legacy initializer
2025-05-11 16:45:09,332 - vibe_check_actor_initialization - INFO - Actor file_actor_10 state changed: created -> initializing
2025-05-11 16:45:09,332 - vibe_check_actor_initializer - DEBUG - Actor file_actor_10 is already in state initializing, no change needed
2025-05-11 16:45:09,332 - vibe_check_initialization_tracker - INFO - Actor file_actor_10 initialization_start
2025-05-11 16:45:09,332 - vibe_check_actor_system - DEBUG - Actor file_actor_10 has no _initialize method
2025-05-11 16:45:09,332 - vibe_check_actor_initialization - INFO - Actor file_actor_10 state changed: initializing -> initialized
2025-05-11 16:45:09,332 - vibe_check_actor_initializer - INFO - Actor file_actor_10 state changed: initializing -> initialized
2025-05-11 16:45:09,332 - vibe_check_initialization_tracker - INFO - Actor file_actor_10 initialization_complete
2025-05-11 16:45:09,332 - vibe_check_actor_system - INFO - Actor file_actor_10 initialized successfully
2025-05-11 16:45:09,332 - vibe_check_actor_system - INFO - Actor file_actor_10 is already registered with initialization manager
2025-05-11 16:45:09,332 - vibe_check_actor_system - INFO - Actor file_actor_10 is already registered with legacy initializer
2025-05-11 16:45:09,332 - vibe_check_actor_initializer - INFO - Actor file_actor_10 state changed: REGISTERED -> INITIALIZING
2025-05-11 16:45:09,332 - vibe_check_actor_system.file_actor_10 - INFO - State transition: REGISTERED -> INITIALIZING
2025-05-11 16:45:09,332 - vibe_check_actor_system.file_actor_10 - DEBUG - State transition details: {'phase': 'initialize'}
2025-05-11 16:45:09,332 - vibe_check_actor_system.file_actor_10 - DEBUG - Operation 'state_transition_REGISTERED_to_INITIALIZING' took 0.000052 seconds
2025-05-11 16:45:09,332 - vibe_check_actor_system.initializer - DEBUG - Component state: {'actor_id': 'file_actor_10', 'state': 'INITIALIZING', 'previous_state': 'REGISTERED', 'timestamp': 1746974709.332897, 'details': {'phase': 'initialize'}}
2025-05-11 16:45:09,332 - vibe_check_actor_registry - WARNING - Actor file_actor_10 already registered, updating
2025-05-11 16:45:09,333 - vibe_check_actor_registry - INFO - Registered actor file_actor_10 of type file
2025-05-11 16:45:09,333 - vibe_check_actor_system - DEBUG - Actor file_actor_10 has no _initialize method
2025-05-11 16:45:09,333 - vibe_check_actor_initializer - INFO - Actor file_actor_10 state changed: INITIALIZING -> INITIALIZED
2025-05-11 16:45:09,333 - vibe_check_actor_system.file_actor_10 - INFO - State transition: INITIALIZING -> INITIALIZED
2025-05-11 16:45:09,333 - vibe_check_actor_system.file_actor_10 - DEBUG - State transition details: {'phase': 'initialize'}
2025-05-11 16:45:09,333 - vibe_check_actor_system.file_actor_10 - DEBUG - Operation 'state_transition_INITIALIZING_to_INITIALIZED' took 0.000035 seconds
2025-05-11 16:45:09,333 - vibe_check_actor_system.initializer - DEBUG - Component state: {'actor_id': 'file_actor_10', 'state': 'INITIALIZED', 'previous_state': 'INITIALIZING', 'timestamp': 1746974709.333045, 'details': {'phase': 'initialize'}}
2025-05-11 16:45:09,333 - vibe_check_synchronization - DEBUG - Actor file_actor_10 reached synchronization point initialization_complete
2025-05-11 16:45:09,333 - vibe_check_actor_system - INFO - Actor file_actor_10 initialized successfully
2025-05-11 16:45:09,333 - vibe_check_actor_system.actor.file_actor_10 - ERROR - Error during state transition to FAILED: Actor file_actor_10: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds
2025-05-11 16:45:09,333 - vibe_check_actor_system.actor.file_actor_10 - ERROR - Traceback:
NoneType: None

2025-05-11 16:45:09,333 - vibe_check_actor_initializer - INFO - Actor file_actor_10 state changed: INITIALIZED -> FAILED
2025-05-11 16:45:09,333 - vibe_check_actor_system.file_actor_10 - INFO - State transition: INITIALIZED -> FAILED
2025-05-11 16:45:09,333 - vibe_check_actor_system.file_actor_10 - DEBUG - State transition details: {'phase': 'initialize'}
2025-05-11 16:45:09,333 - vibe_check_actor_system.file_actor_10 - DEBUG - Operation 'state_transition_INITIALIZED_to_FAILED' took 0.000114 seconds
2025-05-11 16:45:09,333 - vibe_check_actor_system.initializer - DEBUG - Component state: {'actor_id': 'file_actor_10', 'state': 'FAILED', 'previous_state': 'INITIALIZED', 'timestamp': 1746974709.333154, 'details': {'phase': 'initialize'}}
2025-05-11 16:45:09,333 - vibe_check_actor_initializer - DEBUG - Cleaned up resources for actor file_actor_10
2025-05-11 16:45:09,333 - vibe_check_actor_system - INFO - Cleaning up resources for actor file_actor_10
2025-05-11 16:45:09,333 - vibe_check_actor_system - INFO - Cleared mailbox for actor file_actor_10
2025-05-11 16:45:09,333 - vibe_check_actor_registry - INFO - Unregistered actor file_actor_10
2025-05-11 16:45:09,333 - vibe_check_actor_system - INFO - Unregistered actor file_actor_10 from registry during cleanup
2025-05-11 16:45:09,333 - vibe_check_actor_initialization - INFO - Actor file_actor_10 state changed: initialized -> stopped
2025-05-11 16:45:09,333 - vibe_check_actor_system - INFO - Set actor file_actor_10 state to STOPPED in initialization manager
2025-05-11 16:45:09,333 - vibe_check_actor_initializer - ERROR - Invalid state transition for actor file_actor_10: initialized -> stopped
2025-05-11 16:45:09,333 - vibe_check_actor_initializer - DEBUG - Valid transitions from initialized are: ['stopping', 'starting', 'rollback', 'failed']
2025-05-11 16:45:09,333 - vibe_check_actor_initializer - WARNING - Forcing transition to stopped state for actor file_actor_10
2025-05-11 16:45:09,333 - vibe_check_actor_initializer - INFO - Actor file_actor_10 state changed: initialized -> stopped
2025-05-11 16:45:09,333 - vibe_check_actor_system - INFO - Set actor file_actor_10 state to STOPPED in legacy initializer
2025-05-11 16:45:09,333 - vibe_check_actor_system - INFO - Cleaned up resources for actor file_actor_10
2025-05-11 16:45:09,333 - vibe_check_actor_system - ERROR - Error in initialization process for actor file_actor_10: Actor file_actor_10: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds
2025-05-11 16:45:09,333 - vibe_check_actor_initializer - ERROR - Actor file_actor_10: Invalid state transition: FAILED -> FAILED
2025-05-11 16:45:09,333 - vibe_check_actor_system - ERROR - Error rolling back actor file_actor_10: Actor file_actor_10: Invalid state transition: FAILED -> FAILED (state: FAILED)
2025-05-11 16:45:09,333 - vibe_check_actor_system - INFO - Cleaning up resources for actor file_actor_10
2025-05-11 16:45:09,333 - vibe_check_actor_system - INFO - Cleared mailbox for actor file_actor_10
2025-05-11 16:45:09,333 - vibe_check_actor_registry - WARNING - Actor file_actor_10 not registered, cannot unregister
2025-05-11 16:45:09,333 - vibe_check_actor_system - INFO - Unregistered actor file_actor_10 from registry during cleanup
2025-05-11 16:45:09,333 - vibe_check_actor_initialization - DEBUG - Actor file_actor_10 is already in state stopped, no change needed
2025-05-11 16:45:09,333 - vibe_check_actor_system - INFO - Set actor file_actor_10 state to STOPPED in initialization manager
2025-05-11 16:45:09,333 - vibe_check_actor_initializer - DEBUG - Actor file_actor_10 is already in state stopped, no change needed
2025-05-11 16:45:09,333 - vibe_check_actor_initializer - INFO - Actor file_actor_10 state changed: stopped -> stopped
2025-05-11 16:45:09,333 - vibe_check_actor_system - INFO - Set actor file_actor_10 state to STOPPED in legacy initializer
2025-05-11 16:45:09,333 - vibe_check_actor_system - INFO - Cleaned up resources for actor file_actor_10
2025-05-11 16:45:09,333 - vibe_check_actor_initializer - ERROR - [ACTOR INIT] Failed to initialize actor file_actor_10: Actor file_actor_10: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds
2025-05-11 16:45:09,333 - vibe_check_actor_initializer - ERROR - [ACTOR INIT] Traceback for actor file_actor_10:
Traceback (most recent call last):
  File "/Users/<USER>/Lokalne_Kody/PAT_project_analysis/vibe_check/core/actor_system/actor_initializer.py", line 936, in initialize_actor
    await asyncio.wait_for(actor.initialize(), timeout)
  File "/Users/<USER>/.pyenv/versions/3.13.3/lib/python3.13/asyncio/tasks.py", line 507, in wait_for
    return await fut
           ^^^^^^^^^
  File "/Users/<USER>/Lokalne_Kody/PAT_project_analysis/vibe_check/core/actor_system/actor.py", line 1109, in initialize
    raise timeout_error
vibe_check.core.actor_system.exceptions.ActorTimeoutError: Actor file_actor_10: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds

2025-05-11 16:45:09,333 - vibe_check_actor_initializer - DEBUG - [ACTOR INIT] Setting actor file_actor_10 state to FAILED
2025-05-11 16:45:09,333 - vibe_check_actor_initializer - ERROR - Invalid state transition for actor file_actor_10: stopped -> failed
2025-05-11 16:45:09,334 - vibe_check_actor_initializer - DEBUG - Valid transitions from stopped are: ['initializing', 'created']
2025-05-11 16:45:09,334 - vibe_check_actor_initializer - WARNING - Forcing transition to failed state for actor file_actor_10
2025-05-11 16:45:09,334 - vibe_check_actor_initializer - INFO - Actor file_actor_10 state changed: stopped -> failed
2025-05-11 16:45:09,334 - vibe_check_actor_initializer - ERROR - Actor file_actor_10 failed: Actor file_actor_10 failed during initialize phase (state: stopped): Actor file_actor_10: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds
Original error: Actor file_actor_10: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds
2025-05-11 16:45:09,334 - vibe_check_actor_lifecycle_manager - WARNING - Failed to initialize actor file_actor_10 (attempt 1/3): Actor file_actor_10: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds
2025-05-11 16:45:09,334 - vibe_check_initialization_tracker - INFO - Actor file_actor_10 initialization_start
2025-05-11 16:45:09,334 - vibe_check_initialization_tracker - INFO - Actor file_actor_10 initialization_start
2025-05-11 16:45:09,334 - vibe_check_initialization_tracker - INFO - Actor file_actor_10 initialization_complete
2025-05-11 16:45:09,334 - vibe_check_initialization_tracker - INFO - Actor file_actor_10 initialization_complete
2025-05-11 16:45:09,334 - vibe_check_initialization_tracker - ERROR - Actor file_actor_10 failed: Actor file_actor_10: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds
2025-05-11 16:45:09,435 - vibe_check_actor_lifecycle_manager - INFO - Initializing actor file_actor_10 (type: file)
2025-05-11 16:45:09,435 - vibe_check_actor_initializer - INFO - [ACTOR INIT] Starting initialization of actor file_actor_10
2025-05-11 16:45:09,436 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting registry for actor file_actor_10
2025-05-11 16:45:09,436 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting actor file_actor_10 from registry
2025-05-11 16:45:09,436 - vibe_check_actor_initializer - WARNING - [ACTOR REGISTRY] Actor file_actor_10 not found in registry
2025-05-11 16:45:09,436 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Available actors in registry: ['file_actor_11', 'file_actor_12', 'file_actor_13', 'project_actor']
2025-05-11 16:45:09,436 - vibe_check_actor_initializer - WARNING - [ACTOR INIT] Actor file_actor_10 not found in registry, retrying (1/3)
2025-05-11 16:45:09,537 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting registry for actor file_actor_10
2025-05-11 16:45:09,537 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting actor file_actor_10 from registry
2025-05-11 16:45:09,537 - vibe_check_actor_initializer - WARNING - [ACTOR REGISTRY] Actor file_actor_10 not found in registry
2025-05-11 16:45:09,537 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Available actors in registry: ['file_actor_11', 'file_actor_12', 'file_actor_13', 'project_actor']
2025-05-11 16:45:09,537 - vibe_check_actor_initializer - WARNING - [ACTOR INIT] Actor file_actor_10 not found in registry, retrying (2/3)
2025-05-11 16:45:09,738 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting registry for actor file_actor_10
2025-05-11 16:45:09,738 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting actor file_actor_10 from registry
2025-05-11 16:45:09,738 - vibe_check_actor_initializer - WARNING - [ACTOR REGISTRY] Actor file_actor_10 not found in registry
2025-05-11 16:45:09,738 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Available actors in registry: ['file_actor_11', 'file_actor_12', 'file_actor_13', 'project_actor']
2025-05-11 16:45:09,738 - vibe_check_actor_initializer - ERROR - [ACTOR INIT] Actor file_actor_10 not found in registry after 3 attempts
2025-05-11 16:45:09,738 - vibe_check_actor_lifecycle_manager - WARNING - Failed to initialize actor file_actor_10 (attempt 2/3): Actor file_actor_10 not found in registry after 3 attempts
2025-05-11 16:45:09,939 - vibe_check_actor_lifecycle_manager - INFO - Initializing actor file_actor_10 (type: file)
2025-05-11 16:45:09,939 - vibe_check_actor_initializer - INFO - [ACTOR INIT] Starting initialization of actor file_actor_10
2025-05-11 16:45:09,939 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting registry for actor file_actor_10
2025-05-11 16:45:09,940 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting actor file_actor_10 from registry
2025-05-11 16:45:09,940 - vibe_check_actor_initializer - WARNING - [ACTOR REGISTRY] Actor file_actor_10 not found in registry
2025-05-11 16:45:09,940 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Available actors in registry: ['file_actor_11', 'file_actor_12', 'file_actor_13', 'project_actor']
2025-05-11 16:45:09,940 - vibe_check_actor_initializer - WARNING - [ACTOR INIT] Actor file_actor_10 not found in registry, retrying (1/3)
2025-05-11 16:45:10,041 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting registry for actor file_actor_10
2025-05-11 16:45:10,041 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting actor file_actor_10 from registry
2025-05-11 16:45:10,041 - vibe_check_actor_initializer - WARNING - [ACTOR REGISTRY] Actor file_actor_10 not found in registry
2025-05-11 16:45:10,041 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Available actors in registry: ['file_actor_11', 'file_actor_12', 'file_actor_13', 'project_actor']
2025-05-11 16:45:10,041 - vibe_check_actor_initializer - WARNING - [ACTOR INIT] Actor file_actor_10 not found in registry, retrying (2/3)
2025-05-11 16:45:10,242 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting registry for actor file_actor_10
2025-05-11 16:45:10,242 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting actor file_actor_10 from registry
2025-05-11 16:45:10,242 - vibe_check_actor_initializer - WARNING - [ACTOR REGISTRY] Actor file_actor_10 not found in registry
2025-05-11 16:45:10,242 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Available actors in registry: ['file_actor_11', 'file_actor_12', 'file_actor_13', 'project_actor']
2025-05-11 16:45:10,242 - vibe_check_actor_initializer - ERROR - [ACTOR INIT] Actor file_actor_10 not found in registry after 3 attempts
2025-05-11 16:45:10,242 - vibe_check_actor_lifecycle_manager - ERROR - Failed to initialize actor file_actor_10 after 3 attempts: Actor file_actor_10 not found in registry after 3 attempts
2025-05-11 16:45:10,243 - vibe_check_actor_lifecycle_manager - ERROR - Traceback (most recent call last):
  File "/Users/<USER>/Lokalne_Kody/PAT_project_analysis/vibe_check/core/orchestration/actor_lifecycle_manager.py", line 174, in _initialize_all_actors
    await initializer.initialize_actor(actor_id)
  File "/Users/<USER>/Lokalne_Kody/PAT_project_analysis/vibe_check/core/actor_system/actor_initializer.py", line 883, in initialize_actor
    raise ValueError(error_msg)
ValueError: Actor file_actor_10 not found in registry after 3 attempts

2025-05-11 16:45:10,243 - vibe_check_actor_lifecycle_manager - INFO - Initializing actor file_actor_11 (type: file)
2025-05-11 16:45:10,243 - vibe_check_actor_initializer - INFO - [ACTOR INIT] Starting initialization of actor file_actor_11
2025-05-11 16:45:10,243 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting registry for actor file_actor_11
2025-05-11 16:45:10,243 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting actor file_actor_11 from registry
2025-05-11 16:45:10,243 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Successfully retrieved actor file_actor_11 from registry
2025-05-11 16:45:10,243 - vibe_check_actor_initializer - INFO - [ACTOR INIT] Actor file_actor_11 is of type file
2025-05-11 16:45:10,243 - vibe_check_actor_initializer - DEBUG - [ACTOR INIT] Setting actor file_actor_11 state to INITIALIZING
2025-05-11 16:45:10,243 - vibe_check_actor_initializer - INFO - Actor file_actor_11 state changed: created -> initializing
2025-05-11 16:45:10,243 - vibe_check_actor_initializer - DEBUG - [ACTOR INIT] Calling initialize() method on actor file_actor_11
2025-05-11 16:45:10,243 - vibe_check_actor_system - INFO - Actor file_actor_11 is already registered with initialization manager
2025-05-11 16:45:10,243 - vibe_check_actor_system - INFO - Actor file_actor_11 is already registered with legacy initializer
2025-05-11 16:45:10,243 - vibe_check_actor_initialization - INFO - Actor file_actor_11 state changed: created -> initializing
2025-05-11 16:45:10,243 - vibe_check_actor_initializer - DEBUG - Actor file_actor_11 is already in state initializing, no change needed
2025-05-11 16:45:10,243 - vibe_check_initialization_tracker - INFO - Actor file_actor_11 initialization_start
2025-05-11 16:45:10,243 - vibe_check_actor_system - DEBUG - Actor file_actor_11 has no _initialize method
2025-05-11 16:45:10,243 - vibe_check_actor_initialization - INFO - Actor file_actor_11 state changed: initializing -> initialized
2025-05-11 16:45:10,243 - vibe_check_actor_initializer - INFO - Actor file_actor_11 state changed: initializing -> initialized
2025-05-11 16:45:10,243 - vibe_check_initialization_tracker - INFO - Actor file_actor_11 initialization_complete
2025-05-11 16:45:10,243 - vibe_check_actor_system - INFO - Actor file_actor_11 initialized successfully
2025-05-11 16:45:10,243 - vibe_check_actor_system - INFO - Actor file_actor_11 is already registered with initialization manager
2025-05-11 16:45:10,243 - vibe_check_actor_system - INFO - Actor file_actor_11 is already registered with legacy initializer
2025-05-11 16:45:10,243 - vibe_check_actor_initializer - INFO - Actor file_actor_11 state changed: REGISTERED -> INITIALIZING
2025-05-11 16:45:10,243 - vibe_check_actor_system.file_actor_11 - INFO - State transition: REGISTERED -> INITIALIZING
2025-05-11 16:45:10,243 - vibe_check_actor_system.file_actor_11 - DEBUG - State transition details: {'phase': 'initialize'}
2025-05-11 16:45:10,243 - vibe_check_actor_system.file_actor_11 - DEBUG - Operation 'state_transition_REGISTERED_to_INITIALIZING' took 0.000040 seconds
2025-05-11 16:45:10,243 - vibe_check_actor_system.initializer - DEBUG - Component state: {'actor_id': 'file_actor_11', 'state': 'INITIALIZING', 'previous_state': 'REGISTERED', 'timestamp': 1746974710.2436218, 'details': {'phase': 'initialize'}}
2025-05-11 16:45:10,243 - vibe_check_actor_registry - WARNING - Actor file_actor_11 already registered, updating
2025-05-11 16:45:10,243 - vibe_check_actor_registry - INFO - Registered actor file_actor_11 of type file
2025-05-11 16:45:10,243 - vibe_check_actor_system - DEBUG - Actor file_actor_11 has no _initialize method
2025-05-11 16:45:10,243 - vibe_check_actor_initializer - INFO - Actor file_actor_11 state changed: INITIALIZING -> INITIALIZED
2025-05-11 16:45:10,243 - vibe_check_actor_system.file_actor_11 - INFO - State transition: INITIALIZING -> INITIALIZED
2025-05-11 16:45:10,243 - vibe_check_actor_system.file_actor_11 - DEBUG - State transition details: {'phase': 'initialize'}
2025-05-11 16:45:10,243 - vibe_check_actor_system.file_actor_11 - DEBUG - Operation 'state_transition_INITIALIZING_to_INITIALIZED' took 0.000028 seconds
2025-05-11 16:45:10,243 - vibe_check_actor_system.initializer - DEBUG - Component state: {'actor_id': 'file_actor_11', 'state': 'INITIALIZED', 'previous_state': 'INITIALIZING', 'timestamp': 1746974710.243739, 'details': {'phase': 'initialize'}}
2025-05-11 16:45:10,243 - vibe_check_synchronization - DEBUG - Actor file_actor_11 reached synchronization point initialization_complete
2025-05-11 16:45:10,243 - vibe_check_actor_system - INFO - Actor file_actor_11 initialized successfully
2025-05-11 16:45:10,243 - vibe_check_actor_system.actor.file_actor_11 - ERROR - Error during state transition to FAILED: Actor file_actor_11: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds
2025-05-11 16:45:10,243 - vibe_check_actor_system.actor.file_actor_11 - ERROR - Traceback:
NoneType: None

2025-05-11 16:45:10,243 - vibe_check_actor_initializer - INFO - Actor file_actor_11 state changed: INITIALIZED -> FAILED
2025-05-11 16:45:10,243 - vibe_check_actor_system.file_actor_11 - INFO - State transition: INITIALIZED -> FAILED
2025-05-11 16:45:10,243 - vibe_check_actor_system.file_actor_11 - DEBUG - State transition details: {'phase': 'initialize'}
2025-05-11 16:45:10,243 - vibe_check_actor_system.file_actor_11 - DEBUG - Operation 'state_transition_INITIALIZED_to_FAILED' took 0.000086 seconds
2025-05-11 16:45:10,243 - vibe_check_actor_system.initializer - DEBUG - Component state: {'actor_id': 'file_actor_11', 'state': 'FAILED', 'previous_state': 'INITIALIZED', 'timestamp': 1746974710.243826, 'details': {'phase': 'initialize'}}
2025-05-11 16:45:10,243 - vibe_check_actor_initializer - DEBUG - Cleaned up resources for actor file_actor_11
2025-05-11 16:45:10,243 - vibe_check_actor_system - INFO - Cleaning up resources for actor file_actor_11
2025-05-11 16:45:10,243 - vibe_check_actor_system - INFO - Cleared mailbox for actor file_actor_11
2025-05-11 16:45:10,243 - vibe_check_actor_registry - INFO - Unregistered actor file_actor_11
2025-05-11 16:45:10,243 - vibe_check_actor_system - INFO - Unregistered actor file_actor_11 from registry during cleanup
2025-05-11 16:45:10,243 - vibe_check_actor_initialization - INFO - Actor file_actor_11 state changed: initialized -> stopped
2025-05-11 16:45:10,244 - vibe_check_actor_system - INFO - Set actor file_actor_11 state to STOPPED in initialization manager
2025-05-11 16:45:10,244 - vibe_check_actor_initializer - ERROR - Invalid state transition for actor file_actor_11: initialized -> stopped
2025-05-11 16:45:10,244 - vibe_check_actor_initializer - DEBUG - Valid transitions from initialized are: ['stopping', 'starting', 'rollback', 'failed']
2025-05-11 16:45:10,244 - vibe_check_actor_initializer - WARNING - Forcing transition to stopped state for actor file_actor_11
2025-05-11 16:45:10,244 - vibe_check_actor_initializer - INFO - Actor file_actor_11 state changed: initialized -> stopped
2025-05-11 16:45:10,244 - vibe_check_actor_system - INFO - Set actor file_actor_11 state to STOPPED in legacy initializer
2025-05-11 16:45:10,244 - vibe_check_actor_system - INFO - Cleaned up resources for actor file_actor_11
2025-05-11 16:45:10,244 - vibe_check_actor_system - ERROR - Error in initialization process for actor file_actor_11: Actor file_actor_11: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds
2025-05-11 16:45:10,244 - vibe_check_actor_initializer - ERROR - Actor file_actor_11: Invalid state transition: FAILED -> FAILED
2025-05-11 16:45:10,244 - vibe_check_actor_system - ERROR - Error rolling back actor file_actor_11: Actor file_actor_11: Invalid state transition: FAILED -> FAILED (state: FAILED)
2025-05-11 16:45:10,244 - vibe_check_actor_system - INFO - Cleaning up resources for actor file_actor_11
2025-05-11 16:45:10,244 - vibe_check_actor_system - INFO - Cleared mailbox for actor file_actor_11
2025-05-11 16:45:10,244 - vibe_check_actor_registry - WARNING - Actor file_actor_11 not registered, cannot unregister
2025-05-11 16:45:10,244 - vibe_check_actor_system - INFO - Unregistered actor file_actor_11 from registry during cleanup
2025-05-11 16:45:10,244 - vibe_check_actor_initialization - DEBUG - Actor file_actor_11 is already in state stopped, no change needed
2025-05-11 16:45:10,244 - vibe_check_actor_system - INFO - Set actor file_actor_11 state to STOPPED in initialization manager
2025-05-11 16:45:10,244 - vibe_check_actor_initializer - DEBUG - Actor file_actor_11 is already in state stopped, no change needed
2025-05-11 16:45:10,244 - vibe_check_actor_initializer - INFO - Actor file_actor_11 state changed: stopped -> stopped
2025-05-11 16:45:10,244 - vibe_check_actor_system - INFO - Set actor file_actor_11 state to STOPPED in legacy initializer
2025-05-11 16:45:10,244 - vibe_check_actor_system - INFO - Cleaned up resources for actor file_actor_11
2025-05-11 16:45:10,244 - vibe_check_actor_initializer - ERROR - [ACTOR INIT] Failed to initialize actor file_actor_11: Actor file_actor_11: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds
2025-05-11 16:45:10,244 - vibe_check_actor_initializer - ERROR - [ACTOR INIT] Traceback for actor file_actor_11:
Traceback (most recent call last):
  File "/Users/<USER>/Lokalne_Kody/PAT_project_analysis/vibe_check/core/actor_system/actor_initializer.py", line 936, in initialize_actor
    await asyncio.wait_for(actor.initialize(), timeout)
  File "/Users/<USER>/.pyenv/versions/3.13.3/lib/python3.13/asyncio/tasks.py", line 507, in wait_for
    return await fut
           ^^^^^^^^^
  File "/Users/<USER>/Lokalne_Kody/PAT_project_analysis/vibe_check/core/actor_system/actor.py", line 1109, in initialize
    raise timeout_error
vibe_check.core.actor_system.exceptions.ActorTimeoutError: Actor file_actor_11: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds

2025-05-11 16:45:10,244 - vibe_check_actor_initializer - DEBUG - [ACTOR INIT] Setting actor file_actor_11 state to FAILED
2025-05-11 16:45:10,244 - vibe_check_actor_initializer - ERROR - Invalid state transition for actor file_actor_11: stopped -> failed
2025-05-11 16:45:10,244 - vibe_check_actor_initializer - DEBUG - Valid transitions from stopped are: ['initializing', 'created']
2025-05-11 16:45:10,244 - vibe_check_actor_initializer - WARNING - Forcing transition to failed state for actor file_actor_11
2025-05-11 16:45:10,244 - vibe_check_actor_initializer - INFO - Actor file_actor_11 state changed: stopped -> failed
2025-05-11 16:45:10,244 - vibe_check_actor_initializer - ERROR - Actor file_actor_11 failed: Actor file_actor_11 failed during initialize phase (state: stopped): Actor file_actor_11: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds
Original error: Actor file_actor_11: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds
2025-05-11 16:45:10,244 - vibe_check_actor_lifecycle_manager - WARNING - Failed to initialize actor file_actor_11 (attempt 1/3): Actor file_actor_11: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds
2025-05-11 16:45:10,244 - vibe_check_initialization_tracker - INFO - Actor file_actor_11 initialization_start
2025-05-11 16:45:10,244 - vibe_check_initialization_tracker - INFO - Actor file_actor_11 initialization_start
2025-05-11 16:45:10,244 - vibe_check_initialization_tracker - INFO - Actor file_actor_11 initialization_complete
2025-05-11 16:45:10,244 - vibe_check_initialization_tracker - INFO - Actor file_actor_11 initialization_complete
2025-05-11 16:45:10,244 - vibe_check_initialization_tracker - ERROR - Actor file_actor_11 failed: Actor file_actor_11: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds
2025-05-11 16:45:10,346 - vibe_check_actor_lifecycle_manager - INFO - Initializing actor file_actor_11 (type: file)
2025-05-11 16:45:10,346 - vibe_check_actor_initializer - INFO - [ACTOR INIT] Starting initialization of actor file_actor_11
2025-05-11 16:45:10,347 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting registry for actor file_actor_11
2025-05-11 16:45:10,347 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting actor file_actor_11 from registry
2025-05-11 16:45:10,347 - vibe_check_actor_initializer - WARNING - [ACTOR REGISTRY] Actor file_actor_11 not found in registry
2025-05-11 16:45:10,347 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Available actors in registry: ['file_actor_12', 'file_actor_13', 'project_actor']
2025-05-11 16:45:10,347 - vibe_check_actor_initializer - WARNING - [ACTOR INIT] Actor file_actor_11 not found in registry, retrying (1/3)
2025-05-11 16:45:10,448 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting registry for actor file_actor_11
2025-05-11 16:45:10,448 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting actor file_actor_11 from registry
2025-05-11 16:45:10,448 - vibe_check_actor_initializer - WARNING - [ACTOR REGISTRY] Actor file_actor_11 not found in registry
2025-05-11 16:45:10,448 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Available actors in registry: ['file_actor_12', 'file_actor_13', 'project_actor']
2025-05-11 16:45:10,448 - vibe_check_actor_initializer - WARNING - [ACTOR INIT] Actor file_actor_11 not found in registry, retrying (2/3)
2025-05-11 16:45:10,649 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting registry for actor file_actor_11
2025-05-11 16:45:10,650 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting actor file_actor_11 from registry
2025-05-11 16:45:10,650 - vibe_check_actor_initializer - WARNING - [ACTOR REGISTRY] Actor file_actor_11 not found in registry
2025-05-11 16:45:10,650 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Available actors in registry: ['file_actor_12', 'file_actor_13', 'project_actor']
2025-05-11 16:45:10,650 - vibe_check_actor_initializer - ERROR - [ACTOR INIT] Actor file_actor_11 not found in registry after 3 attempts
2025-05-11 16:45:10,650 - vibe_check_actor_lifecycle_manager - WARNING - Failed to initialize actor file_actor_11 (attempt 2/3): Actor file_actor_11 not found in registry after 3 attempts
2025-05-11 16:45:10,851 - vibe_check_actor_lifecycle_manager - INFO - Initializing actor file_actor_11 (type: file)
2025-05-11 16:45:10,851 - vibe_check_actor_initializer - INFO - [ACTOR INIT] Starting initialization of actor file_actor_11
2025-05-11 16:45:10,851 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting registry for actor file_actor_11
2025-05-11 16:45:10,851 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting actor file_actor_11 from registry
2025-05-11 16:45:10,851 - vibe_check_actor_initializer - WARNING - [ACTOR REGISTRY] Actor file_actor_11 not found in registry
2025-05-11 16:45:10,851 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Available actors in registry: ['file_actor_12', 'file_actor_13', 'project_actor']
2025-05-11 16:45:10,851 - vibe_check_actor_initializer - WARNING - [ACTOR INIT] Actor file_actor_11 not found in registry, retrying (1/3)
2025-05-11 16:45:10,953 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting registry for actor file_actor_11
2025-05-11 16:45:10,953 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting actor file_actor_11 from registry
2025-05-11 16:45:10,953 - vibe_check_actor_initializer - WARNING - [ACTOR REGISTRY] Actor file_actor_11 not found in registry
2025-05-11 16:45:10,953 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Available actors in registry: ['file_actor_12', 'file_actor_13', 'project_actor']
2025-05-11 16:45:10,953 - vibe_check_actor_initializer - WARNING - [ACTOR INIT] Actor file_actor_11 not found in registry, retrying (2/3)
2025-05-11 16:45:11,154 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting registry for actor file_actor_11
2025-05-11 16:45:11,154 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting actor file_actor_11 from registry
2025-05-11 16:45:11,154 - vibe_check_actor_initializer - WARNING - [ACTOR REGISTRY] Actor file_actor_11 not found in registry
2025-05-11 16:45:11,154 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Available actors in registry: ['file_actor_12', 'file_actor_13', 'project_actor']
2025-05-11 16:45:11,154 - vibe_check_actor_initializer - ERROR - [ACTOR INIT] Actor file_actor_11 not found in registry after 3 attempts
2025-05-11 16:45:11,154 - vibe_check_actor_lifecycle_manager - ERROR - Failed to initialize actor file_actor_11 after 3 attempts: Actor file_actor_11 not found in registry after 3 attempts
2025-05-11 16:45:11,155 - vibe_check_actor_lifecycle_manager - ERROR - Traceback (most recent call last):
  File "/Users/<USER>/Lokalne_Kody/PAT_project_analysis/vibe_check/core/orchestration/actor_lifecycle_manager.py", line 174, in _initialize_all_actors
    await initializer.initialize_actor(actor_id)
  File "/Users/<USER>/Lokalne_Kody/PAT_project_analysis/vibe_check/core/actor_system/actor_initializer.py", line 883, in initialize_actor
    raise ValueError(error_msg)
ValueError: Actor file_actor_11 not found in registry after 3 attempts

2025-05-11 16:45:11,155 - vibe_check_actor_lifecycle_manager - INFO - Initializing actor file_actor_12 (type: file)
2025-05-11 16:45:11,155 - vibe_check_actor_initializer - INFO - [ACTOR INIT] Starting initialization of actor file_actor_12
2025-05-11 16:45:11,155 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting registry for actor file_actor_12
2025-05-11 16:45:11,155 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting actor file_actor_12 from registry
2025-05-11 16:45:11,155 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Successfully retrieved actor file_actor_12 from registry
2025-05-11 16:45:11,155 - vibe_check_actor_initializer - INFO - [ACTOR INIT] Actor file_actor_12 is of type file
2025-05-11 16:45:11,155 - vibe_check_actor_initializer - DEBUG - [ACTOR INIT] Setting actor file_actor_12 state to INITIALIZING
2025-05-11 16:45:11,155 - vibe_check_actor_initializer - INFO - Actor file_actor_12 state changed: created -> initializing
2025-05-11 16:45:11,155 - vibe_check_actor_initializer - DEBUG - [ACTOR INIT] Calling initialize() method on actor file_actor_12
2025-05-11 16:45:11,155 - vibe_check_actor_system - INFO - Actor file_actor_12 is already registered with initialization manager
2025-05-11 16:45:11,155 - vibe_check_actor_system - INFO - Actor file_actor_12 is already registered with legacy initializer
2025-05-11 16:45:11,155 - vibe_check_actor_initialization - INFO - Actor file_actor_12 state changed: created -> initializing
2025-05-11 16:45:11,155 - vibe_check_actor_initializer - DEBUG - Actor file_actor_12 is already in state initializing, no change needed
2025-05-11 16:45:11,155 - vibe_check_initialization_tracker - INFO - Actor file_actor_12 initialization_start
2025-05-11 16:45:11,155 - vibe_check_actor_system - DEBUG - Actor file_actor_12 has no _initialize method
2025-05-11 16:45:11,155 - vibe_check_actor_initialization - INFO - Actor file_actor_12 state changed: initializing -> initialized
2025-05-11 16:45:11,155 - vibe_check_actor_initializer - INFO - Actor file_actor_12 state changed: initializing -> initialized
2025-05-11 16:45:11,155 - vibe_check_initialization_tracker - INFO - Actor file_actor_12 initialization_complete
2025-05-11 16:45:11,155 - vibe_check_actor_system - INFO - Actor file_actor_12 initialized successfully
2025-05-11 16:45:11,155 - vibe_check_actor_system - INFO - Actor file_actor_12 is already registered with initialization manager
2025-05-11 16:45:11,155 - vibe_check_actor_system - INFO - Actor file_actor_12 is already registered with legacy initializer
2025-05-11 16:45:11,155 - vibe_check_actor_initializer - INFO - Actor file_actor_12 state changed: REGISTERED -> INITIALIZING
2025-05-11 16:45:11,155 - vibe_check_actor_system.file_actor_12 - INFO - State transition: REGISTERED -> INITIALIZING
2025-05-11 16:45:11,155 - vibe_check_actor_system.file_actor_12 - DEBUG - State transition details: {'phase': 'initialize'}
2025-05-11 16:45:11,155 - vibe_check_actor_system.file_actor_12 - DEBUG - Operation 'state_transition_REGISTERED_to_INITIALIZING' took 0.000059 seconds
2025-05-11 16:45:11,155 - vibe_check_actor_system.initializer - DEBUG - Component state: {'actor_id': 'file_actor_12', 'state': 'INITIALIZING', 'previous_state': 'REGISTERED', 'timestamp': 1746974711.1558459, 'details': {'phase': 'initialize'}}
2025-05-11 16:45:11,155 - vibe_check_actor_registry - WARNING - Actor file_actor_12 already registered, updating
2025-05-11 16:45:11,155 - vibe_check_actor_registry - INFO - Registered actor file_actor_12 of type file
2025-05-11 16:45:11,155 - vibe_check_actor_system - DEBUG - Actor file_actor_12 has no _initialize method
2025-05-11 16:45:11,156 - vibe_check_actor_initializer - INFO - Actor file_actor_12 state changed: INITIALIZING -> INITIALIZED
2025-05-11 16:45:11,156 - vibe_check_actor_system.file_actor_12 - INFO - State transition: INITIALIZING -> INITIALIZED
2025-05-11 16:45:11,156 - vibe_check_actor_system.file_actor_12 - DEBUG - State transition details: {'phase': 'initialize'}
2025-05-11 16:45:11,156 - vibe_check_actor_system.file_actor_12 - DEBUG - Operation 'state_transition_INITIALIZING_to_INITIALIZED' took 0.000031 seconds
2025-05-11 16:45:11,156 - vibe_check_actor_system.initializer - DEBUG - Component state: {'actor_id': 'file_actor_12', 'state': 'INITIALIZED', 'previous_state': 'INITIALIZING', 'timestamp': 1746974711.155997, 'details': {'phase': 'initialize'}}
2025-05-11 16:45:11,156 - vibe_check_synchronization - DEBUG - Actor file_actor_12 reached synchronization point initialization_complete
2025-05-11 16:45:11,156 - vibe_check_actor_system - INFO - Actor file_actor_12 initialized successfully
2025-05-11 16:45:11,156 - vibe_check_actor_system.actor.file_actor_12 - ERROR - Error during state transition to FAILED: Actor file_actor_12: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds
2025-05-11 16:45:11,156 - vibe_check_actor_system.actor.file_actor_12 - ERROR - Traceback:
NoneType: None

2025-05-11 16:45:11,156 - vibe_check_actor_initializer - INFO - Actor file_actor_12 state changed: INITIALIZED -> FAILED
2025-05-11 16:45:11,156 - vibe_check_actor_system.file_actor_12 - INFO - State transition: INITIALIZED -> FAILED
2025-05-11 16:45:11,156 - vibe_check_actor_system.file_actor_12 - DEBUG - State transition details: {'phase': 'initialize'}
2025-05-11 16:45:11,156 - vibe_check_actor_system.file_actor_12 - DEBUG - Operation 'state_transition_INITIALIZED_to_FAILED' took 0.000108 seconds
2025-05-11 16:45:11,156 - vibe_check_actor_system.initializer - DEBUG - Component state: {'actor_id': 'file_actor_12', 'state': 'FAILED', 'previous_state': 'INITIALIZED', 'timestamp': 1746974711.156096, 'details': {'phase': 'initialize'}}
2025-05-11 16:45:11,156 - vibe_check_actor_initializer - DEBUG - Cleaned up resources for actor file_actor_12
2025-05-11 16:45:11,156 - vibe_check_actor_system - INFO - Cleaning up resources for actor file_actor_12
2025-05-11 16:45:11,156 - vibe_check_actor_system - INFO - Cleared mailbox for actor file_actor_12
2025-05-11 16:45:11,156 - vibe_check_actor_registry - INFO - Unregistered actor file_actor_12
2025-05-11 16:45:11,156 - vibe_check_actor_system - INFO - Unregistered actor file_actor_12 from registry during cleanup
2025-05-11 16:45:11,156 - vibe_check_actor_initialization - INFO - Actor file_actor_12 state changed: initialized -> stopped
2025-05-11 16:45:11,156 - vibe_check_actor_system - INFO - Set actor file_actor_12 state to STOPPED in initialization manager
2025-05-11 16:45:11,156 - vibe_check_actor_initializer - ERROR - Invalid state transition for actor file_actor_12: initialized -> stopped
2025-05-11 16:45:11,156 - vibe_check_actor_initializer - DEBUG - Valid transitions from initialized are: ['stopping', 'starting', 'rollback', 'failed']
2025-05-11 16:45:11,156 - vibe_check_actor_initializer - WARNING - Forcing transition to stopped state for actor file_actor_12
2025-05-11 16:45:11,156 - vibe_check_actor_initializer - INFO - Actor file_actor_12 state changed: initialized -> stopped
2025-05-11 16:45:11,156 - vibe_check_actor_system - INFO - Set actor file_actor_12 state to STOPPED in legacy initializer
2025-05-11 16:45:11,156 - vibe_check_actor_system - INFO - Cleaned up resources for actor file_actor_12
2025-05-11 16:45:11,156 - vibe_check_actor_system - ERROR - Error in initialization process for actor file_actor_12: Actor file_actor_12: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds
2025-05-11 16:45:11,156 - vibe_check_actor_initializer - ERROR - Actor file_actor_12: Invalid state transition: FAILED -> FAILED
2025-05-11 16:45:11,156 - vibe_check_actor_system - ERROR - Error rolling back actor file_actor_12: Actor file_actor_12: Invalid state transition: FAILED -> FAILED (state: FAILED)
2025-05-11 16:45:11,156 - vibe_check_actor_system - INFO - Cleaning up resources for actor file_actor_12
2025-05-11 16:45:11,156 - vibe_check_actor_system - INFO - Cleared mailbox for actor file_actor_12
2025-05-11 16:45:11,156 - vibe_check_actor_registry - WARNING - Actor file_actor_12 not registered, cannot unregister
2025-05-11 16:45:11,156 - vibe_check_actor_system - INFO - Unregistered actor file_actor_12 from registry during cleanup
2025-05-11 16:45:11,156 - vibe_check_actor_initialization - DEBUG - Actor file_actor_12 is already in state stopped, no change needed
2025-05-11 16:45:11,156 - vibe_check_actor_system - INFO - Set actor file_actor_12 state to STOPPED in initialization manager
2025-05-11 16:45:11,156 - vibe_check_actor_initializer - DEBUG - Actor file_actor_12 is already in state stopped, no change needed
2025-05-11 16:45:11,156 - vibe_check_actor_initializer - INFO - Actor file_actor_12 state changed: stopped -> stopped
2025-05-11 16:45:11,156 - vibe_check_actor_system - INFO - Set actor file_actor_12 state to STOPPED in legacy initializer
2025-05-11 16:45:11,156 - vibe_check_actor_system - INFO - Cleaned up resources for actor file_actor_12
2025-05-11 16:45:11,156 - vibe_check_actor_initializer - ERROR - [ACTOR INIT] Failed to initialize actor file_actor_12: Actor file_actor_12: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds
2025-05-11 16:45:11,156 - vibe_check_actor_initializer - ERROR - [ACTOR INIT] Traceback for actor file_actor_12:
Traceback (most recent call last):
  File "/Users/<USER>/Lokalne_Kody/PAT_project_analysis/vibe_check/core/actor_system/actor_initializer.py", line 936, in initialize_actor
    await asyncio.wait_for(actor.initialize(), timeout)
  File "/Users/<USER>/.pyenv/versions/3.13.3/lib/python3.13/asyncio/tasks.py", line 507, in wait_for
    return await fut
           ^^^^^^^^^
  File "/Users/<USER>/Lokalne_Kody/PAT_project_analysis/vibe_check/core/actor_system/actor.py", line 1109, in initialize
    raise timeout_error
vibe_check.core.actor_system.exceptions.ActorTimeoutError: Actor file_actor_12: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds

2025-05-11 16:45:11,156 - vibe_check_actor_initializer - DEBUG - [ACTOR INIT] Setting actor file_actor_12 state to FAILED
2025-05-11 16:45:11,156 - vibe_check_actor_initializer - ERROR - Invalid state transition for actor file_actor_12: stopped -> failed
2025-05-11 16:45:11,156 - vibe_check_actor_initializer - DEBUG - Valid transitions from stopped are: ['initializing', 'created']
2025-05-11 16:45:11,156 - vibe_check_actor_initializer - WARNING - Forcing transition to failed state for actor file_actor_12
2025-05-11 16:45:11,156 - vibe_check_actor_initializer - INFO - Actor file_actor_12 state changed: stopped -> failed
2025-05-11 16:45:11,157 - vibe_check_actor_initializer - ERROR - Actor file_actor_12 failed: Actor file_actor_12 failed during initialize phase (state: stopped): Actor file_actor_12: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds
Original error: Actor file_actor_12: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds
2025-05-11 16:45:11,157 - vibe_check_actor_lifecycle_manager - WARNING - Failed to initialize actor file_actor_12 (attempt 1/3): Actor file_actor_12: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds
2025-05-11 16:45:11,157 - vibe_check_initialization_tracker - INFO - Actor file_actor_12 initialization_start
2025-05-11 16:45:11,157 - vibe_check_initialization_tracker - INFO - Actor file_actor_12 initialization_start
2025-05-11 16:45:11,157 - vibe_check_initialization_tracker - INFO - Actor file_actor_12 initialization_complete
2025-05-11 16:45:11,157 - vibe_check_initialization_tracker - INFO - Actor file_actor_12 initialization_complete
2025-05-11 16:45:11,157 - vibe_check_initialization_tracker - ERROR - Actor file_actor_12 failed: Actor file_actor_12: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds
2025-05-11 16:45:11,258 - vibe_check_actor_lifecycle_manager - INFO - Initializing actor file_actor_12 (type: file)
2025-05-11 16:45:11,258 - vibe_check_actor_initializer - INFO - [ACTOR INIT] Starting initialization of actor file_actor_12
2025-05-11 16:45:11,258 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting registry for actor file_actor_12
2025-05-11 16:45:11,258 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting actor file_actor_12 from registry
2025-05-11 16:45:11,258 - vibe_check_actor_initializer - WARNING - [ACTOR REGISTRY] Actor file_actor_12 not found in registry
2025-05-11 16:45:11,258 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Available actors in registry: ['file_actor_13', 'project_actor']
2025-05-11 16:45:11,258 - vibe_check_actor_initializer - WARNING - [ACTOR INIT] Actor file_actor_12 not found in registry, retrying (1/3)
2025-05-11 16:45:11,359 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting registry for actor file_actor_12
2025-05-11 16:45:11,359 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting actor file_actor_12 from registry
2025-05-11 16:45:11,359 - vibe_check_actor_initializer - WARNING - [ACTOR REGISTRY] Actor file_actor_12 not found in registry
2025-05-11 16:45:11,359 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Available actors in registry: ['file_actor_13', 'project_actor']
2025-05-11 16:45:11,359 - vibe_check_actor_initializer - WARNING - [ACTOR INIT] Actor file_actor_12 not found in registry, retrying (2/3)
2025-05-11 16:45:11,560 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting registry for actor file_actor_12
2025-05-11 16:45:11,560 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting actor file_actor_12 from registry
2025-05-11 16:45:11,560 - vibe_check_actor_initializer - WARNING - [ACTOR REGISTRY] Actor file_actor_12 not found in registry
2025-05-11 16:45:11,560 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Available actors in registry: ['file_actor_13', 'project_actor']
2025-05-11 16:45:11,560 - vibe_check_actor_initializer - ERROR - [ACTOR INIT] Actor file_actor_12 not found in registry after 3 attempts
2025-05-11 16:45:11,560 - vibe_check_actor_lifecycle_manager - WARNING - Failed to initialize actor file_actor_12 (attempt 2/3): Actor file_actor_12 not found in registry after 3 attempts
2025-05-11 16:45:11,761 - vibe_check_actor_lifecycle_manager - INFO - Initializing actor file_actor_12 (type: file)
2025-05-11 16:45:11,761 - vibe_check_actor_initializer - INFO - [ACTOR INIT] Starting initialization of actor file_actor_12
2025-05-11 16:45:11,761 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting registry for actor file_actor_12
2025-05-11 16:45:11,761 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting actor file_actor_12 from registry
2025-05-11 16:45:11,761 - vibe_check_actor_initializer - WARNING - [ACTOR REGISTRY] Actor file_actor_12 not found in registry
2025-05-11 16:45:11,761 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Available actors in registry: ['file_actor_13', 'project_actor']
2025-05-11 16:45:11,761 - vibe_check_actor_initializer - WARNING - [ACTOR INIT] Actor file_actor_12 not found in registry, retrying (1/3)
2025-05-11 16:45:11,862 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting registry for actor file_actor_12
2025-05-11 16:45:11,863 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting actor file_actor_12 from registry
2025-05-11 16:45:11,863 - vibe_check_actor_initializer - WARNING - [ACTOR REGISTRY] Actor file_actor_12 not found in registry
2025-05-11 16:45:11,863 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Available actors in registry: ['file_actor_13', 'project_actor']
2025-05-11 16:45:11,863 - vibe_check_actor_initializer - WARNING - [ACTOR INIT] Actor file_actor_12 not found in registry, retrying (2/3)
2025-05-11 16:45:12,064 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting registry for actor file_actor_12
2025-05-11 16:45:12,064 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting actor file_actor_12 from registry
2025-05-11 16:45:12,064 - vibe_check_actor_initializer - WARNING - [ACTOR REGISTRY] Actor file_actor_12 not found in registry
2025-05-11 16:45:12,064 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Available actors in registry: ['file_actor_13', 'project_actor']
2025-05-11 16:45:12,064 - vibe_check_actor_initializer - ERROR - [ACTOR INIT] Actor file_actor_12 not found in registry after 3 attempts
2025-05-11 16:45:12,064 - vibe_check_actor_lifecycle_manager - ERROR - Failed to initialize actor file_actor_12 after 3 attempts: Actor file_actor_12 not found in registry after 3 attempts
2025-05-11 16:45:12,064 - vibe_check_actor_lifecycle_manager - ERROR - Traceback (most recent call last):
  File "/Users/<USER>/Lokalne_Kody/PAT_project_analysis/vibe_check/core/orchestration/actor_lifecycle_manager.py", line 174, in _initialize_all_actors
    await initializer.initialize_actor(actor_id)
  File "/Users/<USER>/Lokalne_Kody/PAT_project_analysis/vibe_check/core/actor_system/actor_initializer.py", line 883, in initialize_actor
    raise ValueError(error_msg)
ValueError: Actor file_actor_12 not found in registry after 3 attempts

2025-05-11 16:45:12,064 - vibe_check_actor_lifecycle_manager - INFO - Initializing actor file_actor_13 (type: file)
2025-05-11 16:45:12,064 - vibe_check_actor_initializer - INFO - [ACTOR INIT] Starting initialization of actor file_actor_13
2025-05-11 16:45:12,064 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting registry for actor file_actor_13
2025-05-11 16:45:12,064 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting actor file_actor_13 from registry
2025-05-11 16:45:12,064 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Successfully retrieved actor file_actor_13 from registry
2025-05-11 16:45:12,064 - vibe_check_actor_initializer - INFO - [ACTOR INIT] Actor file_actor_13 is of type file
2025-05-11 16:45:12,064 - vibe_check_actor_initializer - DEBUG - [ACTOR INIT] Setting actor file_actor_13 state to INITIALIZING
2025-05-11 16:45:12,064 - vibe_check_actor_initializer - INFO - Actor file_actor_13 state changed: created -> initializing
2025-05-11 16:45:12,064 - vibe_check_actor_initializer - DEBUG - [ACTOR INIT] Calling initialize() method on actor file_actor_13
2025-05-11 16:45:12,064 - vibe_check_actor_system - INFO - Actor file_actor_13 is already registered with initialization manager
2025-05-11 16:45:12,064 - vibe_check_actor_system - INFO - Actor file_actor_13 is already registered with legacy initializer
2025-05-11 16:45:12,064 - vibe_check_actor_initialization - INFO - Actor file_actor_13 state changed: created -> initializing
2025-05-11 16:45:12,064 - vibe_check_actor_initializer - DEBUG - Actor file_actor_13 is already in state initializing, no change needed
2025-05-11 16:45:12,064 - vibe_check_initialization_tracker - INFO - Actor file_actor_13 initialization_start
2025-05-11 16:45:12,064 - vibe_check_actor_system - DEBUG - Actor file_actor_13 has no _initialize method
2025-05-11 16:45:12,064 - vibe_check_actor_initialization - INFO - Actor file_actor_13 state changed: initializing -> initialized
2025-05-11 16:45:12,064 - vibe_check_actor_initializer - INFO - Actor file_actor_13 state changed: initializing -> initialized
2025-05-11 16:45:12,064 - vibe_check_initialization_tracker - INFO - Actor file_actor_13 initialization_complete
2025-05-11 16:45:12,064 - vibe_check_actor_system - INFO - Actor file_actor_13 initialized successfully
2025-05-11 16:45:12,064 - vibe_check_actor_system - INFO - Actor file_actor_13 is already registered with initialization manager
2025-05-11 16:45:12,064 - vibe_check_actor_system - INFO - Actor file_actor_13 is already registered with legacy initializer
2025-05-11 16:45:12,065 - vibe_check_actor_initializer - INFO - Actor file_actor_13 state changed: REGISTERED -> INITIALIZING
2025-05-11 16:45:12,065 - vibe_check_actor_system.file_actor_13 - INFO - State transition: REGISTERED -> INITIALIZING
2025-05-11 16:45:12,065 - vibe_check_actor_system.file_actor_13 - DEBUG - State transition details: {'phase': 'initialize'}
2025-05-11 16:45:12,065 - vibe_check_actor_system.file_actor_13 - DEBUG - Operation 'state_transition_REGISTERED_to_INITIALIZING' took 0.000043 seconds
2025-05-11 16:45:12,065 - vibe_check_actor_system.initializer - DEBUG - Component state: {'actor_id': 'file_actor_13', 'state': 'INITIALIZING', 'previous_state': 'REGISTERED', 'timestamp': 1746974712.0650089, 'details': {'phase': 'initialize'}}
2025-05-11 16:45:12,065 - vibe_check_actor_registry - WARNING - Actor file_actor_13 already registered, updating
2025-05-11 16:45:12,065 - vibe_check_actor_registry - INFO - Registered actor file_actor_13 of type file
2025-05-11 16:45:12,065 - vibe_check_actor_system - DEBUG - Actor file_actor_13 has no _initialize method
2025-05-11 16:45:12,065 - vibe_check_actor_initializer - INFO - Actor file_actor_13 state changed: INITIALIZING -> INITIALIZED
2025-05-11 16:45:12,065 - vibe_check_actor_system.file_actor_13 - INFO - State transition: INITIALIZING -> INITIALIZED
2025-05-11 16:45:12,065 - vibe_check_actor_system.file_actor_13 - DEBUG - State transition details: {'phase': 'initialize'}
2025-05-11 16:45:12,065 - vibe_check_actor_system.file_actor_13 - DEBUG - Operation 'state_transition_INITIALIZING_to_INITIALIZED' took 0.000034 seconds
2025-05-11 16:45:12,065 - vibe_check_actor_system.initializer - DEBUG - Component state: {'actor_id': 'file_actor_13', 'state': 'INITIALIZED', 'previous_state': 'INITIALIZING', 'timestamp': 1746974712.065133, 'details': {'phase': 'initialize'}}
2025-05-11 16:45:12,065 - vibe_check_synchronization - DEBUG - Actor file_actor_13 reached synchronization point initialization_complete
2025-05-11 16:45:12,065 - vibe_check_actor_system - INFO - Actor file_actor_13 initialized successfully
2025-05-11 16:45:12,065 - vibe_check_actor_system.actor.file_actor_13 - ERROR - Error during state transition to FAILED: Actor file_actor_13: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds
2025-05-11 16:45:12,065 - vibe_check_actor_system.actor.file_actor_13 - ERROR - Traceback:
NoneType: None

2025-05-11 16:45:12,065 - vibe_check_actor_initializer - INFO - Actor file_actor_13 state changed: INITIALIZED -> FAILED
2025-05-11 16:45:12,065 - vibe_check_actor_system.file_actor_13 - INFO - State transition: INITIALIZED -> FAILED
2025-05-11 16:45:12,065 - vibe_check_actor_system.file_actor_13 - DEBUG - State transition details: {'phase': 'initialize'}
2025-05-11 16:45:12,065 - vibe_check_actor_system.file_actor_13 - DEBUG - Operation 'state_transition_INITIALIZED_to_FAILED' took 0.000093 seconds
2025-05-11 16:45:12,065 - vibe_check_actor_system.initializer - DEBUG - Component state: {'actor_id': 'file_actor_13', 'state': 'FAILED', 'previous_state': 'INITIALIZED', 'timestamp': 1746974712.0652301, 'details': {'phase': 'initialize'}}
2025-05-11 16:45:12,065 - vibe_check_actor_initializer - DEBUG - Cleaned up resources for actor file_actor_13
2025-05-11 16:45:12,065 - vibe_check_actor_system - INFO - Cleaning up resources for actor file_actor_13
2025-05-11 16:45:12,065 - vibe_check_actor_system - INFO - Cleared mailbox for actor file_actor_13
2025-05-11 16:45:12,065 - vibe_check_actor_registry - INFO - Unregistered actor file_actor_13
2025-05-11 16:45:12,065 - vibe_check_actor_system - INFO - Unregistered actor file_actor_13 from registry during cleanup
2025-05-11 16:45:12,065 - vibe_check_actor_initialization - INFO - Actor file_actor_13 state changed: initialized -> stopped
2025-05-11 16:45:12,065 - vibe_check_actor_system - INFO - Set actor file_actor_13 state to STOPPED in initialization manager
2025-05-11 16:45:12,065 - vibe_check_actor_initializer - ERROR - Invalid state transition for actor file_actor_13: initialized -> stopped
2025-05-11 16:45:12,065 - vibe_check_actor_initializer - DEBUG - Valid transitions from initialized are: ['stopping', 'starting', 'rollback', 'failed']
2025-05-11 16:45:12,065 - vibe_check_actor_initializer - WARNING - Forcing transition to stopped state for actor file_actor_13
2025-05-11 16:45:12,065 - vibe_check_actor_initializer - INFO - Actor file_actor_13 state changed: initialized -> stopped
2025-05-11 16:45:12,065 - vibe_check_actor_system - INFO - Set actor file_actor_13 state to STOPPED in legacy initializer
2025-05-11 16:45:12,065 - vibe_check_actor_system - INFO - Cleaned up resources for actor file_actor_13
2025-05-11 16:45:12,065 - vibe_check_actor_system - ERROR - Error in initialization process for actor file_actor_13: Actor file_actor_13: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds
2025-05-11 16:45:12,065 - vibe_check_actor_initializer - ERROR - Actor file_actor_13: Invalid state transition: FAILED -> FAILED
2025-05-11 16:45:12,065 - vibe_check_actor_system - ERROR - Error rolling back actor file_actor_13: Actor file_actor_13: Invalid state transition: FAILED -> FAILED (state: FAILED)
2025-05-11 16:45:12,065 - vibe_check_actor_system - INFO - Cleaning up resources for actor file_actor_13
2025-05-11 16:45:12,065 - vibe_check_actor_system - INFO - Cleared mailbox for actor file_actor_13
2025-05-11 16:45:12,065 - vibe_check_actor_registry - WARNING - Actor file_actor_13 not registered, cannot unregister
2025-05-11 16:45:12,065 - vibe_check_actor_system - INFO - Unregistered actor file_actor_13 from registry during cleanup
2025-05-11 16:45:12,065 - vibe_check_actor_initialization - DEBUG - Actor file_actor_13 is already in state stopped, no change needed
2025-05-11 16:45:12,065 - vibe_check_actor_system - INFO - Set actor file_actor_13 state to STOPPED in initialization manager
2025-05-11 16:45:12,065 - vibe_check_actor_initializer - DEBUG - Actor file_actor_13 is already in state stopped, no change needed
2025-05-11 16:45:12,065 - vibe_check_actor_initializer - INFO - Actor file_actor_13 state changed: stopped -> stopped
2025-05-11 16:45:12,065 - vibe_check_actor_system - INFO - Set actor file_actor_13 state to STOPPED in legacy initializer
2025-05-11 16:45:12,065 - vibe_check_actor_system - INFO - Cleaned up resources for actor file_actor_13
2025-05-11 16:45:12,065 - vibe_check_actor_initializer - ERROR - [ACTOR INIT] Failed to initialize actor file_actor_13: Actor file_actor_13: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds
2025-05-11 16:45:12,065 - vibe_check_actor_initializer - ERROR - [ACTOR INIT] Traceback for actor file_actor_13:
Traceback (most recent call last):
  File "/Users/<USER>/Lokalne_Kody/PAT_project_analysis/vibe_check/core/actor_system/actor_initializer.py", line 936, in initialize_actor
    await asyncio.wait_for(actor.initialize(), timeout)
  File "/Users/<USER>/.pyenv/versions/3.13.3/lib/python3.13/asyncio/tasks.py", line 507, in wait_for
    return await fut
           ^^^^^^^^^
  File "/Users/<USER>/Lokalne_Kody/PAT_project_analysis/vibe_check/core/actor_system/actor.py", line 1109, in initialize
    raise timeout_error
vibe_check.core.actor_system.exceptions.ActorTimeoutError: Actor file_actor_13: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds

2025-05-11 16:45:12,065 - vibe_check_actor_initializer - DEBUG - [ACTOR INIT] Setting actor file_actor_13 state to FAILED
2025-05-11 16:45:12,065 - vibe_check_actor_initializer - ERROR - Invalid state transition for actor file_actor_13: stopped -> failed
2025-05-11 16:45:12,065 - vibe_check_actor_initializer - DEBUG - Valid transitions from stopped are: ['initializing', 'created']
2025-05-11 16:45:12,065 - vibe_check_actor_initializer - WARNING - Forcing transition to failed state for actor file_actor_13
2025-05-11 16:45:12,065 - vibe_check_actor_initializer - INFO - Actor file_actor_13 state changed: stopped -> failed
2025-05-11 16:45:12,066 - vibe_check_actor_initializer - ERROR - Actor file_actor_13 failed: Actor file_actor_13 failed during initialize phase (state: stopped): Actor file_actor_13: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds
Original error: Actor file_actor_13: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds
2025-05-11 16:45:12,066 - vibe_check_actor_lifecycle_manager - WARNING - Failed to initialize actor file_actor_13 (attempt 1/3): Actor file_actor_13: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds
2025-05-11 16:45:12,066 - vibe_check_initialization_tracker - INFO - Actor file_actor_13 initialization_start
2025-05-11 16:45:12,066 - vibe_check_initialization_tracker - INFO - Actor file_actor_13 initialization_start
2025-05-11 16:45:12,066 - vibe_check_initialization_tracker - INFO - Actor file_actor_13 initialization_complete
2025-05-11 16:45:12,066 - vibe_check_initialization_tracker - INFO - Actor file_actor_13 initialization_complete
2025-05-11 16:45:12,066 - vibe_check_initialization_tracker - ERROR - Actor file_actor_13 failed: Actor file_actor_13: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds
2025-05-11 16:45:12,167 - vibe_check_actor_lifecycle_manager - INFO - Initializing actor file_actor_13 (type: file)
2025-05-11 16:45:12,167 - vibe_check_actor_initializer - INFO - [ACTOR INIT] Starting initialization of actor file_actor_13
2025-05-11 16:45:12,167 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting registry for actor file_actor_13
2025-05-11 16:45:12,167 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting actor file_actor_13 from registry
2025-05-11 16:45:12,167 - vibe_check_actor_initializer - WARNING - [ACTOR REGISTRY] Actor file_actor_13 not found in registry
2025-05-11 16:45:12,167 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Available actors in registry: ['project_actor']
2025-05-11 16:45:12,167 - vibe_check_actor_initializer - WARNING - [ACTOR INIT] Actor file_actor_13 not found in registry, retrying (1/3)
2025-05-11 16:45:12,268 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting registry for actor file_actor_13
2025-05-11 16:45:12,268 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting actor file_actor_13 from registry
2025-05-11 16:45:12,268 - vibe_check_actor_initializer - WARNING - [ACTOR REGISTRY] Actor file_actor_13 not found in registry
2025-05-11 16:45:12,268 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Available actors in registry: ['project_actor']
2025-05-11 16:45:12,268 - vibe_check_actor_initializer - WARNING - [ACTOR INIT] Actor file_actor_13 not found in registry, retrying (2/3)
2025-05-11 16:45:12,470 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting registry for actor file_actor_13
2025-05-11 16:45:12,470 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting actor file_actor_13 from registry
2025-05-11 16:45:12,470 - vibe_check_actor_initializer - WARNING - [ACTOR REGISTRY] Actor file_actor_13 not found in registry
2025-05-11 16:45:12,470 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Available actors in registry: ['project_actor']
2025-05-11 16:45:12,471 - vibe_check_actor_initializer - ERROR - [ACTOR INIT] Actor file_actor_13 not found in registry after 3 attempts
2025-05-11 16:45:12,471 - vibe_check_actor_lifecycle_manager - WARNING - Failed to initialize actor file_actor_13 (attempt 2/3): Actor file_actor_13 not found in registry after 3 attempts
2025-05-11 16:45:12,672 - vibe_check_actor_lifecycle_manager - INFO - Initializing actor file_actor_13 (type: file)
2025-05-11 16:45:12,672 - vibe_check_actor_initializer - INFO - [ACTOR INIT] Starting initialization of actor file_actor_13
2025-05-11 16:45:12,672 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting registry for actor file_actor_13
2025-05-11 16:45:12,672 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting actor file_actor_13 from registry
2025-05-11 16:45:12,672 - vibe_check_actor_initializer - WARNING - [ACTOR REGISTRY] Actor file_actor_13 not found in registry
2025-05-11 16:45:12,673 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Available actors in registry: ['project_actor']
2025-05-11 16:45:12,673 - vibe_check_actor_initializer - WARNING - [ACTOR INIT] Actor file_actor_13 not found in registry, retrying (1/3)
2025-05-11 16:45:12,774 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting registry for actor file_actor_13
2025-05-11 16:45:12,774 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting actor file_actor_13 from registry
2025-05-11 16:45:12,774 - vibe_check_actor_initializer - WARNING - [ACTOR REGISTRY] Actor file_actor_13 not found in registry
2025-05-11 16:45:12,774 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Available actors in registry: ['project_actor']
2025-05-11 16:45:12,774 - vibe_check_actor_initializer - WARNING - [ACTOR INIT] Actor file_actor_13 not found in registry, retrying (2/3)
2025-05-11 16:45:12,975 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting registry for actor file_actor_13
2025-05-11 16:45:12,976 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting actor file_actor_13 from registry
2025-05-11 16:45:12,976 - vibe_check_actor_initializer - WARNING - [ACTOR REGISTRY] Actor file_actor_13 not found in registry
2025-05-11 16:45:12,976 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Available actors in registry: ['project_actor']
2025-05-11 16:45:12,976 - vibe_check_actor_initializer - ERROR - [ACTOR INIT] Actor file_actor_13 not found in registry after 3 attempts
2025-05-11 16:45:12,976 - vibe_check_actor_lifecycle_manager - ERROR - Failed to initialize actor file_actor_13 after 3 attempts: Actor file_actor_13 not found in registry after 3 attempts
2025-05-11 16:45:12,977 - vibe_check_actor_lifecycle_manager - ERROR - Traceback (most recent call last):
  File "/Users/<USER>/Lokalne_Kody/PAT_project_analysis/vibe_check/core/orchestration/actor_lifecycle_manager.py", line 174, in _initialize_all_actors
    await initializer.initialize_actor(actor_id)
  File "/Users/<USER>/Lokalne_Kody/PAT_project_analysis/vibe_check/core/actor_system/actor_initializer.py", line 883, in initialize_actor
    raise ValueError(error_msg)
ValueError: Actor file_actor_13 not found in registry after 3 attempts

2025-05-11 16:45:12,977 - vibe_check_actor_lifecycle_manager - INFO - Completed initialization of 14 actors of type file
2025-05-11 16:45:12,977 - vibe_check_actor_lifecycle_manager - INFO - Initializing 1 actors of type project
2025-05-11 16:45:12,977 - vibe_check_actor_lifecycle_manager - INFO - Initializing actor project_actor (type: project)
2025-05-11 16:45:12,977 - vibe_check_actor_initializer - INFO - [ACTOR INIT] Starting initialization of actor project_actor
2025-05-11 16:45:12,977 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting registry for actor project_actor
2025-05-11 16:45:12,977 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting actor project_actor from registry
2025-05-11 16:45:12,977 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Successfully retrieved actor project_actor from registry
2025-05-11 16:45:12,978 - vibe_check_actor_initializer - INFO - [ACTOR INIT] Actor project_actor is of type project
2025-05-11 16:45:12,978 - vibe_check_actor_initializer - DEBUG - [ACTOR INIT] Setting actor project_actor state to INITIALIZING
2025-05-11 16:45:12,978 - vibe_check_actor_initializer - INFO - Actor project_actor state changed: created -> initializing
2025-05-11 16:45:12,978 - vibe_check_actor_initializer - DEBUG - [ACTOR INIT] Calling initialize() method on actor project_actor
2025-05-11 16:45:12,978 - vibe_check_actor_system - INFO - Actor project_actor is already registered with initialization manager
2025-05-11 16:45:12,978 - vibe_check_actor_system - INFO - Actor project_actor is already registered with legacy initializer
2025-05-11 16:45:12,978 - vibe_check_actor_initialization - INFO - Actor project_actor state changed: created -> initializing
2025-05-11 16:45:12,978 - vibe_check_actor_initializer - DEBUG - Actor project_actor is already in state initializing, no change needed
2025-05-11 16:45:12,978 - vibe_check_initialization_tracker - INFO - Actor project_actor initialization_start
2025-05-11 16:45:12,978 - vibe_check_actor_system - INFO - No state file found for actor project_actor
2025-05-11 16:45:12,979 - vibe_check_actor_system - DEBUG - Actor project_actor has no _initialize method
2025-05-11 16:45:12,979 - vibe_check_actor_initialization - INFO - Actor project_actor state changed: initializing -> initialized
2025-05-11 16:45:12,979 - vibe_check_actor_initializer - INFO - Actor project_actor state changed: initializing -> initialized
2025-05-11 16:45:12,979 - vibe_check_initialization_tracker - INFO - Actor project_actor initialization_complete
2025-05-11 16:45:12,979 - vibe_check_actor_system - INFO - Actor project_actor initialized successfully
2025-05-11 16:45:12,979 - vibe_check_actor_system - INFO - Actor project_actor is already registered with initialization manager
2025-05-11 16:45:12,979 - vibe_check_actor_system - INFO - Actor project_actor is already registered with legacy initializer
2025-05-11 16:45:12,979 - vibe_check_actor_initializer - INFO - Actor project_actor state changed: REGISTERED -> INITIALIZING
2025-05-11 16:45:12,979 - vibe_check_actor_system.project_actor - INFO - State transition: REGISTERED -> INITIALIZING
2025-05-11 16:45:12,979 - vibe_check_actor_system.project_actor - DEBUG - State transition details: {'phase': 'initialize'}
2025-05-11 16:45:12,982 - vibe_check_actor_system.project_actor - DEBUG - Operation 'state_transition_REGISTERED_to_INITIALIZING' took 0.000123 seconds
2025-05-11 16:45:12,982 - vibe_check_actor_system.initializer - DEBUG - Component state: {'actor_id': 'project_actor', 'state': 'INITIALIZING', 'previous_state': 'REGISTERED', 'timestamp': 1746974712.979562, 'details': {'phase': 'initialize'}}
2025-05-11 16:45:12,982 - vibe_check_actor_system - INFO - No state file found for actor project_actor
2025-05-11 16:45:12,982 - vibe_check_actor_registry - WARNING - Actor project_actor already registered, updating
2025-05-11 16:45:12,982 - vibe_check_actor_registry - INFO - Registered actor project_actor of type project
2025-05-11 16:45:12,982 - vibe_check_actor_system - DEBUG - Actor project_actor has no _initialize method
2025-05-11 16:45:12,982 - vibe_check_actor_initializer - INFO - Actor project_actor state changed: INITIALIZING -> INITIALIZED
2025-05-11 16:45:12,982 - vibe_check_actor_system.project_actor - INFO - State transition: INITIALIZING -> INITIALIZED
2025-05-11 16:45:12,983 - vibe_check_actor_system.project_actor - DEBUG - State transition details: {'phase': 'initialize'}
2025-05-11 16:45:12,983 - vibe_check_actor_system.project_actor - DEBUG - Operation 'state_transition_INITIALIZING_to_INITIALIZED' took 0.000095 seconds
2025-05-11 16:45:12,983 - vibe_check_actor_system.initializer - DEBUG - Component state: {'actor_id': 'project_actor', 'state': 'INITIALIZED', 'previous_state': 'INITIALIZING', 'timestamp': 1746974712.982944, 'details': {'phase': 'initialize'}}
2025-05-11 16:45:12,983 - vibe_check_synchronization - DEBUG - Actor project_actor reached synchronization point initialization_complete
2025-05-11 16:45:12,983 - vibe_check_actor_system - INFO - Actor project_actor initialized successfully
2025-05-11 16:45:12,983 - vibe_check_actor_system.actor.project_actor - ERROR - Error during state transition to FAILED: Actor project_actor: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds
2025-05-11 16:45:12,983 - vibe_check_actor_system.actor.project_actor - ERROR - Traceback:
NoneType: None

2025-05-11 16:45:12,983 - vibe_check_actor_initializer - INFO - Actor project_actor state changed: INITIALIZED -> FAILED
2025-05-11 16:45:12,983 - vibe_check_actor_system.project_actor - INFO - State transition: INITIALIZED -> FAILED
2025-05-11 16:45:12,983 - vibe_check_actor_system.project_actor - DEBUG - State transition details: {'phase': 'initialize'}
2025-05-11 16:45:12,983 - vibe_check_actor_system.project_actor - DEBUG - Operation 'state_transition_INITIALIZED_to_FAILED' took 0.000291 seconds
2025-05-11 16:45:12,983 - vibe_check_actor_system.initializer - DEBUG - Component state: {'actor_id': 'project_actor', 'state': 'FAILED', 'previous_state': 'INITIALIZED', 'timestamp': 1746974712.9832408, 'details': {'phase': 'initialize'}}
2025-05-11 16:45:12,983 - vibe_check_actor_initializer - DEBUG - Cleaned up resources for actor project_actor
2025-05-11 16:45:12,983 - vibe_check_actor_system - INFO - Cleaning up resources for actor project_actor
2025-05-11 16:45:12,983 - vibe_check_actor_system - INFO - Cleared mailbox for actor project_actor
2025-05-11 16:45:12,983 - vibe_check_actor_registry - INFO - Unregistered actor project_actor
2025-05-11 16:45:12,983 - vibe_check_actor_system - INFO - Unregistered actor project_actor from registry during cleanup
2025-05-11 16:45:12,983 - vibe_check_actor_initialization - INFO - Actor project_actor state changed: initialized -> stopped
2025-05-11 16:45:12,983 - vibe_check_actor_system - INFO - Set actor project_actor state to STOPPED in initialization manager
2025-05-11 16:45:12,983 - vibe_check_actor_initializer - ERROR - Invalid state transition for actor project_actor: initialized -> stopped
2025-05-11 16:45:12,983 - vibe_check_actor_initializer - DEBUG - Valid transitions from initialized are: ['stopping', 'starting', 'rollback', 'failed']
2025-05-11 16:45:12,983 - vibe_check_actor_initializer - WARNING - Forcing transition to stopped state for actor project_actor
2025-05-11 16:45:12,983 - vibe_check_actor_initializer - INFO - Actor project_actor state changed: initialized -> stopped
2025-05-11 16:45:12,983 - vibe_check_actor_system - INFO - Set actor project_actor state to STOPPED in legacy initializer
2025-05-11 16:45:12,983 - vibe_check_actor_system - INFO - Cleaned up resources for actor project_actor
2025-05-11 16:45:12,983 - vibe_check_actor_system - ERROR - Error in initialization process for actor project_actor: Actor project_actor: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds
2025-05-11 16:45:12,983 - vibe_check_actor_initializer - ERROR - Actor project_actor: Invalid state transition: FAILED -> FAILED
2025-05-11 16:45:12,984 - vibe_check_actor_system - ERROR - Error rolling back actor project_actor: Actor project_actor: Invalid state transition: FAILED -> FAILED (state: FAILED)
2025-05-11 16:45:12,984 - vibe_check_actor_system - INFO - Cleaning up resources for actor project_actor
2025-05-11 16:45:12,984 - vibe_check_actor_system - INFO - Cleared mailbox for actor project_actor
2025-05-11 16:45:12,984 - vibe_check_actor_registry - WARNING - Actor project_actor not registered, cannot unregister
2025-05-11 16:45:12,984 - vibe_check_actor_system - INFO - Unregistered actor project_actor from registry during cleanup
2025-05-11 16:45:12,984 - vibe_check_actor_initialization - DEBUG - Actor project_actor is already in state stopped, no change needed
2025-05-11 16:45:12,984 - vibe_check_actor_system - INFO - Set actor project_actor state to STOPPED in initialization manager
2025-05-11 16:45:12,984 - vibe_check_actor_initializer - DEBUG - Actor project_actor is already in state stopped, no change needed
2025-05-11 16:45:12,984 - vibe_check_actor_initializer - INFO - Actor project_actor state changed: stopped -> stopped
2025-05-11 16:45:12,984 - vibe_check_actor_system - INFO - Set actor project_actor state to STOPPED in legacy initializer
2025-05-11 16:45:12,984 - vibe_check_actor_system - INFO - Cleaned up resources for actor project_actor
2025-05-11 16:45:12,984 - vibe_check_actor_initializer - ERROR - [ACTOR INIT] Failed to initialize actor project_actor: Actor project_actor: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds
2025-05-11 16:45:12,984 - vibe_check_actor_initializer - ERROR - [ACTOR INIT] Traceback for actor project_actor:
Traceback (most recent call last):
  File "/Users/<USER>/Lokalne_Kody/PAT_project_analysis/vibe_check/core/actor_system/actor_initializer.py", line 936, in initialize_actor
    await asyncio.wait_for(actor.initialize(), timeout)
  File "/Users/<USER>/.pyenv/versions/3.13.3/lib/python3.13/asyncio/tasks.py", line 507, in wait_for
    return await fut
           ^^^^^^^^^
  File "/Users/<USER>/Lokalne_Kody/PAT_project_analysis/vibe_check/core/actor_system/actor.py", line 1109, in initialize
    raise timeout_error
vibe_check.core.actor_system.exceptions.ActorTimeoutError: Actor project_actor: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds

2025-05-11 16:45:12,985 - vibe_check_actor_initializer - DEBUG - [ACTOR INIT] Setting actor project_actor state to FAILED
2025-05-11 16:45:12,985 - vibe_check_actor_initializer - ERROR - Invalid state transition for actor project_actor: stopped -> failed
2025-05-11 16:45:12,985 - vibe_check_actor_initializer - DEBUG - Valid transitions from stopped are: ['initializing', 'created']
2025-05-11 16:45:12,985 - vibe_check_actor_initializer - WARNING - Forcing transition to failed state for actor project_actor
2025-05-11 16:45:12,985 - vibe_check_actor_initializer - INFO - Actor project_actor state changed: stopped -> failed
2025-05-11 16:45:12,985 - vibe_check_actor_initializer - ERROR - Actor project_actor failed: Actor project_actor failed during initialize phase (state: stopped): Actor project_actor: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds
Original error: Actor project_actor: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds
2025-05-11 16:45:12,985 - vibe_check_actor_lifecycle_manager - WARNING - Failed to initialize actor project_actor (attempt 1/3): Actor project_actor: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds
2025-05-11 16:45:12,985 - vibe_check_initialization_tracker - INFO - Actor project_actor initialization_start
2025-05-11 16:45:12,985 - vibe_check_initialization_tracker - INFO - Actor project_actor initialization_start
2025-05-11 16:45:12,985 - vibe_check_initialization_tracker - INFO - Actor project_actor initialization_complete
2025-05-11 16:45:12,985 - vibe_check_initialization_tracker - INFO - Actor project_actor initialization_complete
2025-05-11 16:45:12,985 - vibe_check_initialization_tracker - ERROR - Actor project_actor failed: Actor project_actor: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds
2025-05-11 16:45:13,087 - vibe_check_actor_lifecycle_manager - INFO - Initializing actor project_actor (type: project)
2025-05-11 16:45:13,087 - vibe_check_actor_initializer - INFO - [ACTOR INIT] Starting initialization of actor project_actor
2025-05-11 16:45:13,087 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting registry for actor project_actor
2025-05-11 16:45:13,087 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting actor project_actor from registry
2025-05-11 16:45:13,087 - vibe_check_actor_initializer - WARNING - [ACTOR REGISTRY] Actor project_actor not found in registry
2025-05-11 16:45:13,087 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Available actors in registry: []
2025-05-11 16:45:13,087 - vibe_check_actor_initializer - WARNING - [ACTOR INIT] Actor project_actor not found in registry, retrying (1/3)
2025-05-11 16:45:13,188 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting registry for actor project_actor
2025-05-11 16:45:13,189 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting actor project_actor from registry
2025-05-11 16:45:13,189 - vibe_check_actor_initializer - WARNING - [ACTOR REGISTRY] Actor project_actor not found in registry
2025-05-11 16:45:13,189 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Available actors in registry: []
2025-05-11 16:45:13,189 - vibe_check_actor_initializer - WARNING - [ACTOR INIT] Actor project_actor not found in registry, retrying (2/3)
2025-05-11 16:45:13,390 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting registry for actor project_actor
2025-05-11 16:45:13,390 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting actor project_actor from registry
2025-05-11 16:45:13,390 - vibe_check_actor_initializer - WARNING - [ACTOR REGISTRY] Actor project_actor not found in registry
2025-05-11 16:45:13,390 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Available actors in registry: []
2025-05-11 16:45:13,390 - vibe_check_actor_initializer - ERROR - [ACTOR INIT] Actor project_actor not found in registry after 3 attempts
2025-05-11 16:45:13,391 - vibe_check_actor_lifecycle_manager - WARNING - Failed to initialize actor project_actor (attempt 2/3): Actor project_actor not found in registry after 3 attempts
2025-05-11 16:45:13,592 - vibe_check_actor_lifecycle_manager - INFO - Initializing actor project_actor (type: project)
2025-05-11 16:45:13,592 - vibe_check_actor_initializer - INFO - [ACTOR INIT] Starting initialization of actor project_actor
2025-05-11 16:45:13,592 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting registry for actor project_actor
2025-05-11 16:45:13,592 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting actor project_actor from registry
2025-05-11 16:45:13,592 - vibe_check_actor_initializer - WARNING - [ACTOR REGISTRY] Actor project_actor not found in registry
2025-05-11 16:45:13,592 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Available actors in registry: []
2025-05-11 16:45:13,592 - vibe_check_actor_initializer - WARNING - [ACTOR INIT] Actor project_actor not found in registry, retrying (1/3)
2025-05-11 16:45:13,694 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting registry for actor project_actor
2025-05-11 16:45:13,694 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting actor project_actor from registry
2025-05-11 16:45:13,694 - vibe_check_actor_initializer - WARNING - [ACTOR REGISTRY] Actor project_actor not found in registry
2025-05-11 16:45:13,694 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Available actors in registry: []
2025-05-11 16:45:13,694 - vibe_check_actor_initializer - WARNING - [ACTOR INIT] Actor project_actor not found in registry, retrying (2/3)
2025-05-11 16:45:13,896 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting registry for actor project_actor
2025-05-11 16:45:13,896 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Getting actor project_actor from registry
2025-05-11 16:45:13,896 - vibe_check_actor_initializer - WARNING - [ACTOR REGISTRY] Actor project_actor not found in registry
2025-05-11 16:45:13,897 - vibe_check_actor_initializer - DEBUG - [ACTOR REGISTRY] Available actors in registry: []
2025-05-11 16:45:13,897 - vibe_check_actor_initializer - ERROR - [ACTOR INIT] Actor project_actor not found in registry after 3 attempts
2025-05-11 16:45:13,897 - vibe_check_actor_lifecycle_manager - ERROR - Failed to initialize actor project_actor after 3 attempts: Actor project_actor not found in registry after 3 attempts
2025-05-11 16:45:13,898 - vibe_check_actor_lifecycle_manager - ERROR - Traceback (most recent call last):
  File "/Users/<USER>/Lokalne_Kody/PAT_project_analysis/vibe_check/core/orchestration/actor_lifecycle_manager.py", line 174, in _initialize_all_actors
    await initializer.initialize_actor(actor_id)
  File "/Users/<USER>/Lokalne_Kody/PAT_project_analysis/vibe_check/core/actor_system/actor_initializer.py", line 883, in initialize_actor
    raise ValueError(error_msg)
ValueError: Actor project_actor not found in registry after 3 attempts

2025-05-11 16:45:13,898 - vibe_check_actor_lifecycle_manager - INFO - Completed initialization of 1 actors of type project
2025-05-11 16:45:13,898 - vibe_check_actor_lifecycle_manager - ERROR - No actors were successfully initialized
2025-05-11 16:45:13,898 - vibe_check_actor_lifecycle_manager - ERROR - Actor supervisor_actor (type: supervisor) failed to initialize
2025-05-11 16:45:13,899 - vibe_check_actor_lifecycle_manager - ERROR - Error for actor supervisor_actor: Actor supervisor_actor failed during initialize phase (state: stopped): Actor supervisor_actor: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds
Original error: Actor supervisor_actor: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds
2025-05-11 16:45:13,899 - vibe_check_actor_lifecycle_manager - ERROR - Actor report_actor (type: report) failed to initialize
2025-05-11 16:45:13,899 - vibe_check_actor_lifecycle_manager - ERROR - Error for actor report_actor: Actor report_actor failed during initialize phase (state: stopped): Actor report_actor: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds
Original error: Actor report_actor: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds
2025-05-11 16:45:13,899 - vibe_check_actor_lifecycle_manager - ERROR - Actor visualization_actor (type: visualization) failed to initialize
2025-05-11 16:45:13,899 - vibe_check_actor_lifecycle_manager - ERROR - Error for actor visualization_actor: Actor visualization_actor failed during initialize phase (state: stopped): Actor visualization_actor: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds
Original error: Actor visualization_actor: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds
2025-05-11 16:45:13,899 - vibe_check_actor_lifecycle_manager - ERROR - Actor tool_actor (type: tool) failed to initialize
2025-05-11 16:45:13,899 - vibe_check_actor_lifecycle_manager - ERROR - Error for actor tool_actor: Actor tool_actor failed during initialize phase (state: stopped): Actor tool_actor: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds
Original error: Actor tool_actor: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds
2025-05-11 16:45:13,899 - vibe_check_actor_lifecycle_manager - ERROR - Actor file_actor_0 (type: file) failed to initialize
2025-05-11 16:45:13,899 - vibe_check_actor_lifecycle_manager - ERROR - Error for actor file_actor_0: Actor file_actor_0 failed during initialize phase (state: stopped): Actor file_actor_0: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds
Original error: Actor file_actor_0: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds
2025-05-11 16:45:13,899 - vibe_check_actor_lifecycle_manager - ERROR - Actor file_actor_1 (type: file) failed to initialize
2025-05-11 16:45:13,899 - vibe_check_actor_lifecycle_manager - ERROR - Error for actor file_actor_1: Actor file_actor_1 failed during initialize phase (state: stopped): Actor file_actor_1: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds
Original error: Actor file_actor_1: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds
2025-05-11 16:45:13,899 - vibe_check_actor_lifecycle_manager - ERROR - Actor file_actor_2 (type: file) failed to initialize
2025-05-11 16:45:13,899 - vibe_check_actor_lifecycle_manager - ERROR - Error for actor file_actor_2: Actor file_actor_2 failed during initialize phase (state: stopped): Actor file_actor_2: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds
Original error: Actor file_actor_2: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds
2025-05-11 16:45:13,899 - vibe_check_actor_lifecycle_manager - ERROR - Actor file_actor_3 (type: file) failed to initialize
2025-05-11 16:45:13,899 - vibe_check_actor_lifecycle_manager - ERROR - Error for actor file_actor_3: Actor file_actor_3 failed during initialize phase (state: stopped): Actor file_actor_3: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds
Original error: Actor file_actor_3: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds
2025-05-11 16:45:13,899 - vibe_check_actor_lifecycle_manager - ERROR - Actor file_actor_4 (type: file) failed to initialize
2025-05-11 16:45:13,899 - vibe_check_actor_lifecycle_manager - ERROR - Error for actor file_actor_4: Actor file_actor_4 failed during initialize phase (state: stopped): Actor file_actor_4: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds
Original error: Actor file_actor_4: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds
2025-05-11 16:45:13,899 - vibe_check_actor_lifecycle_manager - ERROR - Actor file_actor_5 (type: file) failed to initialize
2025-05-11 16:45:13,899 - vibe_check_actor_lifecycle_manager - ERROR - Error for actor file_actor_5: Actor file_actor_5 failed during initialize phase (state: stopped): Actor file_actor_5: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds
Original error: Actor file_actor_5: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds
2025-05-11 16:45:13,899 - vibe_check_actor_lifecycle_manager - ERROR - Actor file_actor_6 (type: file) failed to initialize
2025-05-11 16:45:13,899 - vibe_check_actor_lifecycle_manager - ERROR - Error for actor file_actor_6: Actor file_actor_6 failed during initialize phase (state: stopped): Actor file_actor_6: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds
Original error: Actor file_actor_6: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds
2025-05-11 16:45:13,899 - vibe_check_actor_lifecycle_manager - ERROR - Actor file_actor_7 (type: file) failed to initialize
2025-05-11 16:45:13,899 - vibe_check_actor_lifecycle_manager - ERROR - Error for actor file_actor_7: Actor file_actor_7 failed during initialize phase (state: stopped): Actor file_actor_7: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds
Original error: Actor file_actor_7: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds
2025-05-11 16:45:13,899 - vibe_check_actor_lifecycle_manager - ERROR - Actor file_actor_8 (type: file) failed to initialize
2025-05-11 16:45:13,899 - vibe_check_actor_lifecycle_manager - ERROR - Error for actor file_actor_8: Actor file_actor_8 failed during initialize phase (state: stopped): Actor file_actor_8: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds
Original error: Actor file_actor_8: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds
2025-05-11 16:45:13,899 - vibe_check_actor_lifecycle_manager - ERROR - Actor file_actor_9 (type: file) failed to initialize
2025-05-11 16:45:13,899 - vibe_check_actor_lifecycle_manager - ERROR - Error for actor file_actor_9: Actor file_actor_9 failed during initialize phase (state: stopped): Actor file_actor_9: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds
Original error: Actor file_actor_9: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds
2025-05-11 16:45:13,899 - vibe_check_actor_lifecycle_manager - ERROR - Actor file_actor_10 (type: file) failed to initialize
2025-05-11 16:45:13,899 - vibe_check_actor_lifecycle_manager - ERROR - Error for actor file_actor_10: Actor file_actor_10 failed during initialize phase (state: stopped): Actor file_actor_10: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds
Original error: Actor file_actor_10: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds
2025-05-11 16:45:13,899 - vibe_check_actor_lifecycle_manager - ERROR - Actor file_actor_11 (type: file) failed to initialize
2025-05-11 16:45:13,899 - vibe_check_actor_lifecycle_manager - ERROR - Error for actor file_actor_11: Actor file_actor_11 failed during initialize phase (state: stopped): Actor file_actor_11: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds
Original error: Actor file_actor_11: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds
2025-05-11 16:45:13,899 - vibe_check_actor_lifecycle_manager - ERROR - Actor file_actor_12 (type: file) failed to initialize
2025-05-11 16:45:13,900 - vibe_check_actor_lifecycle_manager - ERROR - Error for actor file_actor_12: Actor file_actor_12 failed during initialize phase (state: stopped): Actor file_actor_12: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds
Original error: Actor file_actor_12: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds
2025-05-11 16:45:13,900 - vibe_check_actor_lifecycle_manager - ERROR - Actor file_actor_13 (type: file) failed to initialize
2025-05-11 16:45:13,900 - vibe_check_actor_lifecycle_manager - ERROR - Error for actor file_actor_13: Actor file_actor_13 failed during initialize phase (state: stopped): Actor file_actor_13: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds
Original error: Actor file_actor_13: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds
2025-05-11 16:45:13,900 - vibe_check_actor_lifecycle_manager - ERROR - Actor project_actor (type: project) failed to initialize
2025-05-11 16:45:13,900 - vibe_check_actor_lifecycle_manager - ERROR - Error for actor project_actor: Actor project_actor failed during initialize phase (state: stopped): Actor project_actor: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds
Original error: Actor project_actor: Timeout (60.0s) during initialize: Initialization timed out after 60.0 seconds
2025-05-11 16:45:13,900 - vibe_check_actor_lifecycle_manager - ERROR - Error during actor system initialization: No actors were successfully initialized
2025-05-11 16:45:13,900 - vibe_check_actor_lifecycle_manager - ERROR - Traceback (most recent call last):
  File "/Users/<USER>/Lokalne_Kody/PAT_project_analysis/vibe_check/core/orchestration/actor_lifecycle_manager.py", line 72, in start_actors
    initialized_actors = await self._initialize_all_actors(initializer, fail_fast)
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Lokalne_Kody/PAT_project_analysis/vibe_check/core/orchestration/actor_lifecycle_manager.py", line 214, in _initialize_all_actors
    raise RuntimeError(error_msg)
RuntimeError: No actors were successfully initialized

2025-05-11 16:45:13,900 - vibe_check_actor_lifecycle_manager - INFO - Cleaning up actors due to initialization failure
2025-05-11 16:45:13,900 - vibe_check_actor_lifecycle_manager - INFO - Actor cleanup complete
2025-05-11 16:45:13,900 - vibe_check_orchestrator - ERROR - Error during actor system initialization: No actors were successfully initialized
2025-05-11 16:45:13,903 - vibe_check_orchestrator - ERROR - Traceback (most recent call last):
  File "/Users/<USER>/Lokalne_Kody/PAT_project_analysis/vibe_check/core/orchestrator.py", line 343, in start_actors
    await self.actor_lifecycle_manager.start_actors()
  File "/Users/<USER>/Lokalne_Kody/PAT_project_analysis/vibe_check/core/orchestration/actor_lifecycle_manager.py", line 72, in start_actors
    initialized_actors = await self._initialize_all_actors(initializer, fail_fast)
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Lokalne_Kody/PAT_project_analysis/vibe_check/core/orchestration/actor_lifecycle_manager.py", line 214, in _initialize_all_actors
    raise RuntimeError(error_msg)
RuntimeError: No actors were successfully initialized

2025-05-11 16:45:13,904 - vibe_check_actor_initializer - ERROR - Invalid state transition for actor report_actor: failed -> stopping
2025-05-11 16:45:13,904 - vibe_check_actor_initializer - DEBUG - Valid transitions from failed are: ['stopped', 'rollback', 'created']
2025-05-11 16:45:13,904 - vibe_check_actor_system.terminator - INFO - Actor report_actor is already stopped
2025-05-11 16:45:13,904 - vibe_check_actor_initializer - INFO - Actor report_actor state changed: failed -> stopped
2025-05-11 16:45:13,904 - vibe_check_actor_initializer - ERROR - Invalid state transition for actor visualization_actor: failed -> stopping
2025-05-11 16:45:13,904 - vibe_check_actor_initializer - DEBUG - Valid transitions from failed are: ['stopped', 'rollback', 'created']
2025-05-11 16:45:13,904 - vibe_check_actor_system.terminator - INFO - Actor visualization_actor is already stopped
2025-05-11 16:45:13,904 - vibe_check_actor_initializer - INFO - Actor visualization_actor state changed: failed -> stopped
2025-05-11 16:45:13,904 - vibe_check_actor_initializer - ERROR - Invalid state transition for actor tool_actor: failed -> stopping
2025-05-11 16:45:13,904 - vibe_check_actor_initializer - DEBUG - Valid transitions from failed are: ['stopped', 'rollback', 'created']
2025-05-11 16:45:13,904 - vibe_check_actor_system.terminator - INFO - Actor tool_actor is already stopped
2025-05-11 16:45:13,904 - vibe_check_actor_initializer - INFO - Actor tool_actor state changed: failed -> stopped
2025-05-11 16:45:13,904 - vibe_check_actor_initializer - ERROR - Invalid state transition for actor file_actor_0: failed -> stopping
2025-05-11 16:45:13,904 - vibe_check_actor_initializer - DEBUG - Valid transitions from failed are: ['stopped', 'rollback', 'created']
2025-05-11 16:45:13,904 - vibe_check_actor_system.terminator - INFO - Actor file_actor_0 is already stopped
2025-05-11 16:45:13,904 - vibe_check_actor_initializer - INFO - Actor file_actor_0 state changed: failed -> stopped
2025-05-11 16:45:13,904 - vibe_check_actor_initializer - ERROR - Invalid state transition for actor file_actor_1: failed -> stopping
2025-05-11 16:45:13,904 - vibe_check_actor_initializer - DEBUG - Valid transitions from failed are: ['stopped', 'rollback', 'created']
2025-05-11 16:45:13,905 - vibe_check_actor_system.terminator - INFO - Actor file_actor_1 is already stopped
2025-05-11 16:45:13,905 - vibe_check_actor_initializer - INFO - Actor file_actor_1 state changed: failed -> stopped
2025-05-11 16:45:13,905 - vibe_check_actor_initializer - ERROR - Invalid state transition for actor file_actor_2: failed -> stopping
2025-05-11 16:45:13,905 - vibe_check_actor_initializer - DEBUG - Valid transitions from failed are: ['stopped', 'rollback', 'created']
2025-05-11 16:45:13,905 - vibe_check_actor_system.terminator - INFO - Actor file_actor_2 is already stopped
2025-05-11 16:45:13,905 - vibe_check_actor_initializer - INFO - Actor file_actor_2 state changed: failed -> stopped
2025-05-11 16:45:13,905 - vibe_check_actor_initializer - ERROR - Invalid state transition for actor file_actor_3: failed -> stopping
2025-05-11 16:45:13,905 - vibe_check_actor_initializer - DEBUG - Valid transitions from failed are: ['stopped', 'rollback', 'created']
2025-05-11 16:45:13,905 - vibe_check_actor_system.terminator - INFO - Actor file_actor_3 is already stopped
2025-05-11 16:45:13,905 - vibe_check_actor_initializer - INFO - Actor file_actor_3 state changed: failed -> stopped
2025-05-11 16:45:13,905 - vibe_check_actor_initializer - ERROR - Invalid state transition for actor file_actor_4: failed -> stopping
2025-05-11 16:45:13,905 - vibe_check_actor_initializer - DEBUG - Valid transitions from failed are: ['stopped', 'rollback', 'created']
2025-05-11 16:45:13,905 - vibe_check_actor_system.terminator - INFO - Actor file_actor_4 is already stopped
2025-05-11 16:45:13,905 - vibe_check_actor_initializer - INFO - Actor file_actor_4 state changed: failed -> stopped
2025-05-11 16:45:13,905 - vibe_check_actor_initializer - ERROR - Invalid state transition for actor file_actor_5: failed -> stopping
2025-05-11 16:45:13,905 - vibe_check_actor_initializer - DEBUG - Valid transitions from failed are: ['stopped', 'rollback', 'created']
2025-05-11 16:45:13,906 - vibe_check_actor_system.terminator - INFO - Actor file_actor_5 is already stopped
2025-05-11 16:45:13,906 - vibe_check_actor_initializer - INFO - Actor file_actor_5 state changed: failed -> stopped
2025-05-11 16:45:13,906 - vibe_check_actor_initializer - ERROR - Invalid state transition for actor file_actor_6: failed -> stopping
2025-05-11 16:45:13,906 - vibe_check_actor_initializer - DEBUG - Valid transitions from failed are: ['stopped', 'rollback', 'created']
2025-05-11 16:45:13,906 - vibe_check_actor_system.terminator - INFO - Actor file_actor_6 is already stopped
2025-05-11 16:45:13,906 - vibe_check_actor_initializer - INFO - Actor file_actor_6 state changed: failed -> stopped
2025-05-11 16:45:13,906 - vibe_check_actor_initializer - ERROR - Invalid state transition for actor file_actor_7: failed -> stopping
2025-05-11 16:45:13,906 - vibe_check_actor_initializer - DEBUG - Valid transitions from failed are: ['stopped', 'rollback', 'created']
2025-05-11 16:45:13,906 - vibe_check_actor_system.terminator - INFO - Actor file_actor_7 is already stopped
2025-05-11 16:45:13,906 - vibe_check_actor_initializer - INFO - Actor file_actor_7 state changed: failed -> stopped
2025-05-11 16:45:13,906 - vibe_check_actor_initializer - ERROR - Invalid state transition for actor file_actor_8: failed -> stopping
2025-05-11 16:45:13,906 - vibe_check_actor_initializer - DEBUG - Valid transitions from failed are: ['stopped', 'rollback', 'created']
2025-05-11 16:45:13,906 - vibe_check_actor_system.terminator - INFO - Actor file_actor_8 is already stopped
2025-05-11 16:45:13,906 - vibe_check_actor_initializer - INFO - Actor file_actor_8 state changed: failed -> stopped
2025-05-11 16:45:13,906 - vibe_check_actor_initializer - ERROR - Invalid state transition for actor file_actor_9: failed -> stopping
2025-05-11 16:45:13,906 - vibe_check_actor_initializer - DEBUG - Valid transitions from failed are: ['stopped', 'rollback', 'created']
2025-05-11 16:45:13,906 - vibe_check_actor_system.terminator - INFO - Actor file_actor_9 is already stopped
2025-05-11 16:45:13,906 - vibe_check_actor_initializer - INFO - Actor file_actor_9 state changed: failed -> stopped
2025-05-11 16:45:13,906 - vibe_check_actor_initializer - ERROR - Invalid state transition for actor file_actor_10: failed -> stopping
2025-05-11 16:45:13,906 - vibe_check_actor_initializer - DEBUG - Valid transitions from failed are: ['stopped', 'rollback', 'created']
2025-05-11 16:45:13,906 - vibe_check_actor_system.terminator - INFO - Actor file_actor_10 is already stopped
2025-05-11 16:45:13,906 - vibe_check_actor_initializer - INFO - Actor file_actor_10 state changed: failed -> stopped
2025-05-11 16:45:13,906 - vibe_check_actor_initializer - ERROR - Invalid state transition for actor file_actor_11: failed -> stopping
2025-05-11 16:45:13,906 - vibe_check_actor_initializer - DEBUG - Valid transitions from failed are: ['stopped', 'rollback', 'created']
2025-05-11 16:45:13,906 - vibe_check_actor_system.terminator - INFO - Actor file_actor_11 is already stopped
2025-05-11 16:45:13,906 - vibe_check_actor_initializer - INFO - Actor file_actor_11 state changed: failed -> stopped
2025-05-11 16:45:13,906 - vibe_check_actor_initializer - ERROR - Invalid state transition for actor file_actor_12: failed -> stopping
2025-05-11 16:45:13,906 - vibe_check_actor_initializer - DEBUG - Valid transitions from failed are: ['stopped', 'rollback', 'created']
2025-05-11 16:45:13,906 - vibe_check_actor_system.terminator - INFO - Actor file_actor_12 is already stopped
2025-05-11 16:45:13,906 - vibe_check_actor_initializer - INFO - Actor file_actor_12 state changed: failed -> stopped
2025-05-11 16:45:13,906 - vibe_check_actor_initializer - ERROR - Invalid state transition for actor file_actor_13: failed -> stopping
2025-05-11 16:45:13,906 - vibe_check_actor_initializer - DEBUG - Valid transitions from failed are: ['stopped', 'rollback', 'created']
2025-05-11 16:45:13,906 - vibe_check_actor_system.terminator - INFO - Actor file_actor_13 is already stopped
2025-05-11 16:45:13,906 - vibe_check_actor_initializer - INFO - Actor file_actor_13 state changed: failed -> stopped
2025-05-11 16:45:13,906 - vibe_check_actor_initializer - ERROR - Invalid state transition for actor project_actor: failed -> stopping
2025-05-11 16:45:13,906 - vibe_check_actor_initializer - DEBUG - Valid transitions from failed are: ['stopped', 'rollback', 'created']
2025-05-11 16:45:13,907 - vibe_check_actor_system.terminator - INFO - Actor project_actor is already stopped
2025-05-11 16:45:13,907 - vibe_check_actor_initializer - INFO - Actor project_actor state changed: failed -> stopped
2025-05-11 16:45:13,907 - vibe_check_orchestrator - INFO - All regular actors stopped
2025-05-11 16:45:13,907 - vibe_check_actor_pool - INFO - Actor pool file_actor_pool is already stopped
2025-05-11 16:45:13,907 - vibe_check_orchestrator - INFO - File actor pool stopped
2025-05-11 16:45:13,907 - vibe_check_actor_initializer - ERROR - Invalid state transition for actor supervisor_actor: failed -> stopping
2025-05-11 16:45:13,907 - vibe_check_actor_initializer - DEBUG - Valid transitions from failed are: ['stopped', 'rollback', 'created']
2025-05-11 16:45:13,907 - vibe_check_actor_system.terminator - INFO - Actor supervisor_actor is already stopped
2025-05-11 16:45:13,907 - vibe_check_actor_initializer - INFO - Actor supervisor_actor state changed: failed -> stopped
2025-05-11 16:45:13,907 - vibe_check_orchestrator - INFO - Supervisor actor stopped
2025-05-11 16:45:13,907 - vibe_check_orchestrator - INFO - All actors stopped
2025-05-11 16:45:13,908 - vibe_check_orchestrator - ERROR - Error initializing actor system: No actors were successfully initialized
Traceback (most recent call last):
  File "/Users/<USER>/Lokalne_Kody/PAT_project_analysis/vibe_check/core/orchestrator.py", line 961, in _initialize_actor_system
    await asyncio.wait_for(self.start_actors(), timeout=60.0)
  File "/Users/<USER>/.pyenv/versions/3.13.3/lib/python3.13/asyncio/tasks.py", line 507, in wait_for
    return await fut
           ^^^^^^^^^
  File "/Users/<USER>/Lokalne_Kody/PAT_project_analysis/vibe_check/core/orchestrator.py", line 343, in start_actors
    await self.actor_lifecycle_manager.start_actors()
  File "/Users/<USER>/Lokalne_Kody/PAT_project_analysis/vibe_check/core/orchestration/actor_lifecycle_manager.py", line 72, in start_actors
    initialized_actors = await self._initialize_all_actors(initializer, fail_fast)
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Lokalne_Kody/PAT_project_analysis/vibe_check/core/orchestration/actor_lifecycle_manager.py", line 214, in _initialize_all_actors
    raise RuntimeError(error_msg)
RuntimeError: No actors were successfully initialized

2025-05-11 16:45:13,909 - vibe_check_initialization_tracker - ERROR - Actor system failed: No actors were successfully initialized
2025-05-11 16:45:13,909 - vibe_check_orchestrator - INFO - Set is_running to False
2025-05-11 16:45:13,909 - vibe_check_orchestrator - INFO - Analysis completed with results: {'error': 'No actors were successfully initialized', 'error_details': 'Traceback (most recent call last):\n  File "/Users/<USER>/Lokalne_Kody/PAT_project_analysis/vibe_check/core/orchestrator.py", line 961, in _initialize_actor_system\n    await asyncio.wait_for(self.start_actors(), timeout=60.0)\n  File "/Users/<USER>/.pyenv/versions/3.13.3/lib/python3.13/asyncio/tasks.py", line 507, in wait_for\n    return await fut\n           ^^^^^^^^^\n  File "/Users/<USER>/Lokalne_Kody/PAT_project_analysis/vibe_check/core/orchestrator.py", line 343, in start_actors\n    await self.actor_lifecycle_manager.start_actors()\n  File "/Users/<USER>/Lokalne_Kody/PAT_project_analysis/vibe_check/core/orchestration/actor_lifecycle_manager.py", line 72, in start_actors\n    initialized_actors = await self._initialize_all_actors(initializer, fail_fast)\n                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "/Users/<USER>/Lokalne_Kody/PAT_project_analysis/vibe_check/core/orchestration/actor_lifecycle_manager.py", line 214, in _initialize_all_actors\n    raise RuntimeError(error_msg)\nRuntimeError: No actors were successfully initialized\n'}
2025-05-11 16:45:13,909 - vibe_check_orchestrator - INFO - Closing orchestrator
2025-05-11 16:45:13,909 - vibe_check_orchestrator - INFO - Closing orchestrator
2025-05-11 16:45:13,909 - vibe_check_orchestrator - INFO - Stopping all actors
2025-05-11 16:45:13,909 - vibe_check_actor_initializer - ERROR - Invalid state transition for actor report_actor: stopped -> stopping
2025-05-11 16:45:13,909 - vibe_check_actor_initializer - DEBUG - Valid transitions from stopped are: ['initializing', 'created']
2025-05-11 16:45:13,909 - vibe_check_actor_system.terminator - INFO - Actor report_actor is already stopped
2025-05-11 16:45:13,909 - vibe_check_actor_initializer - DEBUG - Actor report_actor is already in state stopped, no change needed
2025-05-11 16:45:13,909 - vibe_check_actor_initializer - INFO - Actor report_actor state changed: stopped -> stopped
2025-05-11 16:45:13,910 - vibe_check_actor_initializer - ERROR - Invalid state transition for actor visualization_actor: stopped -> stopping
2025-05-11 16:45:13,910 - vibe_check_actor_initializer - DEBUG - Valid transitions from stopped are: ['initializing', 'created']
2025-05-11 16:45:13,910 - vibe_check_actor_system.terminator - INFO - Actor visualization_actor is already stopped
2025-05-11 16:45:13,910 - vibe_check_actor_initializer - DEBUG - Actor visualization_actor is already in state stopped, no change needed
2025-05-11 16:45:13,910 - vibe_check_actor_initializer - INFO - Actor visualization_actor state changed: stopped -> stopped
2025-05-11 16:45:13,910 - vibe_check_actor_initializer - ERROR - Invalid state transition for actor tool_actor: stopped -> stopping
2025-05-11 16:45:13,910 - vibe_check_actor_initializer - DEBUG - Valid transitions from stopped are: ['initializing', 'created']
2025-05-11 16:45:13,910 - vibe_check_actor_system.terminator - INFO - Actor tool_actor is already stopped
2025-05-11 16:45:13,910 - vibe_check_actor_initializer - DEBUG - Actor tool_actor is already in state stopped, no change needed
2025-05-11 16:45:13,910 - vibe_check_actor_initializer - INFO - Actor tool_actor state changed: stopped -> stopped
2025-05-11 16:45:13,910 - vibe_check_actor_initializer - ERROR - Invalid state transition for actor file_actor_0: stopped -> stopping
2025-05-11 16:45:13,910 - vibe_check_actor_initializer - DEBUG - Valid transitions from stopped are: ['initializing', 'created']
2025-05-11 16:45:13,910 - vibe_check_actor_system.terminator - INFO - Actor file_actor_0 is already stopped
2025-05-11 16:45:13,910 - vibe_check_actor_initializer - DEBUG - Actor file_actor_0 is already in state stopped, no change needed
2025-05-11 16:45:13,910 - vibe_check_actor_initializer - INFO - Actor file_actor_0 state changed: stopped -> stopped
2025-05-11 16:45:13,910 - vibe_check_actor_initializer - ERROR - Invalid state transition for actor file_actor_1: stopped -> stopping
2025-05-11 16:45:13,910 - vibe_check_actor_initializer - DEBUG - Valid transitions from stopped are: ['initializing', 'created']
2025-05-11 16:45:13,910 - vibe_check_actor_system.terminator - INFO - Actor file_actor_1 is already stopped
2025-05-11 16:45:13,910 - vibe_check_actor_initializer - DEBUG - Actor file_actor_1 is already in state stopped, no change needed
2025-05-11 16:45:13,910 - vibe_check_actor_initializer - INFO - Actor file_actor_1 state changed: stopped -> stopped
2025-05-11 16:45:13,910 - vibe_check_actor_initializer - ERROR - Invalid state transition for actor file_actor_2: stopped -> stopping
2025-05-11 16:45:13,910 - vibe_check_actor_initializer - DEBUG - Valid transitions from stopped are: ['initializing', 'created']
2025-05-11 16:45:13,910 - vibe_check_actor_system.terminator - INFO - Actor file_actor_2 is already stopped
2025-05-11 16:45:13,910 - vibe_check_actor_initializer - DEBUG - Actor file_actor_2 is already in state stopped, no change needed
2025-05-11 16:45:13,910 - vibe_check_actor_initializer - INFO - Actor file_actor_2 state changed: stopped -> stopped
2025-05-11 16:45:13,910 - vibe_check_actor_initializer - ERROR - Invalid state transition for actor file_actor_3: stopped -> stopping
2025-05-11 16:45:13,910 - vibe_check_actor_initializer - DEBUG - Valid transitions from stopped are: ['initializing', 'created']
2025-05-11 16:45:13,910 - vibe_check_actor_system.terminator - INFO - Actor file_actor_3 is already stopped
2025-05-11 16:45:13,910 - vibe_check_actor_initializer - DEBUG - Actor file_actor_3 is already in state stopped, no change needed
2025-05-11 16:45:13,910 - vibe_check_actor_initializer - INFO - Actor file_actor_3 state changed: stopped -> stopped
2025-05-11 16:45:13,910 - vibe_check_actor_initializer - ERROR - Invalid state transition for actor file_actor_4: stopped -> stopping
2025-05-11 16:45:13,910 - vibe_check_actor_initializer - DEBUG - Valid transitions from stopped are: ['initializing', 'created']
2025-05-11 16:45:13,910 - vibe_check_actor_system.terminator - INFO - Actor file_actor_4 is already stopped
2025-05-11 16:45:13,910 - vibe_check_actor_initializer - DEBUG - Actor file_actor_4 is already in state stopped, no change needed
2025-05-11 16:45:13,910 - vibe_check_actor_initializer - INFO - Actor file_actor_4 state changed: stopped -> stopped
2025-05-11 16:45:13,910 - vibe_check_actor_initializer - ERROR - Invalid state transition for actor file_actor_5: stopped -> stopping
2025-05-11 16:45:13,910 - vibe_check_actor_initializer - DEBUG - Valid transitions from stopped are: ['initializing', 'created']
2025-05-11 16:45:13,910 - vibe_check_actor_system.terminator - INFO - Actor file_actor_5 is already stopped
2025-05-11 16:45:13,910 - vibe_check_actor_initializer - DEBUG - Actor file_actor_5 is already in state stopped, no change needed
2025-05-11 16:45:13,910 - vibe_check_actor_initializer - INFO - Actor file_actor_5 state changed: stopped -> stopped
2025-05-11 16:45:13,911 - vibe_check_actor_initializer - ERROR - Invalid state transition for actor file_actor_6: stopped -> stopping
2025-05-11 16:45:13,911 - vibe_check_actor_initializer - DEBUG - Valid transitions from stopped are: ['initializing', 'created']
2025-05-11 16:45:13,911 - vibe_check_actor_system.terminator - INFO - Actor file_actor_6 is already stopped
2025-05-11 16:45:13,911 - vibe_check_actor_initializer - DEBUG - Actor file_actor_6 is already in state stopped, no change needed
2025-05-11 16:45:13,911 - vibe_check_actor_initializer - INFO - Actor file_actor_6 state changed: stopped -> stopped
2025-05-11 16:45:13,911 - vibe_check_actor_initializer - ERROR - Invalid state transition for actor file_actor_7: stopped -> stopping
2025-05-11 16:45:13,911 - vibe_check_actor_initializer - DEBUG - Valid transitions from stopped are: ['initializing', 'created']
2025-05-11 16:45:13,911 - vibe_check_actor_system.terminator - INFO - Actor file_actor_7 is already stopped
2025-05-11 16:45:13,911 - vibe_check_actor_initializer - DEBUG - Actor file_actor_7 is already in state stopped, no change needed
2025-05-11 16:45:13,911 - vibe_check_actor_initializer - INFO - Actor file_actor_7 state changed: stopped -> stopped
2025-05-11 16:45:13,911 - vibe_check_actor_initializer - ERROR - Invalid state transition for actor file_actor_8: stopped -> stopping
2025-05-11 16:45:13,911 - vibe_check_actor_initializer - DEBUG - Valid transitions from stopped are: ['initializing', 'created']
2025-05-11 16:45:13,911 - vibe_check_actor_system.terminator - INFO - Actor file_actor_8 is already stopped
2025-05-11 16:45:13,911 - vibe_check_actor_initializer - DEBUG - Actor file_actor_8 is already in state stopped, no change needed
2025-05-11 16:45:13,911 - vibe_check_actor_initializer - INFO - Actor file_actor_8 state changed: stopped -> stopped
2025-05-11 16:45:13,911 - vibe_check_actor_initializer - ERROR - Invalid state transition for actor file_actor_9: stopped -> stopping
2025-05-11 16:45:13,911 - vibe_check_actor_initializer - DEBUG - Valid transitions from stopped are: ['initializing', 'created']
2025-05-11 16:45:13,911 - vibe_check_actor_system.terminator - INFO - Actor file_actor_9 is already stopped
2025-05-11 16:45:13,911 - vibe_check_actor_initializer - DEBUG - Actor file_actor_9 is already in state stopped, no change needed
2025-05-11 16:45:13,911 - vibe_check_actor_initializer - INFO - Actor file_actor_9 state changed: stopped -> stopped
2025-05-11 16:45:13,911 - vibe_check_actor_initializer - ERROR - Invalid state transition for actor file_actor_10: stopped -> stopping
2025-05-11 16:45:13,911 - vibe_check_actor_initializer - DEBUG - Valid transitions from stopped are: ['initializing', 'created']
2025-05-11 16:45:13,911 - vibe_check_actor_system.terminator - INFO - Actor file_actor_10 is already stopped
2025-05-11 16:45:13,911 - vibe_check_actor_initializer - DEBUG - Actor file_actor_10 is already in state stopped, no change needed
2025-05-11 16:45:13,911 - vibe_check_actor_initializer - INFO - Actor file_actor_10 state changed: stopped -> stopped
2025-05-11 16:45:13,911 - vibe_check_actor_initializer - ERROR - Invalid state transition for actor file_actor_11: stopped -> stopping
2025-05-11 16:45:13,911 - vibe_check_actor_initializer - DEBUG - Valid transitions from stopped are: ['initializing', 'created']
2025-05-11 16:45:13,911 - vibe_check_actor_system.terminator - INFO - Actor file_actor_11 is already stopped
2025-05-11 16:45:13,911 - vibe_check_actor_initializer - DEBUG - Actor file_actor_11 is already in state stopped, no change needed
2025-05-11 16:45:13,911 - vibe_check_actor_initializer - INFO - Actor file_actor_11 state changed: stopped -> stopped
2025-05-11 16:45:13,911 - vibe_check_actor_initializer - ERROR - Invalid state transition for actor file_actor_12: stopped -> stopping
2025-05-11 16:45:13,911 - vibe_check_actor_initializer - DEBUG - Valid transitions from stopped are: ['initializing', 'created']
2025-05-11 16:45:13,911 - vibe_check_actor_system.terminator - INFO - Actor file_actor_12 is already stopped
2025-05-11 16:45:13,911 - vibe_check_actor_initializer - DEBUG - Actor file_actor_12 is already in state stopped, no change needed
2025-05-11 16:45:13,911 - vibe_check_actor_initializer - INFO - Actor file_actor_12 state changed: stopped -> stopped
2025-05-11 16:45:13,911 - vibe_check_actor_initializer - ERROR - Invalid state transition for actor file_actor_13: stopped -> stopping
2025-05-11 16:45:13,911 - vibe_check_actor_initializer - DEBUG - Valid transitions from stopped are: ['initializing', 'created']
2025-05-11 16:45:13,911 - vibe_check_actor_system.terminator - INFO - Actor file_actor_13 is already stopped
2025-05-11 16:45:13,911 - vibe_check_actor_initializer - DEBUG - Actor file_actor_13 is already in state stopped, no change needed
2025-05-11 16:45:13,911 - vibe_check_actor_initializer - INFO - Actor file_actor_13 state changed: stopped -> stopped
2025-05-11 16:45:13,911 - vibe_check_actor_initializer - ERROR - Invalid state transition for actor project_actor: stopped -> stopping
2025-05-11 16:45:13,911 - vibe_check_actor_initializer - DEBUG - Valid transitions from stopped are: ['initializing', 'created']
2025-05-11 16:45:13,911 - vibe_check_actor_system.terminator - INFO - Actor project_actor is already stopped
2025-05-11 16:45:13,911 - vibe_check_actor_initializer - DEBUG - Actor project_actor is already in state stopped, no change needed
2025-05-11 16:45:13,911 - vibe_check_actor_initializer - INFO - Actor project_actor state changed: stopped -> stopped
2025-05-11 16:45:13,912 - vibe_check_orchestrator - INFO - All regular actors stopped
2025-05-11 16:45:13,912 - vibe_check_actor_pool - INFO - Actor pool file_actor_pool is already stopped
2025-05-11 16:45:13,912 - vibe_check_orchestrator - INFO - File actor pool stopped
2025-05-11 16:45:13,912 - vibe_check_actor_initializer - ERROR - Invalid state transition for actor supervisor_actor: stopped -> stopping
2025-05-11 16:45:13,912 - vibe_check_actor_initializer - DEBUG - Valid transitions from stopped are: ['initializing', 'created']
2025-05-11 16:45:13,912 - vibe_check_actor_system.terminator - INFO - Actor supervisor_actor is already stopped
2025-05-11 16:45:13,912 - vibe_check_actor_initializer - DEBUG - Actor supervisor_actor is already in state stopped, no change needed
2025-05-11 16:45:13,912 - vibe_check_actor_initializer - INFO - Actor supervisor_actor state changed: stopped -> stopped
2025-05-11 16:45:13,912 - vibe_check_orchestrator - INFO - Supervisor actor stopped
2025-05-11 16:45:13,912 - vibe_check_orchestrator - INFO - All actors stopped
2025-05-11 16:45:14,013 - vibe_check_orchestrator - INFO - Orchestrator closed
2025-05-11 16:45:14,014 - vibe_check_orchestrator - INFO - Returning raw results
2025-05-11 16:45:14,015 - vibe_check_orchestrator - INFO - Analysis completed with result: {'error': 'No actors were successfully initialized', 'error_details': 'Traceback (most recent call last):\n  File "/Users/<USER>/Lokalne_Kody/PAT_project_analysis/vibe_check/core/orchestrator.py", line 961, in _initialize_actor_system\n    await asyncio.wait_for(self.start_actors(), timeout=60.0)\n  File "/Users/<USER>/.pyenv/versions/3.13.3/lib/python3.13/asyncio/tasks.py", line 507, in wait_for\n    return await fut\n           ^^^^^^^^^\n  File "/Users/<USER>/Lokalne_Kody/PAT_project_analysis/vibe_check/core/orchestrator.py", line 343, in start_actors\n    await self.actor_lifecycle_manager.start_actors()\n  File "/Users/<USER>/Lokalne_Kody/PAT_project_analysis/vibe_check/core/orchestration/actor_lifecycle_manager.py", line 72, in start_actors\n    initialized_actors = await self._initialize_all_actors(initializer, fail_fast)\n                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "/Users/<USER>/Lokalne_Kody/PAT_project_analysis/vibe_check/core/orchestration/actor_lifecycle_manager.py", line 214, in _initialize_all_actors\n    raise RuntimeError(error_msg)\nRuntimeError: No actors were successfully initialized\n'}
2025-05-11 16:45:14,015 - vibe_check_cli.analyze_command - INFO - Analysis completed in 16.55 seconds
2025-05-11 16:45:14,015 - vibe_check_cli.analyze_command - DEBUG - analyze_project returned: {'error': 'No actors were successfully initialized', 'error_details': 'Traceback (most recent call last):\n  File "/Users/<USER>/Lokalne_Kody/PAT_project_analysis/vibe_check/core/orchestrator.py", line 961, in _initialize_actor_system\n    await asyncio.wait_for(self.start_actors(), timeout=60.0)\n  File "/Users/<USER>/.pyenv/versions/3.13.3/lib/python3.13/asyncio/tasks.py", line 507, in wait_for\n    return await fut\n           ^^^^^^^^^\n  File "/Users/<USER>/Lokalne_Kody/PAT_project_analysis/vibe_check/core/orchestrator.py", line 343, in start_actors\n    await self.actor_lifecycle_manager.start_actors()\n  File "/Users/<USER>/Lokalne_Kody/PAT_project_analysis/vibe_check/core/orchestration/actor_lifecycle_manager.py", line 72, in start_actors\n    initialized_actors = await self._initialize_all_actors(initializer, fail_fast)\n                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "/Users/<USER>/Lokalne_Kody/PAT_project_analysis/vibe_check/core/orchestration/actor_lifecycle_manager.py", line 214, in _initialize_all_actors\n    raise RuntimeError(error_msg)\nRuntimeError: No actors were successfully initialized\n'}
=== Analysis Error ===
Error: No actors were successfully initialized

Error Details:
Traceback (most recent call last):
  File "/Users/<USER>/Lokalne_Kody/PAT_project_analysis/vibe_check/core/orchestrator.py", line 961, in _initialize_actor_system
    await asyncio.wait_for(self.start_actors(), timeout=60.0)
  File "/Users/<USER>/.pyenv/versions/3.13.3/lib/python3.13/asyncio/tasks.py", line 507, in wait_for
    return await fut
           ^^^^^^^^^
  File "/Users/<USER>/Lokalne_Kody/PAT_project_analysis/vibe_check/core/orchestrator.py", line 343, in start_actors
    await self.actor_lifecycle_manager.start_actors()
  File "/Users/<USER>/Lokalne_Kody/PAT_project_analysis/vibe_check/core/orchestration/actor_lifecycle_manager.py", line 72, in start_actors
    initialized_actors = await self._initialize_all_actors(initializer, fail_fast)
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Lokalne_Kody/PAT_project_analysis/vibe_check/core/orchestration/actor_lifecycle_manager.py", line 214, in _initialize_all_actors
    raise RuntimeError(error_msg)
RuntimeError: No actors were successfully initialized


Suggestions:
- The actor system failed to initialize. Try the following:
  * Use the --use-simple-analyzer flag to bypass the actor system
  * Run with --debug-actor-system to get more detailed logs
  * Check the log file for more details
2025-05-11 16:45:14,016 - vibe_check_cli.error_handler - ERROR - Analysis failed: No actors were successfully initialized
2025-05-11 16:45:14,016 - vibe_check_cli.error_handler - ERROR - Actor system initialization failed. Recommend using --use-simple-analyzer flag.

Recommendation: Use the --use-simple-analyzer flag to bypass the actor system.
