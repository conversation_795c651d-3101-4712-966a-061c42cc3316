legacy/PAT_project_analysis/core/actor_system/message.py:59: error: Function is missing a return type annotation  [no-untyped-def]
legacy/PAT_project_analysis/core/actor_system/message.py:59: note: Use "-> None" if function does not return a value
legacy/PAT_project_analysis/core/actor_system/message.py:69: error: Argument 1 to "asdict" has incompatible type "Optional[ContextWave]"; expected "DataclassInstance"  [arg-type]
legacy/PAT_project_analysis/core/actor_system/actor.py:41: error: Need type annotation for "mailbox"  [var-annotated]
legacy/PAT_project_analysis/core/actor_system/actor.py:44: error: Need type annotation for "_known_actors" (hint: "_known_actors: Dict[<type>, <type>] = ...")  [var-annotated]
legacy/PAT_project_analysis/core/actor_system/actor.py:45: error: Need type annotation for "_message_handlers" (hint: "_message_handlers: Dict[<type>, <type>] = ...")  [var-annotated]
legacy/PAT_project_analysis/core/actor_system/actor.py:154: error: Item "None" of "Optional[ContextWave]" has no attribute "metadata"  [union-attr]
legacy/PAT_project_analysis/core/actor_system/actor.py:156: error: Item "None" of "Optional[ContextWave]" has no attribute "propagate"  [union-attr]
Found 7 errors in 2 files (checked 5 source files)
