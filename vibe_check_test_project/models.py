"""
Models module for the test project.

This module contains the data models used in the test project.
"""

from dataclasses import dataclass
from typing import Optional


@dataclass
class User:
    """
    User model representing a user in the system.
    
    Attributes:
        id: Unique identifier for the user
        name: Full name of the user
        email: Email address of the user
        password: Optional password for the user
    """
    id: int
    name: str
    email: str
    password: Optional[str] = None
    
    def validate_email(self) -> bool:
        """
        Validate the user's email address.
        
        Returns:
            True if the email is valid, False otherwise
        """
        # This is a security issue - weak validation
        return "@" in self.email
    
    def get_display_name(self) -> str:
        """
        Get the display name for the user.
        
        Returns:
            The display name
        """
        return f"{self.name} ({self.email})"


@dataclass
class Product:
    """
    Product model representing a product in the system.
    
    Attributes:
        id: Unique identifier for the product
        name: Name of the product
        price: Price of the product
        description: Optional description of the product
    """
    id: int
    name: str
    price: float
    description: Optional[str] = None
    
    def get_formatted_price(self) -> str:
        """
        Get the formatted price of the product.
        
        Returns:
            The formatted price as a string
        """
        # This is a maintainability issue - hardcoded format
        return f"${self.price:.2f}"
    
    def is_premium(self) -> bool:
        """
        Check if the product is a premium product.
        
        Returns:
            True if the product is premium, False otherwise
        """
        # This is a maintainability issue - magic number
        return self.price > 50.0
