#!/usr/bin/env python3
"""
Vibe Check - Project Analysis Tool
==========================

This is the main entry point for the Vibe Check tool. It provides a unified interface
to the various commands and functionality of the tool.

The Vibe Check tool implements the CAW (Contextual Adaptive Wave) architecture for
analyzing Python projects, providing insights into code quality, complexity,
security, and other metrics.
"""

import asyncio
import os
import sys
from pathlib import Path


def setup_environment():
    """Set up the environment for running Vibe Check."""
    # Add the current directory to sys.path if not already there
    current_dir = str(Path(__file__).parent.absolute())
    if current_dir not in sys.path:
        sys.path.insert(0, current_dir)

    # Set up environment variables
    os.environ["VIBE_CHECK_ROOT"] = current_dir


def main():
    """Main entry point for the Vibe Check tool."""
    # Set up the environment
    setup_environment()

    # Import the CLI module
    try:
        from vibe_check.cli.main import main as cli_main
    except ImportError as e:
        print(f"Error importing Vibe Check modules: {e}", file=sys.stderr)
        print("\nMake sure you have installed Vibe Check correctly:", file=sys.stderr)
        print("pip install -e .", file=sys.stderr)
        sys.exit(1)

    # Run the CLI
    cli_main()


if __name__ == "__main__":
    # Fix event loop policy for Windows
    if sys.platform == "win32" and hasattr(asyncio, "WindowsSelectorEventLoopPolicy"):
        asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())

    main()
