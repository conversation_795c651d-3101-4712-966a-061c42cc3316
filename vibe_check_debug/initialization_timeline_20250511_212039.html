<!DOCTYPE html>
<html>
<head>
    <title>Enhanced Actor Initialization Timeline</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        h1, h2, h3 {
            color: #333;
            text-align: center;
        }
        .controls {
            margin: 20px auto;
            width: 95%;
            background-color: white;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            padding: 20px;
            display: flex;
            flex-wrap: wrap;
            justify-content: space-between;
        }
        .control-group {
            margin-bottom: 10px;
            margin-right: 20px;
        }
        .timeline-container {
            margin: 20px auto;
            width: 95%;
            overflow-x: auto;
        }
        .timeline {
            position: relative;
            width: 100%;
            height: 50px;
            background-color: white;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            padding: 20px;
        }
        .timeline-header {
            height: 30px;
            border-bottom: 1px solid #ddd;
            position: relative;
        }
        .timeline-row {
            height: 40px;
            border-bottom: 1px solid #eee;
            position: relative;
        }
        .timeline-label {
            position: absolute;
            left: 0;
            top: 0;
            width: 150px;
            height: 100%;
            padding: 10px;
            box-sizing: border-box;
            font-weight: bold;
            border-right: 1px solid #ddd;
            background-color: #f9f9f9;
            z-index: 1;
        }
        .timeline-content {
            position: absolute;
            left: 150px;
            top: 0;
            right: 0;
            height: 100%;
        }
        .timeline-event {
            position: absolute;
            height: 20px;
            top: 10px;
            border-radius: 3px;
            padding: 0 5px;
            box-sizing: border-box;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            font-size: 12px;
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .timeline-event.error {
            background-image: repeating-linear-gradient(45deg, transparent, transparent 5px, rgba(0,0,0,0.1) 5px, rgba(0,0,0,0.1) 10px) !important;
        }
        .timeline-event.critical-path {
            border: 2px solid #FF5722;
            box-shadow: 0 0 5px #FF5722;
        }
        .timeline-event.bottleneck {
            border: 2px solid #F44336;
            box-shadow: 0 0 8px #F44336;
        }
        .timeline-event.filtered-out {
            opacity: 0.3;
        }
        .timeline-event.highlighted {
            transform: scale(1.05);
            z-index: 10;
            box-shadow: 0 0 10px rgba(0,0,0,0.5);
        }
        .timeline-marker {
            position: absolute;
            top: 0;
            bottom: 0;
            width: 1px;
            background-color: #aaa;
        }
        .timeline-marker-label {
            position: absolute;
            top: 5px;
            transform: translateX(-50%);
            font-size: 10px;
            color: #666;
        }
        .dependency-line {
            position: absolute;
            height: 2px;
            background-color: rgba(0,0,0,0.2);
            z-index: 0;
            pointer-events: none;
        }
        .dependency-line.highlighted {
            background-color: #FF9800;
            height: 3px;
            z-index: 5;
        }
        .tooltip {
            position: absolute;
            background-color: #333;
            color: white;
            padding: 10px;
            border-radius: 3px;
            z-index: 100;
            max-width: 400px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
            display: none;
        }
        .legend {
            margin-top: 20px;
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
        }
        .legend-item {
            display: flex;
            align-items: center;
            margin: 0 10px 10px 0;
        }
        .legend-color {
            width: 20px;
            height: 20px;
            margin-right: 5px;
            border-radius: 3px;
        }
        .summary {
            margin: 20px auto;
            width: 95%;
            background-color: white;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            padding: 20px;
        }
        .tabs {
            display: flex;
            margin-bottom: 10px;
            border-bottom: 1px solid #ddd;
        }
        .tab {
            padding: 10px 20px;
            cursor: pointer;
            background-color: #f9f9f9;
            border: 1px solid #ddd;
            border-bottom: none;
            margin-right: 5px;
            border-radius: 5px 5px 0 0;
        }
        .tab.active {
            background-color: white;
            border-bottom: 1px solid white;
            margin-bottom: -1px;
        }
        .tab-content {
            display: none;
        }
        .tab-content.active {
            display: block;
        }
        .chart-container {
            height: 300px;
            margin-bottom: 20px;
        }
        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
            gap: 20px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
        }
        th, td {
            padding: 8px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #f2f2f2;
        }
        tr:hover {
            background-color: #f5f5f5;
        }
        .critical-path-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border: 2px solid #FF5722;
            border-radius: 50%;
            margin-right: 5px;
        }
        .bottleneck-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border: 2px solid #F44336;
            background-color: #F44336;
            border-radius: 50%;
            margin-right: 5px;
        }
        .zoom-controls {
            position: absolute;
            top: 10px;
            right: 10px;
            background-color: white;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            padding: 5px;
            z-index: 10;
        }
        .zoom-button {
            width: 30px;
            height: 30px;
            background-color: #f9f9f9;
            border: 1px solid #ddd;
            border-radius: 3px;
            margin: 2px;
            cursor: pointer;
            font-size: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .zoom-button:hover {
            background-color: #eee;
        }
        .range-slider {
            margin-top: 10px;
            width: 100%;
        }
        .range-values {
            display: flex;
            justify-content: space-between;
            margin-top: 5px;
            font-size: 12px;
            color: #666;
        }
    </style>
</head>
<body>
    <h1>Enhanced Actor Initialization Timeline</h1>

    <div class="controls">
        <div class="control-group">
            <h3>Filters</h3>
            <div>
                <label>
                    <input type="checkbox" id="show-critical-path" checked>
                    Highlight Critical Path
                </label>
            </div>
            <div>
                <label>
                    <input type="checkbox" id="show-bottlenecks" checked>
                    Highlight Bottlenecks
                </label>
            </div>
            <div>
                <label>
                    <input type="checkbox" id="show-dependencies" checked>
                    Show Dependencies
                </label>
            </div>
            <div>
                <label>
                    <input type="checkbox" id="show-errors" checked>
                    Show Errors
                </label>
            </div>
        </div>

        <div class="control-group">
            <h3>Category Filter</h3>
            <div>
                <label>
                    <input type="checkbox" class="category-filter" data-category="all" checked>
                    All Categories
                </label>
            </div>
            
        </div>

        <div class="control-group">
            <h3>Actor Filter</h3>
            <div>
                <label>
                    <input type="checkbox" class="actor-filter" data-actor="all" checked>
                    All Actors
                </label>
            </div>
            <div>
                <input type="text" id="actor-search" placeholder="Search actors...">
            </div>
            <div id="actor-list" style="max-height: 200px; overflow-y: auto; margin-top: 10px;">
                
            </div>
        </div>

        <div class="control-group">
            <h3>Time Range</h3>
            <div>
                <input type="range" id="time-range-start" min="0" max="100" value="0" class="range-slider">
                <input type="range" id="time-range-end" min="0" max="100" value="100" class="range-slider">
                <div class="range-values">
                    <span id="time-range-start-value">0s</span>
                    <span id="time-range-end-value">0.00s</span>
                </div>
            </div>
        </div>
    </div>

    <div class="timeline-container">
        <div class="timeline">
            <div class="zoom-controls">
                <button class="zoom-button" id="zoom-in">+</button>
                <button class="zoom-button" id="zoom-out">-</button>
                <button class="zoom-button" id="zoom-reset">↺</button>
            </div>

            <div class="timeline-header">
                <div class="timeline-label">Time (seconds)</div>
                <div class="timeline-content">

                    <div class="timeline-marker" style="left: 0%;">
                        <div class="timeline-marker-label">0s</div>
                    </div>
                </div>
            </div>

        </div>
    </div>

    <div class="legend">

    </div>

    <div class="summary">
        <div class="tabs">
            <div class="tab active" data-tab="step-stats">Step Statistics</div>
            <div class="tab" data-tab="critical-path">Critical Path Analysis</div>
            <div class="tab" data-tab="bottlenecks">Bottlenecks</div>
            <div class="tab" data-tab="category-stats">Category Statistics</div>
            <div class="tab" data-tab="actor-stats">Actor Statistics</div>
        </div>

        <div class="tab-content active" id="step-stats">
            <h2>Step Statistics</h2>
            <div class="chart-container">
                <canvas id="step-duration-chart"></canvas>
            </div>
            <table>
                <tr>
                    <th>Step</th>
                    <th>Category</th>
                    <th>Count</th>
                    <th>Avg Duration</th>
                    <th>Min Duration</th>
                    <th>Max Duration</th>
                    <th>Total Duration</th>
                </tr>

            </table>
        </div>

        <div class="tab-content" id="critical-path">
            <h2>Critical Path Analysis</h2>
            <p>The critical path represents the sequence of steps that determine the total initialization time. Optimizing these steps will have the greatest impact on overall performance.</p>

            <div class="chart-container">
                <canvas id="critical-path-chart"></canvas>
            </div>

            <h3>Critical Path by Actor</h3>
            <table>
                <tr>
                    <th>Actor</th>
                    <th>Critical Path Steps</th>
                    <th>Total Duration</th>
                    <th>% of Total Time</th>
                </tr>

            </table>
        </div>

        <div class="tab-content" id="bottlenecks">
            <h2>Bottleneck Analysis</h2>
            <p>Bottlenecks are steps that take a disproportionate amount of time during initialization. These are prime candidates for optimization.</p>

            <div class="chart-container">
                <canvas id="bottleneck-chart"></canvas>
            </div>

            <h3>Identified Bottlenecks</h3>
            <table>
                <tr>
                    <th>Actor</th>
                    <th>Step</th>
                    <th>Duration</th>
                    <th>% of Actor's Time</th>
                </tr>

            </table>
        </div>

        <div class="tab-content" id="category-stats">
            <h2>Category Statistics</h2>
            <p>This shows the distribution of time across different categories of initialization steps.</p>

            <div class="chart-container">
                <canvas id="category-chart"></canvas>
            </div>

            <h3>Time by Category</h3>
            <table>
                <tr>
                    <th>Category</th>
                    <th>Steps</th>
                    <th>Count</th>
                    <th>Total Duration</th>
                    <th>Avg Duration</th>
                    <th>% of Total Time</th>
                </tr>

            </table>
        </div>

        <div class="tab-content" id="actor-stats">
            <h2>Actor Statistics</h2>
            <p>This shows the initialization performance by actor.</p>

            <div class="chart-container">
                <canvas id="actor-chart"></canvas>
            </div>

            <h3>Time by Actor</h3>
            <table>
                <tr>
                    <th>Actor</th>
                    <th>Steps</th>
                    <th>Critical Path Steps</th>
                    <th>Bottlenecks</th>
                    <th>Total Duration</th>
                    <th>% of Total Time</th>
                </tr>

            </table>
        </div>
    </div>

    <div class="tooltip" id="tooltip"></div>

    <script>
        // Global variables for timeline data and state
        let zoomLevel = 1;
        let panOffset = 0;
        let timeRangeStart = 0;
        let timeRangeEnd = 100;
        let selectedActor = null;
        let selectedStep = null;
        let selectedCategory = null;
        let showCriticalPath = true;
        let showBottlenecks = true;
        let showDependencies = true;
        let showErrors = true;
        let filteredCategories = [];
        let filteredActors = [];

        // Initialize the page
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize tabs
            initTabs();

            // Initialize filters
            initFilters();

            // Initialize zoom controls
            initZoomControls();

            // Initialize time range sliders
            initTimeRangeSliders();

            // Initialize tooltips
            initTooltips();

            // Initialize charts
            initCharts();

            // Apply initial filters
            applyFilters();
        });

        // Initialize tab switching
        function initTabs() {
            const tabs = document.querySelectorAll('.tab');
            const tabContents = document.querySelectorAll('.tab-content');

            tabs.forEach(tab => {
                tab.addEventListener('click', () => {
                    // Remove active class from all tabs and contents
                    tabs.forEach(t => t.classList.remove('active'));
                    tabContents.forEach(c => c.classList.remove('active'));

                    // Add active class to clicked tab and corresponding content
                    tab.classList.add('active');
                    const tabId = tab.getAttribute('data-tab');
                    document.getElementById(tabId).classList.add('active');
                });
            });
        }

        // Initialize filters
        function initFilters() {
            // Critical path filter
            const criticalPathCheckbox = document.getElementById('show-critical-path');
            criticalPathCheckbox.addEventListener('change', function() {
                showCriticalPath = this.checked;
                applyFilters();
            });

            // Bottlenecks filter
            const bottlenecksCheckbox = document.getElementById('show-bottlenecks');
            bottlenecksCheckbox.addEventListener('change', function() {
                showBottlenecks = this.checked;
                applyFilters();
            });

            // Dependencies filter
            const dependenciesCheckbox = document.getElementById('show-dependencies');
            dependenciesCheckbox.addEventListener('change', function() {
                showDependencies = this.checked;
                applyFilters();
            });

            // Errors filter
            const errorsCheckbox = document.getElementById('show-errors');
            errorsCheckbox.addEventListener('change', function() {
                showErrors = this.checked;
                applyFilters();
            });

            // Category filters
            const categoryFilters = document.querySelectorAll('.category-filter');
            categoryFilters.forEach(filter => {
                filter.addEventListener('change', function() {
                    const category = this.getAttribute('data-category');

                    if (category === 'all') {
                        // If "All Categories" is checked/unchecked, update all category filters
                        const checked = this.checked;
                        categoryFilters.forEach(f => {
                            if (f.getAttribute('data-category') !== 'all') {
                                f.checked = checked;
                            }
                        });
                    } else {
                        // If a specific category is unchecked, uncheck "All Categories"
                        if (!this.checked) {
                            const allCategoriesFilter = document.querySelector('.category-filter[data-category="all"]');
                            allCategoriesFilter.checked = false;
                        }

                        // If all specific categories are checked, check "All Categories"
                        const allChecked = Array.from(categoryFilters)
                            .filter(f => f.getAttribute('data-category') !== 'all')
                            .every(f => f.checked);

                        if (allChecked) {
                            const allCategoriesFilter = document.querySelector('.category-filter[data-category="all"]');
                            allCategoriesFilter.checked = true;
                        }
                    }

                    // Update filtered categories
                    updateFilteredCategories();

                    // Apply filters
                    applyFilters();
                });
            });

            // Actor filters
            const actorFilters = document.querySelectorAll('.actor-filter');
            actorFilters.forEach(filter => {
                filter.addEventListener('change', function() {
                    const actor = this.getAttribute('data-actor');

                    if (actor === 'all') {
                        // If "All Actors" is checked/unchecked, update all actor filters
                        const checked = this.checked;
                        actorFilters.forEach(f => {
                            if (f.getAttribute('data-actor') !== 'all') {
                                f.checked = checked;
                            }
                        });
                    } else {
                        // If a specific actor is unchecked, uncheck "All Actors"
                        if (!this.checked) {
                            const allActorsFilter = document.querySelector('.actor-filter[data-actor="all"]');
                            allActorsFilter.checked = false;
                        }

                        // If all specific actors are checked, check "All Actors"
                        const allChecked = Array.from(actorFilters)
                            .filter(f => f.getAttribute('data-actor') !== 'all')
                            .every(f => f.checked);

                        if (allChecked) {
                            const allActorsFilter = document.querySelector('.actor-filter[data-actor="all"]');
                            allActorsFilter.checked = true;
                        }
                    }

                    // Update filtered actors
                    updateFilteredActors();

                    // Apply filters
                    applyFilters();
                });
            });

            // Actor search
            const actorSearch = document.getElementById('actor-search');
            actorSearch.addEventListener('input', function() {
                const searchTerm = this.value.toLowerCase();
                const actorItems = document.querySelectorAll('#actor-list div');

                actorItems.forEach(item => {
                    const label = item.querySelector('label');
                    const actorId = label.textContent.trim().toLowerCase();

                    if (actorId.includes(searchTerm)) {
                        item.style.display = '';
                    } else {
                        item.style.display = 'none';
                    }
                });
            });
        }

        // Update filtered categories
        function updateFilteredCategories() {
            const categoryFilters = document.querySelectorAll('.category-filter');
            filteredCategories = Array.from(categoryFilters)
                .filter(f => f.getAttribute('data-category') !== 'all' && f.checked)
                .map(f => f.getAttribute('data-category'));
        }

        // Update filtered actors
        function updateFilteredActors() {
            const actorFilters = document.querySelectorAll('.actor-filter');
            filteredActors = Array.from(actorFilters)
                .filter(f => f.getAttribute('data-actor') !== 'all' && f.checked)
                .map(f => f.getAttribute('data-actor'));
        }

        // Initialize zoom controls
        function initZoomControls() {
            const zoomIn = document.getElementById('zoom-in');
            const zoomOut = document.getElementById('zoom-out');
            const zoomReset = document.getElementById('zoom-reset');

            zoomIn.addEventListener('click', function() {
                zoomLevel *= 1.2;
                applyZoom();
            });

            zoomOut.addEventListener('click', function() {
                zoomLevel /= 1.2;
                if (zoomLevel < 0.1) zoomLevel = 0.1;
                applyZoom();
            });

            zoomReset.addEventListener('click', function() {
                zoomLevel = 1;
                panOffset = 0;
                applyZoom();
            });
        }

        // Apply zoom to timeline
        function applyZoom() {
            const timelineContent = document.querySelectorAll('.timeline-content');

            timelineContent.forEach(content => {
                content.style.transform = `scale(${zoomLevel}) translateX(${panOffset}px)`;
                content.style.transformOrigin = 'left center';
            });
        }

        // Initialize time range sliders
        function initTimeRangeSliders() {
            const startSlider = document.getElementById('time-range-start');
            const endSlider = document.getElementById('time-range-end');
            const startValue = document.getElementById('time-range-start-value');
            const endValue = document.getElementById('time-range-end-value');

            // Get max time from the timeline
            const maxTime = parseFloat(endValue.textContent);

            startSlider.addEventListener('input', function() {
                timeRangeStart = parseInt(this.value);

                // Ensure start doesn't exceed end
                if (timeRangeStart >= timeRangeEnd) {
                    timeRangeStart = timeRangeEnd - 1;
                    this.value = timeRangeStart;
                }

                // Update display value
                const timeValue = (maxTime * timeRangeStart / 100).toFixed(2);
                startValue.textContent = `${timeValue}s`;

                // Apply filters
                applyFilters();
            });

            endSlider.addEventListener('input', function() {
                timeRangeEnd = parseInt(this.value);

                // Ensure end doesn't go below start
                if (timeRangeEnd <= timeRangeStart) {
                    timeRangeEnd = timeRangeStart + 1;
                    this.value = timeRangeEnd;
                }

                // Update display value
                const timeValue = (maxTime * timeRangeEnd / 100).toFixed(2);
                endValue.textContent = `${timeValue}s`;

                // Apply filters
                applyFilters();
            });
        }

        // Initialize tooltips
        function initTooltips() {
            const events = document.querySelectorAll('.timeline-event');
            const tooltip = document.getElementById('tooltip');

            events.forEach(event => {
                event.addEventListener('mouseover', e => {
                    const tooltipContent = event.getAttribute('data-tooltip');
                    tooltip.innerHTML = tooltipContent;
                    tooltip.style.display = 'block';
                    tooltip.style.left = (e.pageX + 10) + 'px';
                    tooltip.style.top = (e.pageY + 10) + 'px';

                    // Highlight the event
                    event.classList.add('highlighted');

                    // Highlight dependencies if enabled
                    if (showDependencies) {
                        highlightDependencies(event);
                    }
                });

                event.addEventListener('mousemove', e => {
                    tooltip.style.left = (e.pageX + 10) + 'px';
                    tooltip.style.top = (e.pageY + 10) + 'px';
                });

                event.addEventListener('mouseout', () => {
                    tooltip.style.display = 'none';

                    // Remove highlight
                    event.classList.remove('highlighted');

                    // Remove dependency highlights
                    const dependencyLines = document.querySelectorAll('.dependency-line');
                    dependencyLines.forEach(line => {
                        line.classList.remove('highlighted');
                    });

                    // Remove highlights from related events
                    const relatedEvents = document.querySelectorAll('.timeline-event.highlighted');
                    relatedEvents.forEach(relatedEvent => {
                        if (relatedEvent !== event) {
                            relatedEvent.classList.remove('highlighted');
                        }
                    });
                });
            });
        }

        // Highlight dependencies for an event
        function highlightDependencies(event) {
            // TODO: Implement dependency highlighting based on the dependency data
        }

        // Initialize charts
        function initCharts() {
            // Step duration chart
            createStepDurationChart();

            // Critical path chart
            createCriticalPathChart();

            // Bottleneck chart
            createBottleneckChart();

            // Category chart
            createCategoryChart();

            // Actor chart
            createActorChart();
        }

        // Create step duration chart
        function createStepDurationChart() {
            const ctx = document.getElementById('step-duration-chart').getContext('2d');

            // Extract data from the table
            const table = document.querySelector('#step-stats table');
            const rows = Array.from(table.querySelectorAll('tr')).slice(1); // Skip header row

            const labels = [];
            const avgDurations = [];
            const maxDurations = [];
            const totalDurations = [];
            const backgroundColors = [];

            rows.forEach(row => {
                const cells = row.querySelectorAll('td');
                const stepName = cells[0].textContent.replace(/^[●○]/, '').trim(); // Remove indicators
                const category = cells[1].textContent;
                const avgDuration = parseFloat(cells[3].textContent);
                const maxDuration = parseFloat(cells[5].textContent);
                const totalDuration = parseFloat(cells[6].textContent);

                labels.push(stepName);
                avgDurations.push(avgDuration);
                maxDurations.push(maxDuration);
                totalDurations.push(totalDuration);

                // Use category color
                const categoryColors = {
                    'SETUP': 'rgba(66, 133, 244, 0.7)',      // Google Blue
                    'DEPENDENCY': 'rgba(251, 188, 5, 0.7)',  // Google Yellow
                    'CORE': 'rgba(52, 168, 83, 0.7)',        // Google Green
                    'LIFECYCLE': 'rgba(26, 188, 156, 0.7)',  // Turquoise
                    'ERROR': 'rgba(234, 67, 53, 0.7)',       // Google Red
                    'CLEANUP': 'rgba(155, 89, 182, 0.7)',    // Purple
                    'UNKNOWN': 'rgba(127, 140, 141, 0.7)'    // Gray
                };

                backgroundColors.push(categoryColors[category] || 'rgba(127, 140, 141, 0.7)');
            });

            new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: labels,
                    datasets: [
                        {
                            label: 'Average Duration (s)',
                            data: avgDurations,
                            backgroundColor: backgroundColors,
                            borderColor: backgroundColors.map(color => color.replace('0.7', '1')),
                            borderWidth: 1
                        },
                        {
                            label: 'Maximum Duration (s)',
                            data: maxDurations,
                            type: 'line',
                            fill: false,
                            borderColor: 'rgba(255, 99, 132, 1)',
                            borderWidth: 2,
                            pointBackgroundColor: 'rgba(255, 99, 132, 1)',
                            pointRadius: 3
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: 'Duration (seconds)'
                            }
                        },
                        x: {
                            title: {
                                display: true,
                                text: 'Initialization Steps'
                            }
                        }
                    },
                    plugins: {
                        title: {
                            display: true,
                            text: 'Step Duration Analysis'
                        },
                        tooltip: {
                            callbacks: {
                                afterLabel: function(context) {
                                    const index = context.dataIndex;
                                    return `Total Duration: ${totalDurations[index].toFixed(2)}s`;
                                }
                            }
                        }
                    }
                }
            });
        }

        // Create critical path chart
        function createCriticalPathChart() {
            const ctx = document.getElementById('critical-path-chart').getContext('2d');

            // Extract data from the table
            const table = document.querySelector('#critical-path table');
            const rows = Array.from(table.querySelectorAll('tr')).slice(1); // Skip header row

            const labels = [];
            const durations = [];
            const percentages = [];

            rows.forEach(row => {
                const cells = row.querySelectorAll('td');
                const actorId = cells[0].textContent;
                const duration = parseFloat(cells[2].textContent);
                const percentage = parseFloat(cells[3].textContent);

                labels.push(actorId);
                durations.push(duration);
                percentages.push(percentage);
            });

            new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: labels,
                    datasets: [
                        {
                            label: 'Duration (s)',
                            data: durations,
                            backgroundColor: 'rgba(255, 87, 34, 0.7)',
                            borderColor: 'rgba(255, 87, 34, 1)',
                            borderWidth: 1
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: 'Duration (seconds)'
                            }
                        },
                        x: {
                            title: {
                                display: true,
                                text: 'Actors on Critical Path'
                            }
                        }
                    },
                    plugins: {
                        title: {
                            display: true,
                            text: 'Critical Path Analysis'
                        },
                        tooltip: {
                            callbacks: {
                                afterLabel: function(context) {
                                    const index = context.dataIndex;
                                    return `Percentage of Total Time: ${percentages[index].toFixed(1)}%`;
                                }
                            }
                        }
                    }
                }
            });
        }

        // Create bottleneck chart
        function createBottleneckChart() {
            const ctx = document.getElementById('bottleneck-chart').getContext('2d');

            // Extract data from the table
            const table = document.querySelector('#bottlenecks table');
            const rows = Array.from(table.querySelectorAll('tr')).slice(1); // Skip header row

            const labels = [];
            const durations = [];
            const percentages = [];

            rows.forEach(row => {
                const cells = row.querySelectorAll('td');
                const actorId = cells[0].textContent;
                const step = cells[1].textContent;
                const duration = parseFloat(cells[2].textContent);
                const percentage = parseFloat(cells[3].textContent);

                labels.push(`${actorId} - ${step}`);
                durations.push(duration);
                percentages.push(percentage);
            });

            new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: labels,
                    datasets: [
                        {
                            label: 'Duration (s)',
                            data: durations,
                            backgroundColor: 'rgba(244, 67, 54, 0.7)',
                            borderColor: 'rgba(244, 67, 54, 1)',
                            borderWidth: 1
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: 'Duration (seconds)'
                            }
                        },
                        x: {
                            title: {
                                display: true,
                                text: 'Bottlenecks'
                            }
                        }
                    },
                    plugins: {
                        title: {
                            display: true,
                            text: 'Bottleneck Analysis'
                        },
                        tooltip: {
                            callbacks: {
                                afterLabel: function(context) {
                                    const index = context.dataIndex;
                                    return `Percentage of Actor's Time: ${percentages[index].toFixed(1)}%`;
                                }
                            }
                        }
                    }
                }
            });
        }

        // Create category chart
        function createCategoryChart() {
            const ctx = document.getElementById('category-chart').getContext('2d');

            // Extract data from the table
            const table = document.querySelector('#category-stats table');
            const rows = Array.from(table.querySelectorAll('tr')).slice(1); // Skip header row

            const labels = [];
            const durations = [];
            const percentages = [];
            const backgroundColors = [];

            // Category colors
            const categoryColors = {
                'SETUP': 'rgba(66, 133, 244, 0.7)',      // Google Blue
                'DEPENDENCY': 'rgba(251, 188, 5, 0.7)',  // Google Yellow
                'CORE': 'rgba(52, 168, 83, 0.7)',        // Google Green
                'LIFECYCLE': 'rgba(26, 188, 156, 0.7)',  // Turquoise
                'ERROR': 'rgba(234, 67, 53, 0.7)',       // Google Red
                'CLEANUP': 'rgba(155, 89, 182, 0.7)',    // Purple
                'UNKNOWN': 'rgba(127, 140, 141, 0.7)'    // Gray
            };

            rows.forEach(row => {
                const cells = row.querySelectorAll('td');
                const category = cells[0].textContent;
                const duration = parseFloat(cells[3].textContent);
                const percentage = parseFloat(cells[5].textContent);

                labels.push(category);
                durations.push(duration);
                percentages.push(percentage);
                backgroundColors.push(categoryColors[category] || 'rgba(127, 140, 141, 0.7)');
            });

            new Chart(ctx, {
                type: 'pie',
                data: {
                    labels: labels,
                    datasets: [
                        {
                            data: durations,
                            backgroundColor: backgroundColors,
                            borderColor: backgroundColors.map(color => color.replace('0.7', '1')),
                            borderWidth: 1
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            display: true,
                            text: 'Time Distribution by Category'
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const index = context.dataIndex;
                                    const value = context.raw;
                                    const percentage = percentages[index];
                                    return `${context.label}: ${value.toFixed(2)}s (${percentage.toFixed(1)}%)`;
                                }
                            }
                        }
                    }
                }
            });
        }

        // Create actor chart
        function createActorChart() {
            const ctx = document.getElementById('actor-chart').getContext('2d');

            // Extract data from the table
            const table = document.querySelector('#actor-stats table');
            const rows = Array.from(table.querySelectorAll('tr')).slice(1); // Skip header row

            const labels = [];
            const durations = [];
            const percentages = [];
            const backgroundColors = [];

            rows.forEach((row, index) => {
                const cells = row.querySelectorAll('td');
                const actorId = cells[0].textContent;
                const duration = parseFloat(cells[4].textContent);
                const percentage = parseFloat(cells[5].textContent);

                labels.push(actorId);
                durations.push(duration);
                percentages.push(percentage);

                // Generate a color based on index
                const hue = (index * 137.5) % 360;
                backgroundColors.push(`hsla(${hue}, 70%, 60%, 0.7)`);
            });

            new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: labels,
                    datasets: [
                        {
                            label: 'Duration (s)',
                            data: durations,
                            backgroundColor: backgroundColors,
                            borderColor: backgroundColors.map(color => color.replace('0.7', '1')),
                            borderWidth: 1
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: 'Duration (seconds)'
                            }
                        },
                        x: {
                            title: {
                                display: true,
                                text: 'Actors'
                            }
                        }
                    },
                    plugins: {
                        title: {
                            display: true,
                            text: 'Time by Actor'
                        },
                        tooltip: {
                            callbacks: {
                                afterLabel: function(context) {
                                    const index = context.dataIndex;
                                    return `Percentage of Total Time: ${percentages[index].toFixed(1)}%`;
                                }
                            }
                        }
                    }
                }
            });
        }

        // Apply filters to timeline events
        function applyFilters() {
            const events = document.querySelectorAll('.timeline-event');
            const maxTime = parseFloat(document.getElementById('time-range-end-value').textContent);

            // Update filtered categories and actors
            updateFilteredCategories();
            updateFilteredActors();

            events.forEach(event => {
                const actor = event.getAttribute('data-actor');
                const category = event.getAttribute('data-category');
                const start = parseFloat(event.getAttribute('data-start'));
                const end = parseFloat(event.getAttribute('data-end'));
                const onCriticalPath = event.getAttribute('data-critical-path') === 'true';
                const isBottleneck = event.getAttribute('data-bottleneck') === 'true';
                const hasError = event.getAttribute('data-error') === 'true';

                // Calculate time range in seconds
                const timeStart = (maxTime * timeRangeStart / 100);
                const timeEnd = (maxTime * timeRangeEnd / 100);

                // Check if event is within time range
                const inTimeRange = (start >= timeStart && start <= timeEnd) ||
                                   (end >= timeStart && end <= timeEnd) ||
                                   (start <= timeStart && end >= timeEnd);

                // Check if event matches category filter
                const matchesCategory = filteredCategories.length === 0 || filteredCategories.includes(category);

                // Check if event matches actor filter
                const matchesActor = filteredActors.length === 0 || filteredActors.includes(actor);

                // Check if event matches critical path filter
                const matchesCriticalPath = !onCriticalPath || showCriticalPath;

                // Check if event matches bottleneck filter
                const matchesBottleneck = !isBottleneck || showBottlenecks;

                // Check if event matches error filter
                const matchesError = !hasError || showErrors;

                // Apply filter
                if (inTimeRange && matchesCategory && matchesActor && matchesCriticalPath && matchesBottleneck && matchesError) {
                    event.classList.remove('filtered-out');
                } else {
                    event.classList.add('filtered-out');
                }
            });
        }
    </script>
</body>
</html>
