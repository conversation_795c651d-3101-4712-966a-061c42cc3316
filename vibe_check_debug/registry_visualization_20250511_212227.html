
        <!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Registry Visualization - 20250511_212227</title>
            <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
            <script src="https://cdn.jsdelivr.net/npm/moment"></script>
            <style>
                body {
                    font-family: Arial, sans-serif;
                    margin: 0;
                    padding: 20px;
                    background-color: #f5f5f5;
                }
                .container {
                    max-width: 1200px;
                    margin: 0 auto;
                    background-color: white;
                    padding: 20px;
                    border-radius: 5px;
                    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
                }
                h1, h2, h3 {
                    color: #333;
                }
                .section {
                    margin-bottom: 30px;
                    padding: 20px;
                    background-color: #f9f9f9;
                    border-radius: 5px;
                }
                .chart-container {
                    height: 300px;
                    margin-bottom: 20px;
                }
                .grid {
                    display: grid;
                    grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
                    gap: 20px;
                }
                table {
                    width: 100%;
                    border-collapse: collapse;
                    margin-bottom: 20px;
                }
                th, td {
                    padding: 8px 12px;
                    text-align: left;
                    border-bottom: 1px solid #ddd;
                }
                th {
                    background-color: #f2f2f2;
                }
                tr:hover {
                    background-color: #f5f5f5;
                }
                .inconsistency {
                    background-color: #fff0f0;
                }
                .resolved {
                    background-color: #f0fff0;
                }
                .warning {
                    color: #856404;
                    background-color: #fff3cd;
                    padding: 10px;
                    border-radius: 5px;
                    margin-bottom: 10px;
                }
                .error {
                    color: #721c24;
                    background-color: #f8d7da;
                    padding: 10px;
                    border-radius: 5px;
                    margin-bottom: 10px;
                }
                .success {
                    color: #155724;
                    background-color: #d4edda;
                    padding: 10px;
                    border-radius: 5px;
                    margin-bottom: 10px;
                }
            </style>
        </head>
        <body>
            <div class="container">
                <h1>Registry Visualization</h1>
                <p>Generated on: 2025-05-11 21:22:27</p>

                <div class="section">
                    <h2>Registry Consistency</h2>
                    <div class="success">
                        <strong>Consistency Check:</strong> Passed
                    </div>

                    <h3>Orphaned References</h3>
                    <table>
                        <tr>
                            <th>Type</th>
                            <th>Count</th>
                        </tr>
        
                        <tr>
                            <td>actor_types</td>
                            <td>0</td>
                        </tr>
            
                        <tr>
                            <td>actor_tags</td>
                            <td>0</td>
                        </tr>
            
                        <tr>
                            <td>actor_capabilities</td>
                            <td>0</td>
                        </tr>
            
                        <tr>
                            <td>actor_patterns</td>
                            <td>0</td>
                        </tr>
            
                        <tr>
                            <td>subscriptions</td>
                            <td>0</td>
                        </tr>
            
                        <tr>
                            <td>heartbeats</td>
                            <td>0</td>
                        </tr>
            
                    </table>

                    <h3>Missing References</h3>
                    <table>
                        <tr>
                            <th>Type</th>
                            <th>Count</th>
                        </tr>
        
                        <tr>
                            <td>actor_types</td>
                            <td>0</td>
                        </tr>
            
                        <tr>
                            <td>actor_tags</td>
                            <td>0</td>
                        </tr>
            
                        <tr>
                            <td>actor_capabilities</td>
                            <td>0</td>
                        </tr>
            
                        <tr>
                            <td>actor_patterns</td>
                            <td>0</td>
                        </tr>
            
                        <tr>
                            <td>heartbeats</td>
                            <td>0</td>
                        </tr>
            
                    </table>
                </div>

                <div class="section">
                    <h2>Registry Operations</h2>
                    <div class="grid">
                        <div class="chart-container">
                            <canvas id="operationTypesChart"></canvas>
                        </div>
                        <div class="chart-container">
                            <canvas id="operationDurationsChart"></canvas>
                        </div>
                    </div>

                    <h3>Operation Types</h3>
                    <table>
                        <tr>
                            <th>Operation Type</th>
                            <th>Count</th>
                            <th>Avg Duration (s)</th>
                        </tr>
        
                        <tr>
                            <td>CONSISTENCY_CHECK</td>
                            <td>4</td>
                            <td>0.0000</td>
                        </tr>
            
                    </table>
                </div>

                <div class="section">
                    <h2>Registry State Evolution</h2>
                    <div class="chart-container">
                        <canvas id="stateEvolutionChart"></canvas>
                    </div>
                </div>

                <div class="section">
                    <h2>Lookup Retries</h2>
                    <div class="grid">
                        <div class="chart-container">
                            <canvas id="retryStatsChart"></canvas>
                        </div>
                        <div>
                            <h3>Retry Statistics</h3>
                            <table>
                                <tr>
                                    <th>Metric</th>
                                    <th>Value</th>
                                </tr>
                                <tr>
                                    <td>Total Retries</td>
                                    <td>0</td>
                                </tr>
                                <tr>
                                    <td>Successful Retries</td>
                                    <td>0</td>
                                </tr>
                                <tr>
                                    <td>Failed Retries</td>
                                    <td>0</td>
                                </tr>
                                <tr>
                                    <td>Actors with Retries</td>
                                    <td>0</td>
                                </tr>
                                <tr>
                                    <td>Max Retries per Actor</td>
                                    <td>0</td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    <h3>Actors with Most Retries</h3>
                    <ul>
        
                    </ul>
                </div>

                <div class="section">
                    <h2>Registry Inconsistencies</h2>
                    <div class="grid">
                        <div class="chart-container">
                            <canvas id="inconsistencyStatsChart"></canvas>
                        </div>
                        <div>
                            <h3>Inconsistency Statistics</h3>
                            <table>
                                <tr>
                                    <th>Metric</th>
                                    <th>Value</th>
                                </tr>
                                <tr>
                                    <td>Total Inconsistencies</td>
                                    <td>0</td>
                                </tr>
                                <tr>
                                    <td>Resolved Inconsistencies</td>
                                    <td>0</td>
                                </tr>
                                <tr>
                                    <td>Unresolved Inconsistencies</td>
                                    <td>0</td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    <h3>Inconsistency Types</h3>
                    <table>
                        <tr>
                            <th>Type</th>
                            <th>Count</th>
                        </tr>
        
                    </table>

                    <h3>Inconsistency Details</h3>
                    <table>
                        <tr>
                            <th>Type</th>
                            <th>Message</th>
                            <th>Actor ID</th>
                            <th>Status</th>
                            <th>Resolution Suggestion</th>
                        </tr>
        
                    </table>
                </div>
            </div>

            <script>
                // Operation Types Chart
                const operationTypesCtx = document.getElementById('operationTypesChart').getContext('2d');
                new Chart(operationTypesCtx, {
                    type: 'bar',
                    data: {
                        labels: ['CONSISTENCY_CHECK'],
                        datasets: [{
                            label: 'Operation Count',
                            data: [4],
                            backgroundColor: 'rgba(54, 162, 235, 0.5)',
                            borderColor: 'rgba(54, 162, 235, 1)',
                            borderWidth: 1
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            title: {
                                display: true,
                                text: 'Operation Types'
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true
                            }
                        }
                    }
                });

                // Operation Durations Chart
                const operationDurationsCtx = document.getElementById('operationDurationsChart').getContext('2d');
                new Chart(operationDurationsCtx, {
                    type: 'bar',
                    data: {
                        labels: ['CONSISTENCY_CHECK'],
                        datasets: [{
                            label: 'Average Duration (s)',
                            data: [1.7523765563964844e-05],
                            backgroundColor: 'rgba(255, 99, 132, 0.5)',
                            borderColor: 'rgba(255, 99, 132, 1)',
                            borderWidth: 1
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            title: {
                                display: true,
                                text: 'Average Operation Durations'
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true
                            }
                        }
                    }
                });

                // State Evolution Chart
                const stateEvolutionCtx = document.getElementById('stateEvolutionChart').getContext('2d');
                const actorCountEvolution = [];
                const actorTypeCountEvolution = [];

                new Chart(stateEvolutionCtx, {
                    type: 'line',
                    data: {
                        datasets: [
                            {
                                label: 'Actor Count',
                                data: actorCountEvolution.map(item => ({
                                    x: item.timestamp,
                                    y: item.count
                                })),
                                borderColor: 'rgba(54, 162, 235, 1)',
                                backgroundColor: 'rgba(54, 162, 235, 0.1)',
                                fill: true
                            },
                            {
                                label: 'Actor Type Count',
                                data: actorTypeCountEvolution.map(item => ({
                                    x: item.timestamp,
                                    y: item.count
                                })),
                                borderColor: 'rgba(255, 99, 132, 1)',
                                backgroundColor: 'rgba(255, 99, 132, 0.1)',
                                fill: true
                            }
                        ]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            title: {
                                display: true,
                                text: 'Registry State Evolution'
                            }
                        },
                        scales: {
                            x: {
                                type: 'time',
                                time: {
                                    unit: 'minute'
                                }
                            },
                            y: {
                                beginAtZero: true
                            }
                        }
                    }
                });

                // Retry Stats Chart
                const retryStatsCtx = document.getElementById('retryStatsChart').getContext('2d');
                const retryStats = {'total_retries': 0, 'successful_retries': 0, 'failed_retries': 0, 'actors_with_retries': 0, 'max_retries_per_actor': 0, 'actors_with_most_retries': []};

                new Chart(retryStatsCtx, {
                    type: 'pie',
                    data: {
                        labels: ['Successful Retries', 'Failed Retries', 'Pending Retries'],
                        datasets: [{
                            data: [
                                retryStats.successful_retries || 0,
                                retryStats.failed_retries || 0,
                                (retryStats.total_retries || 0) - (retryStats.successful_retries || 0) - (retryStats.failed_retries || 0)
                            ],
                            backgroundColor: [
                                'rgba(75, 192, 192, 0.5)',
                                'rgba(255, 99, 132, 0.5)',
                                'rgba(255, 205, 86, 0.5)'
                            ],
                            borderColor: [
                                'rgba(75, 192, 192, 1)',
                                'rgba(255, 99, 132, 1)',
                                'rgba(255, 205, 86, 1)'
                            ],
                            borderWidth: 1
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            title: {
                                display: true,
                                text: 'Retry Statistics'
                            }
                        }
                    }
                });

                // Inconsistency Stats Chart
                const inconsistencyStatsCtx = document.getElementById('inconsistencyStatsChart').getContext('2d');
                const inconsistencyStats = {'total_inconsistencies': 0, 'resolved_inconsistencies': 0, 'unresolved_inconsistencies': 0, 'inconsistency_types': {}};

                new Chart(inconsistencyStatsCtx, {
                    type: 'pie',
                    data: {
                        labels: ['Resolved Inconsistencies', 'Unresolved Inconsistencies'],
                        datasets: [{
                            data: [
                                inconsistencyStats.resolved_inconsistencies || 0,
                                inconsistencyStats.unresolved_inconsistencies || 0
                            ],
                            backgroundColor: [
                                'rgba(75, 192, 192, 0.5)',
                                'rgba(255, 99, 132, 0.5)'
                            ],
                            borderColor: [
                                'rgba(75, 192, 192, 1)',
                                'rgba(255, 99, 132, 1)'
                            ],
                            borderWidth: 1
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            title: {
                                display: true,
                                text: 'Inconsistency Statistics'
                            }
                        }
                    }
                });
            </script>
        </body>
        </html>
        