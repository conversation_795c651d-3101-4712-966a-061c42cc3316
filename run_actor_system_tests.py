#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to run the actor system tests.

This script runs the comprehensive test suite for the enhanced actor system,
including tests for the two-phase initialization process, message queuing,
and error handling.
"""

import asyncio
import logging
import os
import sys
import pytest
import argparse
from typing import List, Optional, Union, Any, Dict, Set, Tuple, Callable, TypeVar, Generic, cast, Type, Iterable, Iterator, Mapping, MutableMapping, Sequence, MutableSequence, Container, Collection, Sized, Hashable, AbstractSet, MutableSet, ItemsView, KeysView, ValuesView, ContextManager, Generator, AsyncGenerator, Awaitable, Coroutine, AsyncIterable, AsyncIterator, Reversible, Sized

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger("run_actor_system_tests")


def run_tests(test_files: Optional[List[str]] = None, verbose: bool = False, stop_on_first_failure: bool = False) -> bool:
    """
    Run the actor system tests.

    Args:
        test_files: Optional list of specific test files to run
        verbose: Whether to run tests in verbose mode
        stop_on_first_failure: Whether to stop on the first test failure

    Returns:
        True if all tests passed, False otherwise
    """
    logger.info("Running actor system tests...")

    # Build pytest arguments
    pytest_args = []

    # Add verbosity
    if verbose:
        pytest_args.append("-v")

    # Add stop on first failure
    if stop_on_first_failure:
        pytest_args.append("-x")

    # Add test files
    if test_files:
        pytest_args.extend(test_files)
    else:
        # Run all actor system tests
        pytest_args.extend([
            # Original tests
            "tests/test_actor_system.py",

            # New comprehensive tests
            "tests/test_actor_system_initialization.py",
            "tests/test_actor_system_supervision.py",
            "tests/test_actor_system_messaging.py",
            "tests/test_actor_system_components.py",
            "tests/test_actor_system_lifecycle.py",
            "tests/test_actor_system_registry.py",
            "tests/test_actor_system_initializer.py",
            "tests/test_actor_system_context_wave.py",
            "tests/test_actor_system_message.py"
        ])

    # Run the tests
    result = pytest.main(pytest_args)

    # Check the result
    if result == 0:
        logger.info("All tests passed!")
        return True
    else:
        logger.error(f"Tests failed with code {result}")
        return False


def parse_args() -> argparse.Namespace:
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description="Run actor system tests")
    parser.add_argument(
        "--files", "-f",
        nargs="+",
        help="Specific test files to run"
    )
    parser.add_argument(
        "--verbose", "-v",
        action="store_true",
        help="Run tests in verbose mode"
    )
    parser.add_argument(
        "--stop-on-first-failure", "-x",
        action="store_true",
        help="Stop on the first test failure"
    )
    return parser.parse_args()


def main() -> int:
    """Main entry point."""
    args = parse_args()

    success = run_tests(
        test_files=args.files,
        verbose=args.verbose,
        stop_on_first_failure=args.stop_on_first_failure
    )

    return 0 if success else 1


if __name__ == "__main__":
    sys.exit(main())
