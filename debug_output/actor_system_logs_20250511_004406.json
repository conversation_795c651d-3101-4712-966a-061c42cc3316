{"timestamp": "20250511_004406", "debug_mode": true, "logs": [{"type": "message", "timestamp": 1746917046.0602582, "sender_id": "test_sender", "recipient_id": "test_recipient", "message_type": "TEST", "message_id": "test_message_1", "payload_size": 101}, {"type": "state_transition", "timestamp": 1746917046.0603, "actor_id": "test_actor", "from_state": "CREATED", "to_state": "INITIALIZED", "details": {"initialization_time": 1746917046.06026}}, {"type": "initialization", "timestamp": 1746917046.060321, "actor_id": "test_actor", "phase": "initialization", "status": "completed", "details": {"config": {"test_config": true}}, "error": null}, {"type": "component_state", "timestamp": 1746917046.0603352, "component_name": "test_component", "state": {"status": "running", "uptime": 123.45, "message_count": 42}}, {"type": "timing", "timestamp": 1746917046.0604742, "actor_id": "initializer", "operation": "create_sync_points", "duration": 6.723403930664062e-05}, {"type": "component_state", "timestamp": 1746917046.060492, "component_name": "initializer", "state": {"sync_points": ["registration_complete", "initialization_complete", "start_complete", "ready_complete"], "sync_points_created": true, "creation_time": 6.723403930664062e-05, "timestamp": 1746917046.0604749}}, {"type": "timing", "timestamp": 1746917046.161611, "actor_id": "test_actor", "operation": "test_operation", "duration": 0.10115885734558105}, {"type": "exception", "timestamp": 1746917046.162237, "actor_id": "test_actor", "message": "An error occurred during testing", "exception": "Test exception", "traceback": "Traceback (most recent call last):\n  File \"/Users/<USER>/Lokalne_Kody/PAT_project_analysis/debug_actor_system.py\", line 232, in test_actor_system_logging\n    raise ValueError(\"Test exception\")\nValueError: Test exception\n"}], "timing_data": {"initializer": {"create_sync_points": [6.723403930664062e-05]}, "test_actor": {"test_operation": [0.10115885734558105]}}, "state_transitions": [{"timestamp": 1746917046.060299, "actor_id": "test_actor", "from_state": "CREATED", "to_state": "INITIALIZED", "details": {"initialization_time": 1746917046.06026}}], "message_flow": [{"timestamp": 1746917046.0602582, "sender_id": "test_sender", "recipient_id": "test_recipient", "message_type": "TEST", "message_id": "test_message_1", "payload_size": 101, "context": {"test_context": true}}], "component_states": {"test_component": {"timestamp": 1746917046.0603352, "state": {"status": "running", "uptime": 123.45, "message_count": 42}}, "initializer": {"timestamp": 1746917046.060491, "state": {"sync_points": ["registration_complete", "initialization_complete", "start_complete", "ready_complete"], "sync_points_created": true, "creation_time": 6.723403930664062e-05, "timestamp": 1746917046.0604749}}}, "initialization_events": [{"timestamp": 1746917046.06032, "actor_id": "test_actor", "phase": "initialization", "status": "completed", "details": {"config": {"test_config": true}}, "error": null}]}