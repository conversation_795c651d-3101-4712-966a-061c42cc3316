#!/usr/bin/env python3
"""
Script to analyze the cyclomatic complexity of methods in the Vibe Check project.
"""

import os
import ast
import sys
from pathlib import Path
from typing import Dict, List, Tuple, Any, Optional
from collections import defaultdict

# Configuration
PROJECT_ROOT = Path(".")
PYTHON_EXTENSIONS = [".py"]
EXCLUDE_DIRS = ["venv", "__pycache__", ".git", "legacy", "build", "dist", "egg-info"]


class ComplexityMetrics:
    """Class to store complexity metrics for a method."""
    
    def __init__(self, name: str, path: str, complexity: int, start_line: int, end_line: int, class_name: Optional[str] = None):
        self.name = name
        self.path = path
        self.complexity = complexity
        self.start_line = start_line
        self.end_line = end_line
        self.class_name = class_name
        
    def __repr__(self) -> str:
        class_info = f" in {self.class_name}" if self.class_name else ""
        return f"{self.name}{class_info} (complexity: {self.complexity}) at {self.path}:{self.start_line}-{self.end_line}"


def should_exclude(path: Path) -> bool:
    """Check if a path should be excluded based on exclude patterns."""
    path_str = str(path)
    for pattern in EXCLUDE_DIRS:
        if pattern in path_str:
            return True
    return False


def find_python_files(root_dir: Path) -> List[Path]:
    """Find all Python files in the given directory."""
    python_files = []
    
    for root, dirs, files in os.walk(root_dir):
        # Filter out excluded directories
        dirs[:] = [d for d in dirs if not should_exclude(Path(root) / d)]
        
        for file in files:
            file_path = Path(root) / file
            if file_path.suffix in PYTHON_EXTENSIONS and not should_exclude(file_path):
                python_files.append(file_path)
    
    return python_files


class ComplexityVisitor(ast.NodeVisitor):
    """AST visitor to calculate cyclomatic complexity."""
    
    def __init__(self):
        self.complexity = 1  # Start with 1 (base complexity)
        
    def visit_If(self, node):
        self.complexity += 1
        self.generic_visit(node)
        
    def visit_For(self, node):
        self.complexity += 1
        self.generic_visit(node)
        
    def visit_While(self, node):
        self.complexity += 1
        self.generic_visit(node)
        
    def visit_AsyncFor(self, node):
        self.complexity += 1
        self.generic_visit(node)
        
    def visit_With(self, node):
        self.complexity += 1
        self.generic_visit(node)
        
    def visit_AsyncWith(self, node):
        self.complexity += 1
        self.generic_visit(node)
        
    def visit_Try(self, node):
        self.complexity += len(node.handlers) + len(node.finalbody) + (1 if node.orelse else 0)
        self.generic_visit(node)
        
    def visit_BoolOp(self, node):
        if isinstance(node.op, ast.And) or isinstance(node.op, ast.Or):
            self.complexity += len(node.values) - 1
        self.generic_visit(node)
        
    def visit_BinOp(self, node):
        self.generic_visit(node)
        
    def visit_Lambda(self, node):
        self.complexity += 1
        self.generic_visit(node)


def calculate_complexity(node: ast.AST) -> int:
    """Calculate the cyclomatic complexity of an AST node."""
    visitor = ComplexityVisitor()
    visitor.visit(node)
    return visitor.complexity


def analyze_file_complexity(file_path: Path) -> List[ComplexityMetrics]:
    """
    Analyze a Python file to extract complexity metrics about methods.
    
    Returns:
        List of complexity metrics for methods in the file
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Parse the AST
        tree = ast.parse(content)
        
        # Extract method complexity metrics
        method_metrics = []
        
        # Track current class for methods
        current_class = None
        
        for node in ast.walk(tree):
            if isinstance(node, ast.ClassDef):
                current_class = node.name
                
                # Process methods within the class
                for item in node.body:
                    if isinstance(item, (ast.FunctionDef, ast.AsyncFunctionDef)):
                        complexity = calculate_complexity(item)
                        method_metrics.append(ComplexityMetrics(
                            item.name,
                            str(file_path),
                            complexity,
                            item.lineno,
                            item.end_lineno,
                            current_class
                        ))
                
                current_class = None
            
            elif isinstance(node, (ast.FunctionDef, ast.AsyncFunctionDef)) and node.parent_field != 'body':
                # Only process top-level functions (not methods in classes)
                complexity = calculate_complexity(node)
                method_metrics.append(ComplexityMetrics(
                    node.name,
                    str(file_path),
                    complexity,
                    node.lineno,
                    node.end_lineno
                ))
        
        return method_metrics
    
    except SyntaxError as e:
        print(f"Syntax error in {file_path}: {e}")
        return []
    except Exception as e:
        print(f"Error analyzing {file_path}: {e}")
        return []


def analyze_project_complexity(root_dir: Path) -> List[ComplexityMetrics]:
    """
    Analyze the complexity of methods in the entire project.
    
    Returns:
        List of complexity metrics for all methods
    """
    python_files = find_python_files(root_dir)
    
    all_method_metrics = []
    
    for file_path in python_files:
        try:
            method_metrics = analyze_file_complexity(file_path)
            all_method_metrics.extend(method_metrics)
        except Exception as e:
            print(f"Error analyzing {file_path}: {e}")
    
    return all_method_metrics


def print_top_complex_methods(metrics: List[ComplexityMetrics], count: int = 10) -> None:
    """Print the top N most complex methods."""
    print("\nTop Most Complex Methods")
    print("=======================")
    
    sorted_metrics = sorted(metrics, key=lambda m: m.complexity, reverse=True)
    
    for i, metric in enumerate(sorted_metrics[:count], 1):
        class_info = f" in {metric.class_name}" if metric.class_name else ""
        print(f"{i}. {metric.name}{class_info}: Complexity {metric.complexity}")
        print(f"   Path: {metric.path}")
        print(f"   Lines: {metric.start_line}-{metric.end_line}")
        print()


def print_complexity_summary(metrics: List[ComplexityMetrics]) -> None:
    """Print a summary of the complexity metrics."""
    total_methods = len(metrics)
    avg_complexity = sum(m.complexity for m in metrics) / total_methods if total_methods > 0 else 0
    
    # Count methods by complexity range
    complexity_ranges = {
        "Low (1-5)": 0,
        "Medium (6-10)": 0,
        "High (11-20)": 0,
        "Very High (21-30)": 0,
        "Extreme (31+)": 0
    }
    
    for metric in metrics:
        if metric.complexity <= 5:
            complexity_ranges["Low (1-5)"] += 1
        elif metric.complexity <= 10:
            complexity_ranges["Medium (6-10)"] += 1
        elif metric.complexity <= 20:
            complexity_ranges["High (11-20)"] += 1
        elif metric.complexity <= 30:
            complexity_ranges["Very High (21-30)"] += 1
        else:
            complexity_ranges["Extreme (31+)"] += 1
    
    print("\nComplexity Summary")
    print("=================")
    print(f"Total methods analyzed: {total_methods}")
    print(f"Average cyclomatic complexity: {avg_complexity:.2f}")
    print("\nComplexity Distribution:")
    
    for range_name, count in complexity_ranges.items():
        percentage = (count / total_methods) * 100 if total_methods > 0 else 0
        print(f"  {range_name}: {count} methods ({percentage:.1f}%)")


def main():
    """Main function to run the complexity analysis."""
    print(f"Analyzing project complexity at {PROJECT_ROOT.absolute()}")
    
    method_metrics = analyze_project_complexity(PROJECT_ROOT)
    
    print_complexity_summary(method_metrics)
    print_top_complex_methods(method_metrics, 20)


if __name__ == "__main__":
    main()
