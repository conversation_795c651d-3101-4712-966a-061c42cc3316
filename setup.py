#!/usr/bin/env python3
"""
Setup Script for Vibe Check (Project Analysis Tool)
"""

from pathlib import Path
from setuptools import setup, find_packages

# Read the README file
readme_file = Path(__file__).parent / "README.md"
long_description = readme_file.read_text() if readme_file.exists() else ""

# Define version
__version__ = "1.0.0"

setup(
    name="vibe-check",
    version=__version__,
    description="Vibe Check - A Python code quality and architecture analysis tool",
    long_description=long_description,
    long_description_content_type="text/markdown",
    author="Vibe Check Development Team",
    author_email="<EMAIL>",
    url="https://github.com/example/vibe-check",
    packages=find_packages(include=["vibe_check", "vibe_check.*"]),
    include_package_data=True,
    python_requires=">=3.8",
    install_requires=[
        "click>=8.0.0",
        "pyyaml>=6.0",
        "networkx>=2.6.3",
        "typing-extensions>=4.0.0",
        "rich>=12.0.0"],
    extras_require={
        "dev": [
            "pytest>=7.0.0",
            "black>=22.0.0",
            "mypy>=0.950",
            "ruff>=0.0.128"],
        "web": [
            "streamlit>=1.10.0",
            "altair>=4.2.0",
            "pandas>=1.4.0",
            "graphviz>=0.20",
            "matplotlib>=3.5.0"],
        "tui": [
            "textual>=0.9.0",
            "keyboard>=0.13.5"],
        "full": [
            "streamlit>=1.10.0",
            "altair>=4.2.0",
            "pandas>=1.4.0",
            "graphviz>=0.20",
            "matplotlib>=3.5.0",
            "textual>=0.9.0",
            "keyboard>=0.13.5",
            "pytest>=7.0.0",
            "mypy>=0.950",
            "ruff>=0.0.128",
            "bandit>=1.7.4"],
    },
    entry_points={
        "console_scripts": [
            "vibe-check=vibe_check.cli:main",
            "pat=vibe_check.cli:main",  # Alias for backward compatibility
        ],
    },
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Developers",
        "License :: OSI Approved :: MIT License",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Topic :: Software Development :: Libraries :: Python Modules",
        "Topic :: Software Development :: Quality Assurance"],
    package_data={
        "vibe_check": [
            "config/*.yaml",
            "config/*.json",
            "ui/web/templates/*.html",
            "ui/web/static/css/*.css",
            "ui/web/static/js/*.js"],
    },
    license="MIT",
)
